<!-- 侧边栏 -->
<div class="col-md-3 col-lg-2 sidebar">
    <div class="sidebar-header">
        <img 
            src="{{ url_for('static', filename='images/logo.png') }}" 
            alt="Logo" 
            class="logo" 
            onerror="this.src='{{ url_for('static', filename='images/default_logo.png') }}'"
        >
        <h4>太享查询系统</h4>
        <p class="user-info">
            用户级别: {{ user.user_level }}
            <span class="badge bg-primary">{{ version }}</span>
        </p>
    </div>
    <hr>
    
    <!-- 导航菜单区域 - 使用flex布局并分为主菜单和底部菜单 -->
    <div class="sidebar-content d-flex flex-column flex-grow-1">
        <!-- 主要菜单项 -->
        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link{% if request.path == url_for('main.homepage') %} active{% endif %}" href="javascript:void(0);" onclick="AppNavigation.goToWorkbench()">
                    <i class="bi bi-grid"></i> <span class="nav-text">工作台</span>
                </a>
            </li>
            
            <!-- 日期筛选表单 -->
            <li class="nav-item sidebar-search">
                <form id="dateFilterForm" class="mb-3 mt-2" onsubmit="event.preventDefault(); AppNavigation.filterByDate();">
                    <div class="form-group mb-2">
                        <label for="date" class="form-label">筛选日期</label>
                        <div class="date-input-container">
                            <input type="date" class="form-control form-control-sm date-input" id="date" name="date" value="{{ selected_date or today }}">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-outline-primary btn-sm w-100">
                        <i class="bi bi-search"></i> <span class="d-inline-block">日期筛选</span>
                    </button>
                </form>
            </li>
            
            <!-- 客户名搜索表单 -->
            <li class="nav-item sidebar-search">
                <div class="customer-search-wrap">
                    <form id="customerSearchForm" class="mb-3" onsubmit="event.preventDefault(); AppNavigation.searchCustomer(); return false;">
                        <div class="form-group mb-2">
                            <label for="customerName" class="form-label">客户姓名</label>
                            <input type="text" class="form-control form-control-sm" id="customerName" name="customerName" placeholder="输入客户姓名" autocomplete="off">
                        </div>
                        <button type="submit" class="btn btn-outline-primary btn-sm w-100">
                            <i class="bi bi-search"></i> <span class="d-inline-block">客户搜索</span>
                        </button>
                    </form>
                </div>
            </li>
            
            <li class="nav-item">
                <a class="nav-link{% if request.path == url_for('main.query.overdue_orders') %} active{% endif %}" href="{{ url_for('main.query.overdue_orders') }}">
                    <i class="bi bi-exclamation-circle"></i> <span class="nav-text">逾期订单查询</span>
                </a>
            </li>
            
            {% if user.has_permission('full') %}
            <li class="nav-item">
                <a class="nav-link d-flex align-items-center{% if request.path == url_for('main.summary_view') %} active{% endif %}" href="javascript:void(0);" onclick="AppNavigation.showSummaryView()">
                    <i class="bi bi-graph-up"></i> <span class="nav-text">数据汇总</span>
                </a>
            </li>
            {% endif %}
        </ul>
        
        <!-- 管理员工具 -->
        {% if user.has_permission('admin') %}
        <div class="admin-tools-section mt-2">
            <h6 class="sidebar-heading px-2 text-muted">
                <span class="nav-text">管理员工具</span>
            </h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center" href="/clear_cache" title="清理系统缓存">
                        <i class="bi bi-trash"></i> <span class="nav-text">清理缓存</span>
                    </a>
                </li>
            </ul>
        </div>
        {% endif %}
        
        <!-- 底部菜单项，使用mt-auto推到底部 -->
        <ul class="nav flex-column bottom-menu mt-auto">
            <li class="nav-item">
                <a class="nav-link d-flex align-items-center" href="javascript:void(0);" onclick="AppNavigation.logout()">
                    <i class="bi bi-box-arrow-right"></i> <span class="nav-text">退出登录</span>
                </a>
            </li>
        </ul>
    </div>
    
    <!-- API状态指示器 -->
    <div class="api-status-container">
        <hr>
        <div class="d-flex justify-content-between">
            <span class="text-muted">API状态:</span>
            <span id="apiStatus" onclick="AppNavigation.checkApiStatus()">
                <span class="badge bg-secondary">检测中...</span>
            </span>
        </div>
        <!-- 下载Windows版按钮 -->
        <div class="mt-2">
            <a href="javascript:void(0);" onclick="AppNavigation.downloadWindowsVersion()" class="btn btn-sm btn-outline-primary w-100">
                <i class="bi bi-windows"></i> <span class="d-inline-block">下载Windows版</span>
            </a>
        </div>
    </div>
    
    <!-- 折叠按钮 -->
    <div class="sidebar-footer">
        <button id="collapseToggle" class="btn btn-sm btn-light d-none d-md-block">
            <i class="bi bi-chevron-left"></i>
        </button>
    </div>
</div>

<!-- 展开按钮 (当侧边栏折叠时显示) -->
<div id="expandToggle" class="sidebar-expand-button d-none">
    <button class="btn">
        <i class="bi bi-chevron-right"></i> <span>展开菜单</span>
    </button>
</div>