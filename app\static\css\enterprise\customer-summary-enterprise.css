/*
 * 太享查询系统 - 客户汇总页面企业级样式
 * Enterprise Customer Summary Page Styles
 * Version: 2.0
 * Last Updated: 2024-12-28
 */

/* ===============================================
   设计令牌系统 (Design Tokens)
   ============================================= */

:root {
    /* 主色调 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --primary-light: #dbeafe;
    --primary-dark: #1e40af;
    
    /* 渐变色系 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
    
    /* 背景色系 */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f1f5f9;
    --bg-accent: rgba(59, 130, 246, 0.05);
    
    /* 文字色系 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-accent: #3b82f6;
    
    /* 边框色系 */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 圆角系统 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* 间距系统 */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 动画系统 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===============================================
   基础容器布局
   ============================================= */

.enterprise-customer-summary {
    min-height: 100vh;
    background: var(--gradient-primary);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
}

.enterprise-customer-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

/* ===============================================
   页面标题区域
   ============================================= */

.enterprise-page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.enterprise-page-title {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: none;
}

.enterprise-page-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.enterprise-action-btn {
    background: var(--gradient-primary);
    border: none;
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.enterprise-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.enterprise-action-btn i {
    font-size: 0.875rem;
}

/* ===============================================
   卡片系统
   ============================================= */

.enterprise-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.enterprise-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.enterprise-card-header {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-lg);
    margin: 0;
    border-bottom: none;
}

.enterprise-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.enterprise-card-title i {
    font-size: 1.125rem;
}

.enterprise-card-body {
    padding: var(--spacing-xl);
}

/* ===============================================
   客户信息区域
   ============================================= */

.enterprise-customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.enterprise-info-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.enterprise-info-table th {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-weight: 600;
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
    border-right: none;
    width: 140px;
    text-align: left;
}

.enterprise-info-table td {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
    border-left: none;
}

.enterprise-info-table tr:first-child th {
    border-top-left-radius: var(--radius-md);
}

.enterprise-info-table tr:first-child td {
    border-top-right-radius: var(--radius-md);
}

.enterprise-info-table tr:last-child th {
    border-bottom-left-radius: var(--radius-md);
}

.enterprise-info-table tr:last-child td {
    border-bottom-right-radius: var(--radius-md);
}

/* ===============================================
   统计卡片区域
   ============================================= */

.enterprise-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.enterprise-stat-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.enterprise-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.enterprise-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.enterprise-stat-card.stat-primary::before {
    background: var(--gradient-primary);
}

.enterprise-stat-card.stat-success::before {
    background: var(--gradient-success);
}

.enterprise-stat-card.stat-warning::before {
    background: var(--gradient-warning);
}

.enterprise-stat-card.stat-secondary::before {
    background: var(--gradient-secondary);
}

.enterprise-stat-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.enterprise-stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    font-family: 'Segoe UI', system-ui, sans-serif;
}

/* ===============================================
   图表区域
   ============================================= */

.enterprise-charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.enterprise-chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.enterprise-chart-container:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.enterprise-chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.enterprise-chart-wrapper {
    position: relative;
    height: 250px;
    width: 100%;
}

/* ===============================================
   数据表格系统
   ============================================= */

.enterprise-data-tabs {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.enterprise-tab-header {
    background: var(--gradient-primary);
    padding: 0;
    margin: 0;
    border-bottom: none;
}

.enterprise-tab-nav {
    border-bottom: none;
    margin: 0;
    padding: 0 var(--spacing-lg);
    background: transparent;
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.enterprise-tab-nav::-webkit-scrollbar {
    display: none;
}

.enterprise-tab-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 0;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 500;
    transition: all var(--transition-normal);
    position: relative;
    white-space: nowrap;
    min-width: max-content;
}

.enterprise-tab-nav .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.enterprise-tab-nav .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.15);
    border-bottom: 3px solid white;
}

.enterprise-tab-content {
    padding: var(--spacing-xl);
    background: white;
}

/* 表格容器响应式 */
.table-responsive {
    border-radius: var(--radius-xl);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(226, 232, 240, 0.8);
    overflow: hidden;
    background: white;
    position: relative;
}

.table-responsive::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
}

/* 企业级表格样式 */
.enterprise-data-table {
    width: 100% !important;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.875rem;
    background: white;
    margin-bottom: 0;
    min-width: 700px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.enterprise-data-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.enterprise-data-table thead::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.enterprise-data-table thead th {
    background: transparent;
    color: white;
    font-weight: 700;
    padding: var(--spacing-lg) var(--spacing-md);
    border: none;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
}

.enterprise-data-table thead th::after {
    content: '';
    position: absolute;
    right: 0;
    top: 25%;
    bottom: 25%;
    width: 1px;
    background: rgba(255, 255, 255, 0.2);
}

.enterprise-data-table thead th:last-child::after {
    display: none;
}

.enterprise-data-table thead th:first-child {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    position: sticky;
    left: 0;
    z-index: 11;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.enterprise-data-table tbody tr {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
}

.enterprise-data-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.enterprise-data-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.1) 100%);
    transform: scale(1.001);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.enterprise-data-table tbody tr:hover::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
}

.enterprise-data-table tbody td {
    padding: var(--spacing-lg) var(--spacing-md);
    border: none;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
    font-weight: 500;
    color: var(--text-primary);
    position: relative;
}

.enterprise-data-table tbody td:first-child {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    text-align: center;
    position: sticky;
    left: 0;
    background: inherit;
    z-index: 9;
}

.enterprise-data-table tbody tr:nth-child(even) td:first-child {
    background: rgba(248, 250, 252, 0.5);
}

.enterprise-data-table tbody tr:hover td:first-child {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.1) 100%);
}

/* 响应式控制列样式 */
.dtr-control {
    width: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
    text-align: center !important;
    cursor: pointer;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 0 !important;
}

.dtr-control:before {
    content: '';
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    transition: all var(--transition-normal);
    position: relative;
}

.dtr-control:before {
    content: '+';
    color: white;
    font-family: 'Segoe UI', sans-serif;
}

.dtr-control.parent:before {
    content: '−';
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
}

.dtr-control:hover:before {
    background: rgba(255, 255, 255, 0.3);
    border-color: white;
    transform: scale(1.1);
}

/* 数据折叠功能 */
.cell-content {
    position: relative;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    transition: all var(--transition-normal);
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    display: block;
    word-break: break-all;
}

.cell-content:hover {
    background: var(--bg-accent);
    color: var(--text-accent);
    white-space: normal;
    word-wrap: break-word;
    max-width: none;
    overflow: visible;
}

.cell-content.expanded {
    white-space: normal;
    word-break: break-word;
    max-width: none !important;
    overflow: visible;
    background: var(--bg-accent);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    position: relative;
    z-index: 1000;
    box-shadow: var(--shadow-md);
}

/* 数据类型特定样式 */
.enterprise-data-table td[data-type="amount"] {
    text-align: right;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: 700;
    color: var(--primary-color);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(59, 130, 246, 0.08) 100%);
    position: relative;
    font-size: 0.9rem;
}

.enterprise-data-table td[data-type="amount"]::before {
    content: '';
    position: absolute;
    right: 0;
    top: 10%;
    bottom: 10%;
    width: 2px;
    background: var(--primary-color);
    opacity: 0.3;
}

.enterprise-data-table td[data-type="date"] {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.8rem;
    color: var(--text-secondary);
    background: rgba(248, 250, 252, 0.8);
    text-align: center;
    font-weight: 500;
}

.enterprise-data-table td[data-type="status"] {
    text-align: center;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* 表格列宽优化 */
.enterprise-data-table th[data-column="period"] {
    width: 120px;
    min-width: 120px;
}

.enterprise-data-table th[data-column="amount"] {
    width: 150px;
    min-width: 150px;
}

.enterprise-data-table th[data-column="date"] {
    width: 130px;
    min-width: 130px;
}

.enterprise-data-table th[data-column="status"] {
    width: 100px;
    min-width: 100px;
}

.enterprise-data-table th[data-column="id"] {
    width: 180px;
    min-width: 180px;
}

.enterprise-data-table th[data-column="type"] {
    width: 120px;
    min-width: 120px;
}

.enterprise-data-table th[data-column="count"] {
    width: 80px;
    min-width: 80px;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    text-align: center;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 70px;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease-in-out;
}

.status-badge:hover::before {
    left: 100%;
}

.status-badge.status-normal {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.25) 100%);
    color: #047857;
    border: 1px solid rgba(16, 185, 129, 0.3);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.1);
}

.status-badge.status-normal:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(16, 185, 129, 0.3) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.2);
}

.status-badge.status-overdue {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(239, 68, 68, 0.25) 100%);
    color: #b91c1c;
    border: 1px solid rgba(239, 68, 68, 0.3);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
}

.status-badge.status-overdue:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(239, 68, 68, 0.3) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.2);
}

.status-badge.status-business {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(59, 130, 246, 0.25) 100%);
    color: #1d4ed8;
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.status-badge.status-business:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.3) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

.status-badge.status-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(245, 158, 11, 0.25) 100%);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.3);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.1);
}

.status-badge.status-warning:hover {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(245, 158, 11, 0.3) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.2);
}

/* 表格工具栏 */
.table-toolbar {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    margin: 0;
}

.table-search {
    flex: 1;
    max-width: 300px;
    margin-right: var(--spacing-sm);
}

.table-actions {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.table-action-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-light);
    background: white;
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
}

.table-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ===============================================
   响应式设计
   ============================================= */

@media (max-width: 1200px) {
    .enterprise-charts-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 992px) {
    .enterprise-customer-info {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .enterprise-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .enterprise-charts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .enterprise-page-header {
        padding: var(--spacing-md);
        text-align: center;
    }
    
    .enterprise-page-title {
        font-size: 1.5rem;
    }
    
    .enterprise-page-actions {
        flex-direction: column;
        width: 100%;
        margin-top: var(--spacing-md);
    }
    
    .enterprise-action-btn {
        width: 100%;
        justify-content: center;
    }
    
    .enterprise-card-body {
        padding: var(--spacing-md);
    }
    
    .enterprise-stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .enterprise-chart-wrapper {
        height: 200px;
    }
    
    .enterprise-tab-content {
        padding: var(--spacing-md);
    }
    
    /* 移动端表格优化 */
    .enterprise-tab-nav {
        padding: 0 var(--spacing-sm);
    }
    
    .enterprise-tab-nav .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }
    
    .enterprise-data-table {
        font-size: 0.75rem;
        min-width: 600px;
    }
    
    .enterprise-data-table thead th {
        padding: var(--spacing-sm);
        font-size: 0.75rem;
    }
    
    .enterprise-data-table tbody td {
        padding: var(--spacing-sm);
        max-width: 120px;
        font-size: 0.75rem;
    }
    
    .cell-content {
        max-width: 100%;
        font-size: 0.75rem;
    }
    
    .table-responsive {
        border-radius: var(--radius-sm);
        margin: 0 -var(--spacing-md);
        border-left: none;
        border-right: none;
    }
    
    .status-badge {
        font-size: 0.65rem;
        padding: 0.125rem 0.375rem;
    }
}

@media (max-width: 576px) {
    .enterprise-customer-summary {
        padding: var(--spacing-sm);
    }
    
    .enterprise-page-title {
        font-size: 1.25rem;
    }
    
    .enterprise-stat-value {
        font-size: 1.5rem;
    }
    
    .enterprise-chart-wrapper {
        height: 180px;
    }
    
    /* 超小屏幕表格优化 */
    .enterprise-tab-nav .nav-link {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }
    
    .enterprise-data-table {
        font-size: 0.7rem;
        min-width: 500px;
    }
    
    .enterprise-data-table thead th {
        padding: var(--spacing-xs);
        font-size: 0.7rem;
    }
    
    .enterprise-data-table tbody td {
        padding: var(--spacing-xs);
        max-width: 100px;
        font-size: 0.7rem;
    }
    
    .enterprise-data-table tbody td:first-child {
        width: 40px;
        min-width: 40px;
        max-width: 40px;
    }
    
    .dtr-control {
        width: 40px !important;
        min-width: 40px !important;
        max-width: 40px !important;
    }
    
    .status-badge {
        font-size: 0.6rem;
        padding: 0.1rem 0.25rem;
    }
    
    .enterprise-tab-content {
        padding: var(--spacing-sm);
    }
}

/* ===============================================
   动画和过渡效果
   ============================================= */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.enterprise-card {
    animation: fadeInUp 0.6s ease-out;
}

.enterprise-stat-card {
    animation: slideInRight 0.6s ease-out;
}

.enterprise-stat-card:nth-child(2) {
    animation-delay: 0.1s;
}

.enterprise-stat-card:nth-child(3) {
    animation-delay: 0.2s;
}

.enterprise-stat-card:nth-child(4) {
    animation-delay: 0.3s;
}

/* ===============================================
   打印样式
   ============================================= */

@media print {
    .enterprise-customer-summary {
        background: white !important;
    }
    
    .enterprise-customer-summary::before {
        display: none !important;
    }
    
    .enterprise-page-actions {
        display: none !important;
    }
    
    .enterprise-card,
    .enterprise-stat-card,
    .enterprise-chart-container {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }
    
    .enterprise-tab-nav {
        display: none !important;
    }
    
    .tab-pane {
        display: block !important;
    }
}

/* ===============================================
   无障碍支持
   ============================================= */

.enterprise-customer-summary *:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.enterprise-action-btn:focus,
.enterprise-tab-nav .nav-link:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-light: #666666;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===============================================
   财务流水过滤功能样式
   ============================================= */

/* 过滤通知样式 */
.finance-filter-notification {
    border: 1px solid #b8daff;
    background-color: #d1ecf1;
    color: #0c5460;
    border-radius: var(--radius-md);
    animation: slideInDown 0.3s ease-out;
}

.finance-filter-notification .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* 过滤匹配项高亮 */
.mobile-data-card.filtered-match {
    border: 2px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.05) 0%, rgba(13, 110, 253, 0.1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
    animation: pulseHighlight 0.6s ease-out;
}

.mobile-data-card.filtered-match .card-header {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(13, 110, 253, 0.15) 100%);
}

/* 过滤动画 */
@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulseHighlight {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 6px 16px rgba(13, 110, 253, 0.25);
    }
    100% {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
    }
}

/* 过滤状态下的卡片过渡效果 */
.mobile-data-card {
    transition: all 0.3s ease;
}

.mobile-data-card[style*="display: none"] {
    opacity: 0;
    transform: scale(0.95);
} 