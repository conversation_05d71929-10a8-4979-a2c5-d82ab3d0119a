{"name": "hdsc-query-frontend", "version": "2.0.0", "description": "太享查询系统 - 现代化前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "@element-plus/icons-vue": "^2.3.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.10.5", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}