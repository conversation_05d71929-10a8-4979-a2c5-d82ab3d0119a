<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="太享查询系统 - 企业级金融数据查询展示平台" />
    <meta name="keywords" content="金融数据,查询系统,数据分析,企业管理" />
    <meta name="author" content="太享查询系统开发团队" />
    
    <!-- SEO优化 -->
    <meta property="og:title" content="太享查询系统" />
    <meta property="og:description" content="企业级金融数据查询展示平台" />
    <meta property="og:type" content="website" />
    
    <!-- PWA配置 -->
    <meta name="theme-color" content="#409eff" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="太享查询系统" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- 字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <title>太享查询系统</title>
    
    <!-- 加载样式 -->
    <style>
      /* 初始加载样式 */
      #app {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      /* 加载动画 */
      .initial-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-content {
        text-align: center;
        color: white;
      }
      
      .loading-logo {
        width: 64px;
        height: 64px;
        margin: 0 auto 24px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
      }
      
      .loading-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
        margin-bottom: 32px;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 隐藏加载动画 */
      .initial-loading.hidden {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
      }
    </style>
  </head>
  <body>
    <!-- 初始加载页面 -->
    <div id="initial-loading" class="initial-loading">
      <div class="loading-content">
        <div class="loading-logo">太</div>
        <div class="loading-title">太享查询系统</div>
        <div class="loading-subtitle">正在加载应用...</div>
        <div class="loading-spinner"></div>
      </div>
    </div>
    
    <!-- Vue应用挂载点 -->
    <div id="app"></div>
    
    <!-- 应用脚本 -->
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 隐藏初始加载动画 -->
    <script>
      // 当Vue应用加载完成后隐藏初始加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('initial-loading');
          if (loading) {
            loading.classList.add('hidden');
            setTimeout(function() {
              loading.remove();
            }, 300);
          }
        }, 500);
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('Application error:', e.error);
        const loading = document.getElementById('initial-loading');
        if (loading) {
          loading.innerHTML = `
            <div class="loading-content">
              <div class="loading-logo">!</div>
              <div class="loading-title">加载失败</div>
              <div class="loading-subtitle">请刷新页面重试</div>
              <button onclick="location.reload()" style="
                background: rgba(255,255,255,0.2);
                border: 1px solid rgba(255,255,255,0.3);
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 16px;
              ">刷新页面</button>
            </div>
          `;
        }
      });
    </script>
  </body>
</html>
