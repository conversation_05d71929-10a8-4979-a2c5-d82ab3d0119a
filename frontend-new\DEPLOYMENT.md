# 太享查询系统前端部署指南

本文档详细说明了太享查询系统前端的部署流程和配置。

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户浏览器     │    │   Nginx代理      │    │   Flask后端      │
│                │────│                │────│                │
│   Vue 3 SPA    │    │   静态文件服务    │    │   API服务       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境要求

### 开发环境
- Node.js >= 18.0.0
- npm >= 9.0.0
- Git

### 生产环境
- Docker >= 20.10.0
- Docker Compose >= 2.0.0
- Nginx >= 1.20.0 (可选，如果不使用Docker)

## 部署方式

### 方式一：Docker 部署（推荐）

#### 1. 构建镜像

```bash
# 克隆代码
git clone <repository-url>
cd frontend-new

# 构建Docker镜像
docker build -t hdsc-query-frontend:latest .
```

#### 2. 运行容器

```bash
# 单独运行前端容器
docker run -d \
  --name hdsc-frontend \
  --restart unless-stopped \
  -p 3000:80 \
  hdsc-query-frontend:latest
```

#### 3. 使用 Docker Compose

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    depends_on:
      - backend
    networks:
      - app-network

  backend:
    image: hdsc-query-backend:latest
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
    restart: unless-stopped
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
```

启动服务：

```bash
docker-compose up -d
```

### 方式二：传统部署

#### 1. 构建应用

```bash
# 安装依赖
npm ci --only=production

# 构建生产版本
npm run build
```

#### 2. 配置 Nginx

创建 Nginx 配置文件 `/etc/nginx/sites-available/hdsc-frontend`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/hdsc-frontend/dist;
    index index.html;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

#### 3. 部署文件

```bash
# 复制构建文件到服务器
sudo cp -r dist/* /var/www/hdsc-frontend/

# 启用站点
sudo ln -s /etc/nginx/sites-available/hdsc-frontend /etc/nginx/sites-enabled/

# 重启Nginx
sudo systemctl restart nginx
```

## 自动化部署

### 使用部署脚本

项目提供了自动化部署脚本 `deploy.sh`：

```bash
# 开发环境部署
./deploy.sh development

# 生产环境部署
./deploy.sh production v2.0.0
```

### CI/CD 集成

#### GitHub Actions 示例

创建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy Frontend

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test
    
    - name: Build application
      run: npm run build
    
    - name: Build Docker image
      run: docker build -t hdsc-query-frontend:${{ github.sha }} .
    
    - name: Deploy to production
      if: github.ref == 'refs/heads/main'
      run: |
        # 部署到生产环境的脚本
        ./deploy.sh production ${{ github.sha }}
```

## 环境配置

### 环境变量

创建对应环境的 `.env` 文件：

#### 开发环境 (`.env.development`)
```bash
VITE_APP_TITLE=太享查询系统（开发）
VITE_API_BASE_URL=http://localhost:5000/api
VITE_APP_VERSION=dev
```

#### 生产环境 (`.env.production`)
```bash
VITE_APP_TITLE=太享查询系统
VITE_API_BASE_URL=/api
VITE_APP_VERSION=2.0.0
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_DROP_CONSOLE=true
```

### Nginx 配置优化

#### 性能优化
```nginx
# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_comp_level 6;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# 启用HTTP/2
listen 443 ssl http2;

# 安全头
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
```

#### SSL配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 其他配置...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 监控和维护

### 健康检查

应用提供了健康检查端点：

```bash
# 检查应用状态
curl http://localhost:3000/health
```

### 日志管理

#### Docker 日志
```bash
# 查看容器日志
docker logs hdsc-frontend

# 实时查看日志
docker logs -f hdsc-frontend
```

#### Nginx 日志
```bash
# 访问日志
tail -f /var/log/nginx/access.log

# 错误日志
tail -f /var/log/nginx/error.log
```

### 性能监控

#### 关键指标
- 页面加载时间
- 资源加载时间
- API响应时间
- 错误率

#### 监控工具
- Google Analytics
- Sentry (错误监控)
- New Relic (性能监控)

## 故障排除

### 常见问题

#### 1. 页面空白
- 检查控制台错误
- 确认API服务是否正常
- 检查Nginx配置

#### 2. API请求失败
- 检查代理配置
- 确认后端服务状态
- 检查CORS设置

#### 3. 静态资源404
- 检查文件路径
- 确认Nginx配置
- 检查缓存设置

### 调试命令

```bash
# 检查容器状态
docker ps

# 进入容器调试
docker exec -it hdsc-frontend sh

# 检查Nginx配置
nginx -t

# 重新加载Nginx配置
nginx -s reload
```

## 备份和恢复

### 备份策略
1. 代码备份：Git仓库
2. 配置备份：配置文件版本控制
3. 镜像备份：Docker镜像仓库

### 恢复流程
1. 拉取最新代码
2. 恢复配置文件
3. 重新构建和部署

## 安全考虑

### 安全措施
- 使用HTTPS
- 设置安全头
- 定期更新依赖
- 最小权限原则
- 定期安全扫描

### 访问控制
- 网络层面的访问控制
- 应用层面的权限验证
- API接口的安全防护
