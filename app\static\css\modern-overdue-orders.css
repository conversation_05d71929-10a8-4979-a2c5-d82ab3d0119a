/**
 * 现代化逾期订单页面样式
 * 采用现代设计语言，支持完全响应式布局
 */

/* ==================== CSS变量定义 ==================== */
:root {
    /* 主色调 */
    --primary-color: #007bff;
    --primary-hover: #0056b3;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    
    /* 中性色 */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #495057;
    --text-color: #212529;
    --text-muted: #6c757d;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* 圆角 */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
    
    /* 过渡 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ==================== 基础布局 ==================== */
.modern-overdue-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.main-content {
    margin-left: 250px;
    padding: var(--spacing-xl);
    transition: margin-left var(--transition-normal);
}

/* ==================== 页面头部 ==================== */
.page-header {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.header-left {
    flex: 1;
    min-width: 200px;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 var(--spacing-sm) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.page-title i {
    color: var(--warning-color);
    font-size: 1.8rem;
}

.page-subtitle {
    color: var(--text-muted);
    margin: 0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* ==================== 操作按钮 ==================== */
.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-fast);
    cursor: pointer;
    font-size: 0.9rem;
    white-space: nowrap;
}

.refresh-btn {
    background: var(--primary-color);
    color: var(--white);
}

.refresh-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.export-btn {
    background: var(--success-color);
    color: var(--white);
    position: relative;
}

.export-btn:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* ==================== 导出下拉菜单 ==================== */
.export-dropdown {
    position: relative;
}

.export-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-sm);
    min-width: 150px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.export-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.export-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    background: none;
    text-align: left;
    border-radius: var(--border-radius-sm);
    transition: background var(--transition-fast);
    cursor: pointer;
}

.export-option:hover {
    background: var(--light-gray);
}

/* ==================== 状态卡片 ==================== */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.status-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.status-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.info-card .card-icon {
    background: linear-gradient(135deg, var(--info-color), #138496);
}

.count-card .card-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.filter-card .card-icon {
    background: linear-gradient(135deg, var(--secondary-color), #545b62);
}

.card-content {
    flex: 1;
}

.card-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.card-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

/* ==================== 加载指示器 ==================== */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--medium-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-color);
    font-weight: 500;
}

/* ==================== 搜索控制区 ==================== */
.search-controls {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.search-section {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    flex: 1;
    min-width: 300px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-muted);
    z-index: 2;
}

.search-input {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 2.5rem;
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-md);
    margin-left: var(--spacing-sm);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.search-btn:hover {
    background: var(--primary-hover);
}

.clear-search-btn {
    position: absolute;
    right: 80px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: color var(--transition-fast);
}

.clear-search-btn:hover {
    color: var(--danger-color);
}

.filter-controls {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.page-size-selector label {
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
}

.page-size-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    background: var(--white);
    cursor: pointer;
    transition: border-color var(--transition-fast);
}

.page-size-select:focus {
    outline: none;
    border-color: var(--primary-color);
}
