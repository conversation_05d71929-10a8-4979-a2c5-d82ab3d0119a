/* 全局样式 */
body {
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* 重置Bootstrap网格布局以适应侧边栏 */
.container-fluid {
    padding: 0;
}

/* 侧边栏样式 */
.sidebar {
    background-color: #343a40;
    color: #fff;
    min-height: 100vh;
    padding: 20px 15px; /* 修改为统一内边距 */
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 250px;
    z-index: 1030;
    overflow-y: auto;
    transition: transform 0.3s ease;
    transform: translateX(0);
    display: flex;
    flex-direction: column;
}

/* 确保所有侧边栏元素自适应宽度 */
.sidebar .nav-item,
.sidebar .sidebar-search,
.sidebar-header,
.api-status-container,
.sidebar-footer {
    width: 100%;
    box-sizing: border-box;
}

/* 按钮和输入框自适应宽度 */
.sidebar .btn,
.sidebar .form-control,
.sidebar .nav-link,
.sidebar-search .form-control,
.sidebar-search .btn {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

/* 主内容区样式 */
.main-content {
    padding: 20px;
    transition: margin 0.3s ease;
    margin-left: 250px;
    width: auto;
}

/* 移动设备上的侧边栏处理 */
@media (max-width: 767.98px) {
    .sidebar {
        transform: translateX(-100%);
        position: fixed;
        height: 100% !important;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        bottom: 0;
        max-height: 100%;
        padding: 20px 15px; /* 统一内边距 */
        width: 250px;
    }
    
    .sidebar-active {
        transform: translateX(0);
        box-shadow: 3px 0 10px rgba(0,0,0,0.2);
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    /* 侧边栏切换按钮 */
    .sidebar-toggle {
        display: block;
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 1040;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        padding: 5px 10px;
    }
    
    /* 遮罩层 */
    .overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1025;
    }
    
    .overlay-active {
        display: block;
    }
    
    /* 移动端键盘弹出固定布局 */
    body.keyboard-visible .sidebar {
        position: absolute !important;
        height: auto !important;
        overflow-y: visible;
    }
    
    body.keyboard-visible .customer-search-wrap {
        position: fixed !important;
        top: 40% !important;
        left: 15px !important;
        right: 15px !important;
        width: calc(100% - 30px) !important;
        background-color: #343a40 !important;
        padding: 15px !important;
        z-index: 1050 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
    }
    
    /* 完全固定搜索表单位置 */
    .mobile-fixed-form {
        position: relative !important;
        width: 100% !important;
        transform: none !important;
        margin-bottom: 15px !important;
    }
    
    /* 修复侧边栏搜索框宽度问题 */
    .sidebar-search {
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
    }
    
    .customer-search-wrap {
        position: relative;
        padding: 0 !important;
        transition: all 0.3s ease;
        width: 100% !important;
    }
    
    /* 修复表单本身和内部元素宽度问题 */
    #customerSearchForm,
    #dateFilterForm {
        position: static !important;
        transform: none !important;
        top: auto !important;
        left: auto !important;
        margin: 0 !important;
        height: auto !important;
        z-index: 1 !important;
        box-sizing: border-box;
        padding: 0 5px !important;           /* 减少内边距 */
        width: 100% !important;
    }
    
    /* 确保表单内元素宽度一致 */
    #customerSearchForm input,
    #customerSearchForm input:focus,
    #customerSearchForm button,
    #dateFilterForm input,
    #dateFilterForm input:focus,
    #dateFilterForm button {
        position: static !important;
        transform: none !important;
        width: 100% !important;
        box-sizing: border-box !important;
        height: 38px !important;
        font-size: 16px !important;
        margin-bottom: 8px !important;
    }
    
    /* 确保所有侧边栏按钮样式一致 */
    .sidebar .btn,
    .sidebar .form-control,
    .sidebar .nav-link,
    .sidebar .btn-outline-primary {
        height: 38px !important;
        font-size: 16px !important;
        padding: 0.375rem 0.75rem !important;
        line-height: 1.5 !important;
        width: calc(100% - 10px) !important; /* 减少侧边距 */
        margin-left: 5px !important;         /* 减少侧边距 */
        margin-right: 5px !important;        /* 减少侧边距 */
        box-sizing: border-box !important;
        text-align: left !important;
        border-radius: 6px !important;
    }
    
    /* 特殊处理逾期订单查询按钮 */
    #overdueButton {
        width: calc(100% - 10px) !important; /* 减少侧边距 */
        margin-left: 5px !important;         /* 减少侧边距 */
        margin-right: 5px !important;        /* 减少侧边距 */
        text-align: left !important;
        justify-content: flex-start !important;
        border-radius: 6px !important;
    }
    
    /* 统一按钮样式和宽度 */
    .sidebar .btn-sm,
    .sidebar-search .btn-sm,
    .sidebar .btn-link,
    .sidebar .nav-link.d-flex {
        height: 38px !important;
        padding: 0.375rem 0.75rem !important;
        font-size: 16px !important;
        line-height: 1.5 !important;
        display: flex !important;
        align-items: center !important;
    }
    
    /* 统一表单组样式 */
    .sidebar .form-group,
    .sidebar-search .form-group {
        margin-bottom: 1rem !important;
    }
    
    /* 确保所有表单标签一致 */
    .sidebar .form-label,
    .sidebar-search .form-label {
        font-size: 16px !important;
        margin-bottom: 0.5rem !important;
        display: block !important;
    }
    
    input[type="text"], 
    input[type="date"], 
    input[type="search"] {
        font-size: 16px !important; /* 防止iOS缩放 */
        height: 38px !important; /* 统一高度 */
    }
    
    /* 增加侧边栏内边距 */
    .sidebar-header {
        padding: 15px 10px !important;       /* 减少内边距 */
    }
    
    .sidebar .nav-item {
        margin-bottom: 8px !important;
    }
    
    .customer-search-wrap {
        margin-bottom: 10px !important;
    }
    
    /* 调整图标和文本的间距 */
    .sidebar .nav-link i,
    .sidebar .btn i {
        margin-right: 10px !important;
    }
    
    /* 确保底部的图标和链接能够完全显示 */
    .api-status-container {
        margin-top: 15px;
        padding: 0 5px !important;
        width: 100% !important;
        box-sizing: border-box;
    }
    
    /* 下载Windows版按钮特殊处理 */
    .api-status-container .btn {
        width: calc(100% - 10px) !important;
        margin-left: 5px !important;
        margin-right: 5px !important;
    }
    
    /* 确保所有表单和按钮使用统一宽度 */
    #customerSearchForm,
    #dateFilterForm,
    #customerSearchForm input,
    #customerSearchForm button,
    #dateFilterForm input,
    #dateFilterForm button,
    .sidebar .btn,
    .sidebar .nav-link,
    #overdueButton,
    .sidebar .btn-outline-primary {
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
    }
    
    /* 确保表单内元素宽度一致 */
    #customerSearchForm input,
    #customerSearchForm input:focus,
    #customerSearchForm button,
    #dateFilterForm input,
    #dateFilterForm input:focus,
    #dateFilterForm button {
        width: 100% !important;
        box-sizing: border-box !important;
        height: 38px !important;
        font-size: 16px !important;
        margin-bottom: 8px !important;
    }
}

/* 移动设备日期选择器样式修复 */
@media (max-width: 767.98px) {
    input[type="date"] {
        position: relative;
        width: 100% !important;
        box-sizing: border-box !important;
        padding-right: 30px !important; /* 确保移动端也有足够空间 */
    }
    
    input[type="date"]::-webkit-calendar-picker-indicator {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px; /* 移动端稍微大一点 */
        height: 24px;
        z-index: 2;
        margin: 0 !important;
        padding: 0 !important;
        opacity: 1;
        pointer-events: auto;
    }
    
    /* 确保日期筛选表单在移动端正确显示 */
    #dateFilterForm .form-group {
        position: relative;
        width: 100%;
    }
    
    /* 确保日期输入框内部的图标完全包含在输入框内 */
    .sidebar-search input[type="date"] {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        border: 1px solid #ced4da;
        border-radius: 4px;
        height: 38px !important;
        font-size: 16px !important;
        background-color: white;
        background-image: none;
    }
    
    /* 针对不同浏览器进行适配 */
    .sidebar-search input[type="date"]::-webkit-datetime-edit {
        padding: 0 8px;
        width: calc(100% - 40px); /* 留出图标空间 */
    }
    
    /* 解决Safari上的问题 */
    @supports (-webkit-touch-callout: none) {
        .sidebar-search input[type="date"] {
            background-position: right 8px center;
            background-repeat: no-repeat;
        }
    }
}

/* 日期选择器容器和图标样式 */
.date-input-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.date-input {
    flex: 1;
    padding-right: 36px; /* 为图标留出空间 */
    width: 100%;
    background-color: white;
}

/* 隐藏原生日期选择器图标 */
input[type="date"]::-webkit-calendar-picker-indicator {
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-clear-button {
    display: none;
}

/* 自定义日历图标 */
.calendar-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    z-index: 1;
    pointer-events: none; /* 允许点击穿透图标到input元素 */
}

/* 移动设备适配 */
@media (max-width: 767.98px) {
    .date-input-container {
        width: 100% !important;
    }
    
    .date-input {
        width: 100% !important;
        height: 38px !important;
        font-size: 16px !important;
        background-color: white;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding-left: 8px;
        padding-right: 36px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }
    
    .calendar-icon {
        font-size: 20px;
        right: 10px;
        color: #495057;
    }
}

/* 侧边栏内容样式 */
.sidebar-header {
    padding: 0 15px 15px;
    text-align: center;
}

.sidebar-header h4 {
    margin-top: 15px;
    font-size: 18px;
}

.logo {
    max-width: 80px;
    margin: 0 auto;
    display: block;
}

.user-info {
    font-size: 14px;
    color: #adb5bd;
    margin-top: 10px;
}

.nav-link {
    color: #adb5bd;
    padding: 10px 15px;
    margin: 2px 0;
    border-radius: 4px;
    transition: all 0.3s;
}

.nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    color: #fff;
    background-color: #007bff;
}

.nav-link i {
    margin-right: 10px;
}

/* 侧边栏搜索表单 */
.sidebar-search {
    padding: 10px 0;
    margin-bottom: 15px;
    width: 100%;
}

.sidebar-search form {
    width: 100%;
    margin-bottom: 10px;
}

.sidebar-search .form-control-sm {
    width: 100%;
    margin-bottom: 10px;
    box-sizing: border-box;
    font-size: 0.875rem;
    border-radius: 4px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.sidebar-search .form-control-sm:focus {
    background-color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    border-color: #80bdff;
}

.sidebar-search .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 4px;
    transition: all 0.2s;
}

.sidebar-search .btn-sm:hover {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.sidebar-search label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

/* 表格样式 */
.table th {
    background-color: #f0f0f0;
    font-weight: 600;
}

.data-table {
    width: 100% !important;
}

/* 表格行状态颜色 - 直接从配置中的状态映射 */
.status-overdue {
    background-color: #FFFFC7CE !important;
}

.status-early {
    background-color: #FFD9E1F2 !important;
}

.status-ontime {
    background-color: #FFC6EFCE !important;
}

.status-upcoming {
    background-color: #FFF4B084 !important;
}

.status-collection {
    background-color: #FFFFEB9C !important;
}

.status-litigation {
    background-color: #FFFFC7CE !important;
}

/* 卡片样式 */
.card {
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 表单元素 */
.form-control:focus {
    border-color: #3f51b5;
    box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
}

.btn-primary {
    background-color: #3f51b5;
    border-color: #3f51b5;
}

.btn-primary:hover {
    background-color: #303f9f;
    border-color: #303f9f;
}

/* 选项卡样式 */
.nav-tabs {
    margin-bottom: 20px;
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
    color: #495057;
    border: none;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #3f51b5;
}

.nav-tabs .nav-link.active {
    color: #3f51b5;
    background-color: transparent;
    border-bottom: 2px solid #3f51b5;
}

/* 图表容器 */
.chart-container {
    height: 400px;
    margin-bottom: 30px;
}

/* 闪现消息样式 */
.flash-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 350px;
}

/* 响应式表格样式增强 */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.data-table {
    width: 100% !important;
    border-collapse: collapse;
}

/* 响应式表格详情样式 */
.dtr-details {
    width: 100%;
    background: #f9f9f9;
    padding: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-bottom: 10px;
}

table.dtr-details {
    width: 100% !important;
    margin-bottom: 0;
}

.dtr-details tr {
    border-bottom: 1px solid #eee;
}

.dtr-details tr:last-child {
    border-bottom: none;
}

.dtr-title {
    padding: 8px;
    font-weight: bold;
    color: #555;
    min-width: 120px;
    vertical-align: top;
}

.dtr-data {
    padding: 8px;
    word-break: break-word;
}

/* 小屏幕设备特定优化 */
@media (max-width: 576px) {
    /* 标题调整 */
    h1.h2 {
        font-size: 1.5rem;
    }
    
    /* 进一步压缩表格 */
    .table th, .table td {
        padding: 0.25rem;
        font-size: 0.75rem;
    }
    
    /* 让图表容器高度更小 */
    .chart-container {
        height: 250px;
    }
    
    /* 简化列表组 */
    .list-group-item {
        padding: 0.5rem 0.75rem;
    }
    
    /* 日期筛选表单响应式调整 */
    .form-group {
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        font-size: 0.875rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* 确保侧边栏在小屏幕上有正确的宽度和内边距 */
    .sidebar {
        width: 85%;
        max-width: 280px;
        padding: 15px 10px;
    }
    
    /* 确保按钮和输入框在小屏幕上正确对齐 */
    .sidebar .btn,
    .sidebar .form-control,
    .sidebar .nav-link,
    .sidebar-search .form-control,
    .sidebar-search .btn,
    #overdueButton,
    .api-status-container .btn {
        padding: 8px 12px;
        font-size: 14px;
        height: auto !important;
        min-height: 38px;
    }
    
    /* 确保图标和文字对齐 */
    .sidebar .btn i,
    .sidebar .nav-link i {
        margin-right: 8px;
        flex-shrink: 0;
    }
    
    /* 确保文本可以换行，防止溢出 */
    .sidebar .btn span,
    .sidebar .nav-link span,
    .sidebar-header h4,
    .sidebar-header p {
        white-space: normal;
        word-break: break-word;
    }
    
    /* 确保表单标签正确显示 */
    .sidebar .form-label {
        font-size: 14px;
        margin-bottom: 4px;
    }
    
    /* 确保折叠按钮在移动设备上正确显示 */
    .sidebar-footer button {
        width: 100%;
    }
}

/* 侧边栏折叠相关样式 */
.sidebar-collapsed {
    width: 60px !important;
    overflow: visible;
}

.sidebar-collapsed .sidebar-header h4,
.sidebar-collapsed .sidebar-header .user-info,
.sidebar-collapsed .nav-text,
.sidebar-collapsed .sidebar-search,
.sidebar-collapsed .api-status-container {
    display: none !important;
}

.sidebar-collapsed .nav-link {
    text-align: center;
    padding: 0.75rem 0;
    justify-content: center !important;
}

/* 处理折叠状态下的侧边栏内容 */
.sidebar-collapsed .sidebar-content {
    align-items: center;
}

.sidebar-collapsed .main-menu,
.sidebar-collapsed .bottom-menu {
    width: 100%;
    padding: 0;
}

.sidebar-collapsed .main-menu .nav-item,
.sidebar-collapsed .bottom-menu .nav-item {
    display: flex;
    justify-content: center;
    width: 100%;
}

/* 确保按钮在折叠状态下正确对齐 */
.sidebar-collapsed .btn,
.sidebar-collapsed .nav-link {
    justify-content: center !important;
    text-align: center !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* 确保小屏幕高度下的样式 */
.small-height .main-menu,
.small-height .bottom-menu {
    margin: 0 !important;
}

.small-height .sidebar-search {
    margin-bottom: 5px !important;
}

/* 确保退出登录按钮在小窗口时总是可见 */
@media (max-height: 450px) {
    .bottom-menu {
        position: relative !important;
        margin-top: 5px !important;
    }
    
    .sidebar-content {
        padding-bottom: 5px;
    }
}

/* API状态容器 */
.api-status-container {
    margin-top: auto;
    padding: 15px 0 5px 0;
    width: 100%;
}

.api-status-container .btn {
    width: 100%;
}

/* 侧边栏展开按钮样式 */
.sidebar-expand-button {
    position: fixed;
    left: 10px;
    bottom: 20px;
    transform: none;
    z-index: 1000;
    opacity: 1;
    transition: all 0.3s;
}

.sidebar-expand-button:hover {
    transform: scale(1.1);
}

.sidebar-expand-button .btn {
    background-color: #007bff;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 16px;
}

/* 单元格折叠/展开 */
.cell-content {
    position: relative;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.cell-content.expanded {
    white-space: normal;
    max-width: none;
    overflow: visible;
}

.cell-content:hover {
    color: #007bff;
}

/* 确保按钮内的文字和图标垂直居中 */
.sidebar .btn,
.sidebar .nav-link {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

/* 确保图标和文字有合适的间距 */
.sidebar .btn i,
.sidebar .nav-link i {
    margin-right: 10px;
}

/* 修复无法触发按钮的问题 */
.sidebar .btn.btn-outline-primary,
.sidebar .btn.btn-sm,
.sidebar .nav-link {
    text-align: left;
    cursor: pointer;
}

/* 修复侧边栏折叠状态 */
.sidebar-collapsed .sidebar .btn span,
.sidebar-collapsed .sidebar .nav-link span:not(.bi) {
    display: none;
}

/* 表格内容居中样式 */
.data-table td {
    text-align: center;
    vertical-align: middle;
}

.data-table th {
    text-align: center;
    vertical-align: middle;
    background-color: #f2f2f2;
    font-weight: bold;
    padding: 10px;
    white-space: nowrap;
}

/* 备注信息样式优化 */
td[data-remark], .remarks-cell, td:nth-last-child(1), td:nth-last-child(2) {
    text-align: left !important;
    max-width: 200px;
    white-space: normal;
    word-break: break-word;
}

/* 可展开的备注信息 */
.expandable-content {
    position: relative;
    max-height: 40px;
    overflow: hidden;
    text-align: left;
    transition: max-height 0.3s ease, width 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
    max-width: 100%;
}

.expandable-content.expanded {
    max-height: 500px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    width: 100%;
    position: relative;
    z-index: 10;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.expandable-content:not(.expanded)::after {
    content: '...';
    position: absolute;
    bottom: 0;
    right: 0;
    padding-left: 20px;
    background: linear-gradient(to right, transparent, white 40%);
}

/* 修复日期输入框的样式，确保日期选择图标在最右侧 */
input[type="date"] {
    position: relative;
    padding-right: 30px; /* 为日期图标留出空间 */
}

/* 隐藏默认的日期图标并创建自定义位置 */
input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    background-color: transparent;
    width: 20px;
    height: 20px;
}

/* 确保在所有浏览器中日期选择图标位置一致 */
input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-clear-button {
    display: none;
}

/* 确保Mozilla浏览器中日期选择图标位置正确 */
@-moz-document url-prefix() {
    input[type="date"] {
        background-position: right 8px center;
        background-repeat: no-repeat;
    }
}

/* 分页样式重构 */
.dataTables_paginate {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    margin: 12px 0;
    padding: 6px 0;
    overflow-x: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.dataTables_paginate::-webkit-scrollbar {
    display: none;
}

.dataTables_paginate .paginate_button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 8px 12px;
    margin: 0 3px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    line-height: 1;
    min-width: 36px;
    min-height: 36px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-align: center;
}

.dataTables_paginate .paginate_button.current {
    background-color: #007bff;
    color: white !important;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0,123,255,0.3);
}

.dataTables_paginate .paginate_button:hover:not(.current):not(.disabled) {
    background-color: #f0f0f0;
    color: #007bff !important;
}

.dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.current-page-indicator {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin: 0 5px;
    padding: 6px 10px;
    font-size: 14px;
    line-height: 1;
    color: #555;
    background-color: #f5f5f5;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

/* 响应式分页样式 */
@media (max-width: 767.98px) {
    .dataTables_paginate {
        justify-content: center;
        padding: 8px 0;
    }
    
    .dataTables_paginate .paginate_button {
        padding: 7px 10px;
        margin: 0 2px;
        min-width: 34px;
        min-height: 34px;
    }
}

/* 小屏幕 */
@media (max-width: 575.98px) {
    .ultra-compact-pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        max-width: 200px;
        margin: 0 auto !important;
    }
    
    .ultra-compact-pagination .paginate_button {
        flex: 0 0 auto;
        min-width: 34px;
    }
    
    .ultra-compact-pagination .current-page-indicator {
        margin: 0 4px;
        padding: 6px 8px;
        font-size: 13px;
        flex: 0 0 auto;
    }
    
    .compact-pagination {
        max-width: 280px;
        margin: 0 auto !important;
        justify-content: center;
    }
    
    .compact-pagination .paginate_button {
        padding: 6px 8px;
        min-width: 32px;
        min-height: 32px;
    }
    
    .mobile-pagination {
        max-width: 260px;
        margin: 0 auto !important;
        justify-content: center;
    }
    
    .mobile-pagination .paginate_button {
        margin: 0 2px;
    }
}

/* 超小屏幕 */
@media (max-width: 375px) {
    .dataTables_paginate {
        max-width: 95%;
        padding: 4px 0;
    }
    
    .ultra-compact-pagination {
        max-width: 160px;
    }
    
    .compact-pagination {
        max-width: 200px;
    }
    
    .paginate_button {
        padding: 5px 7px;
        font-size: 13px;
        min-width: 30px;
        min-height: 30px;
    }
    
    .current-page-indicator {
        font-size: 12px;
        padding: 4px 6px;
    }
    
    /* 在超小屏幕上保持一致的按钮大小 */
    .paginate_button.previous,
    .paginate_button.next {
        min-width: 30px;
    }
}

/* 添加滑动分页指示器 */
.pagination-scroll-hint {
    text-align: center;
    color: #6c757d;
    font-size: 12px;
    margin-top: 5px;
    opacity: 0.8;
    font-weight: 500;
    visibility: hidden;
    height: 0;
    transition: visibility 0.3s, height 0.3s, opacity 0.3s;
}

.pagination-scroll-hint.visible {
    visibility: visible;
    height: auto;
    opacity: 0.8;
}

/* DataTables长度菜单和过滤器移动端优化 */
@media (max-width: 767.98px) {
    .dataTable-top {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dataTables_length, 
    .dataTables_filter {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }
    
    .dataTables_length select {
        width: auto;
        display: inline-block;
        margin: 0 5px;
    }
    
    .dataTables_filter input {
        width: 100%;
        max-width: 200px;
        margin-left: 5px;
    }
    
    .dataTable-bottom {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .dataTables_info {
        text-align: center;
        margin-bottom: 10px;
        width: 100%;
        font-size: 0.85rem;
    }
}

/* 数据表格信息显示优化 */
@media (max-width: 400px) {
    .dataTables_info {
        font-size: 0.8rem;
        white-space: normal;
        padding: 0 10px;
    }
    
    .dataTables_length label,
    .dataTables_filter label {
        font-size: 0.85rem;
    }
}

/* 修复侧边栏折叠按钮垂直居中问题 */
.sidebar-toggle {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1040;
    cursor: pointer;
    background-color: #fff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    line-height: normal !important; /* 重置line-height */
    padding: 0 !important; /* 确保内边距不会干扰居中 */
}

.sidebar-toggle i {
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
}

.sidebar-toggle i.bi-list {
    font-size: 24px;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100%;
    width: 100%;
    margin: 0 !important; /* 重置可能的外边距 */
    padding: 0 !important; /* 重置可能的内边距 */
    position: relative !important;
    top: 0 !important; /* 防止其他样式影响位置 */
    left: 0 !important;
    transform: none !important; /* 防止其他transform属性影响 */
}

/* 确保在PC视图中不显示移动端折叠按钮 */
@media (min-width: 768px) {
    .sidebar-toggle {
        display: none !important;
    }
}