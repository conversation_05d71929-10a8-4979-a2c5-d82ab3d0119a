/* ==========================================
   Home Page Styles - 首页样式
   ========================================== */

/* 小工具卡片样式 */
.icon-box {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.bg-primary-light {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.bg-success-light {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.bg-warning-light {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.bg-info-light {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
}

.bg-danger-light {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.bg-secondary-light {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.bg-purple-light {
    background-color: rgba(138, 43, 226, 0.1);
    color: #8a2be2;
}

/* 快速链接样式 */
.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #495057;
    padding: 10px;
    border-radius: 10px;
    transition: all 0.3s;
}

.quick-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-3px);
    text-decoration: none;
    color: #495057;
}

.quick-link-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-bottom: 8px;
}

/* 通知列表样式 */
.notice-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.notice-list .list-group-item {
    transition: background-color 0.2s;
    border-left: 3px solid transparent;
}

.notice-list .list-group-item:hover {
    background-color: #f8f9fa;
    border-left-color: #0d6efd;
}

.smaller {
    font-size: 0.7rem;
}

/* 简化版迷你日历样式 */
.mini-calendar {
    height: 300px;
    background-color: #fff;
    border-radius: 8px;
}

.simple-calendar {
    padding: 10px;
}

.calendar-grid {
    font-size: 12px;
}

.week-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 5px;
}

.day-header {
    text-align: center;
    font-weight: bold;
    padding: 5px;
    color: #6c757d;
    font-size: 11px;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.calendar-day {
    position: relative;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 11px;
}

.calendar-day:hover:not(.empty) {
    background-color: #f8f9fa;
}

.calendar-day.today {
    background-color: #0d6efd;
    color: white;
    font-weight: bold;
}

.calendar-day.empty {
    cursor: default;
}

.calendar-day.has-event .day-number {
    font-weight: bold;
}

.event-dot {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
}

/* 待办事项样式 */
.todo-list .list-group-item {
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.todo-list .list-group-item:hover {
    background-color: #f8f9fa;
    border-left-color: #198754;
}

.todo-list .list-group-item.completed {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.form-check-input:checked + .todo-content h6 {
    text-decoration: line-through;
    color: #6c757d;
}

/* 计算器样式 */
.calculator {
    max-width: 300px;
    margin: 0 auto;
}

#calcDisplay {
    font-size: 1.2rem;
    font-weight: bold;
    height: 60px;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    text-align: right;
    padding: 5px 15px;
    font-family: 'Courier New', monospace;
    line-height: 1.2;
    resize: none;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.calc-buttons button {
    height: 50px;
    font-size: 1.1rem;
    font-weight: bold;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
}

.calc-buttons .btn-light {
    background-color: #f8f9fa;
    color: #333;
}

.calc-buttons .btn-light:hover {
    background-color: #e9ecef;
    transform: scale(1.05);
}

.calc-buttons .btn-warning {
    background-color: #ff9500;
    border-color: #ff9500;
    color: white;
}

.calc-buttons .btn-warning:hover {
    background-color: #e8890b;
    border-color: #e8890b;
    transform: scale(1.05);
}

.calc-buttons .btn-warning:active {
    background-color: #d1780a;
    border-color: #d1780a;
}

/* 数字0按钮特殊样式 */
.calc-buttons .row:last-child .col-6 button {
    border-radius: 25px;
}

/* 计算器模态框样式优化 */
#calculatorModal .modal-content {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

#calculatorModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

#calculatorModal .modal-header .btn-close {
    filter: brightness(0) invert(1);
}

/* 按钮按下效果 */
.calc-buttons button:active {
    transform: scale(0.95);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .icon-box {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .quick-link-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    .quick-link {
        padding: 8px;
    }
    
    .mini-calendar {
        height: 250px;
    }
    
    .calendar-day {
        height: 25px;
        font-size: 10px;
    }
}

/* 加载状态和错误状态样式 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-state {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

/* 可访问性增强 */
.quick-link:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

.calendar-day:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 1px;
}

/* 性能优化 - 减少重绘 */
.card {
    will-change: transform;
    backface-visibility: hidden;
}

.quick-link {
    will-change: transform;
    backface-visibility: hidden;
}

/* ==========================================
   企业信用查询模态框样式 - 现代化设计
   ========================================== */

/* 模态框基础样式 */
.enterprise-credit-modal {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.enterprise-credit-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
}

.enterprise-credit-modal .modal-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    backdrop-filter: blur(10px);
}

.enterprise-credit-modal .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.enterprise-credit-modal .btn-close:hover {
    opacity: 1;
}

/* 卡片样式 */
.input-card, .settings-card {
    background: #ffffff;
    border: 1px solid #e8ecf4;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
}

.input-card:hover, .settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.input-card .card-header, .settings-card .card-header {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-bottom: 1px solid #e8ecf4;
    border-radius: 16px 16px 0 0;
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: #4c63d2;
}

.input-card .card-body, .settings-card .card-body {
    padding: 1.5rem;
}

/* 现代化输入框 */
.modern-input {
    border: 2px solid #e8ecf4;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fafbff;
}

.modern-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: #ffffff;
    outline: none;
}

/* 现代化按钮 */
.btn-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-modern:active {
    transform: translateY(0);
}

.btn-modern:disabled {
    background: #9ca3af;
    transform: none;
    box-shadow: none;
}

/* 开关样式 */
.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-input:focus {
    border-color: #667eea;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

/* 滑块样式 */
.form-range {
    height: 6px;
}

.form-range::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    border: 3px solid #ffffff;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.form-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    border: 3px solid #ffffff;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 结果区域样式 */
.result-section {
    background: #ffffff;
    border: 1px solid #e8ecf4;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.result-header {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e8ecf4;
}

.result-content {
    position: relative;
    min-height: 400px;
}

.result-view {
    display: none;
    padding: 1.5rem;
}

.result-view.active {
    display: block;
}

/* 文本输出区域 */
.output-area {
    background: #fafbff;
    border: 2px solid #e8ecf4;
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 350px;
    max-height: 450px;
    overflow-y: auto;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 14px;
    position: relative;
}

.output-area::-webkit-scrollbar {
    width: 8px;
}

.output-area::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
}

.output-area::-webkit-scrollbar-thumb {
    background: #c1c7d0;
    border-radius: 4px;
}

.output-area::-webkit-scrollbar-thumb:hover {
    background: #a8b1bd;
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.empty-title {
    color: #4c63d2;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.empty-text {
    color: #6b7280;
    margin: 0;
}

/* 现代化表格样式 */
.modern-table {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.modern-table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

.modern-table tbody td {
    padding: 1rem;
    border-bottom: 1px solid #e8ecf4;
    vertical-align: middle;
}

.modern-table tbody tr:hover {
    background: #f8f9ff;
}

.modern-table tbody tr:last-child td {
    border-bottom: none;
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

.status-dot.connecting {
    background: #f59e0b;
}

.status-dot.error {
    background: #ef4444;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 状态栏 */
.status-bar {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    padding: 1rem 1.5rem;
    border-top: 1px solid #e8ecf4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .enterprise-credit-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
}

@media (max-width: 768px) {
    .enterprise-credit-modal .modal-header {
        padding: 1rem;
    }

    .enterprise-credit-modal .modal-body {
        padding: 1rem;
    }

    .input-card .card-body, .settings-card .card-body {
        padding: 1rem;
    }

    .result-view {
        padding: 1rem;
    }

    .output-area {
        min-height: 250px;
        max-height: 300px;
    }

    .empty-state {
        height: 200px;
    }

    .empty-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
}