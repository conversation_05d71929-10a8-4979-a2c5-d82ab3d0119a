<template>
  <div class="login-container">
    <div class="login-content">
      <!-- 登录表单卡片 -->
      <div class="login-card">
        <!-- 头部Logo和标题 -->
        <div class="login-header">
          <div class="logo-container">
            <img src="/logo.png" alt="Logo" class="logo" />
          </div>
          <h1 class="title">太享查询系统</h1>
          <p class="subtitle">企业级金融数据查询展示平台</p>
        </div>
        
        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
          @submit.prevent="handleLogin"
        >
          <!-- 用户名 -->
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              clearable
              autocomplete="username"
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <!-- 密码 -->
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              autocomplete="current-password"
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <!-- 验证码 -->
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="请输入验证码"
                :prefix-icon="Shield"
                clearable
                class="captcha-input"
                @keyup.enter="handleLogin"
              />
              <div class="captcha-image-container">
                <img
                  v-if="captchaImage"
                  :src="captchaImage"
                  alt="验证码"
                  class="captcha-image"
                  @click="refreshCaptcha"
                />
                <el-button
                  v-else
                  type="info"
                  text
                  :loading="captchaLoading"
                  @click="refreshCaptcha"
                >
                  获取验证码
                </el-button>
              </div>
            </div>
          </el-form-item>
          
          <!-- 记住我 -->
          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="rememberMe">
                记住我
              </el-checkbox>
              <el-button type="primary" text>
                忘记密码？
              </el-button>
            </div>
          </el-form-item>
          
          <!-- 登录按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loginLoading"
              class="login-button"
              @click="handleLogin"
            >
              {{ loginLoading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
        
        <!-- 其他登录方式 -->
        <div class="login-footer">
          <el-divider>
            <span class="divider-text">其他登录方式</span>
          </el-divider>
          
          <div class="social-login">
            <el-button circle :icon="Message" title="短信登录" />
            <el-button circle :icon="Fingerprint" title="指纹登录" />
            <el-button circle :icon="Key" title="密钥登录" />
          </div>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="copyright">
        <p>&copy; 2024 太享查询系统. All rights reserved.</p>
        <p>Version {{ version }}</p>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="login-background">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock, Shield, Message, Fingerprint, Key } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api/auth'
import type { LoginRequest } from '@/types/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const loginFormRef = ref<FormInstance>()
const loginLoading = ref(false)
const captchaLoading = ref(false)
const captchaImage = ref('')
const captchaId = ref('')
const rememberMe = ref(false)
const version = ref('2.0.0')

// 表单数据
const loginForm = reactive<LoginRequest>({
  username: '',
  password: '',
  captcha: '',
  captchaId: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 位', trigger: 'blur' }
  ]
}

// 获取验证码
const refreshCaptcha = async () => {
  captchaLoading.value = true
  
  try {
    const response = await authApi.getCaptcha()
    captchaImage.value = response.captchaImage
    captchaId.value = response.captchaId
    loginForm.captchaId = response.captchaId
    
    // 清空验证码输入
    loginForm.captcha = ''
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败，请重试')
  } finally {
    captchaLoading.value = false
  }
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    // 表单验证
    await loginFormRef.value.validate()
    
    loginLoading.value = true
    
    // 调用登录API
    const result = await authStore.login(loginForm)
    
    if (result.success) {
      ElMessage.success('登录成功')
      
      // 记住我功能
      if (rememberMe.value) {
        localStorage.setItem('rememberedUsername', loginForm.username)
      } else {
        localStorage.removeItem('rememberedUsername')
      }
      
      // 跳转到目标页面
      const redirect = route.query.redirect as string || '/dashboard'
      await router.push(redirect)
    } else {
      ElMessage.error(result.message || '登录失败')
      // 刷新验证码
      await refreshCaptcha()
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请检查网络连接')
    // 刷新验证码
    await refreshCaptcha()
  } finally {
    loginLoading.value = false
  }
}

// 初始化
onMounted(async () => {
  // 获取验证码
  await refreshCaptcha()
  
  // 恢复记住的用户名
  const rememberedUsername = localStorage.getItem('rememberedUsername')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    rememberMe.value = true
  }
})
</script>

<style scoped>
.login-container {
  @apply min-h-screen flex items-center justify-center relative overflow-hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-content {
  @apply relative z-10 w-full max-w-md px-6;
}

.login-card {
  @apply bg-white rounded-2xl shadow-2xl p-8 backdrop-blur-sm;
  background: rgba(255, 255, 255, 0.95);
}

.login-header {
  @apply text-center mb-8;
}

.logo-container {
  @apply mb-4;
}

.logo {
  @apply w-16 h-16 mx-auto;
}

.title {
  @apply text-2xl font-bold text-gray-800 mb-2;
}

.subtitle {
  @apply text-gray-600 text-sm;
}

.login-form {
  @apply space-y-4;
}

.captcha-container {
  @apply flex gap-3;
}

.captcha-input {
  @apply flex-1;
}

.captcha-image-container {
  @apply w-24 h-10 flex items-center justify-center bg-gray-100 rounded border cursor-pointer;
}

.captcha-image {
  @apply w-full h-full object-cover rounded;
}

.login-options {
  @apply flex justify-between items-center w-full;
}

.login-button {
  @apply w-full h-12 text-base font-medium;
}

.login-footer {
  @apply mt-8;
}

.divider-text {
  @apply text-gray-500 text-sm px-4;
}

.social-login {
  @apply flex justify-center gap-4 mt-4;
}

.copyright {
  @apply text-center mt-8 text-gray-400 text-xs;
}

.copyright p {
  @apply mb-1;
}

/* 背景装饰 */
.login-background {
  @apply absolute inset-0 overflow-hidden;
}

.bg-shape {
  @apply absolute rounded-full opacity-10;
  background: rgba(255, 255, 255, 0.1);
}

.shape-1 {
  @apply w-96 h-96 -top-48 -left-48;
  animation: float 6s ease-in-out infinite;
}

.shape-2 {
  @apply w-80 h-80 -bottom-40 -right-40;
  animation: float 8s ease-in-out infinite reverse;
}

.shape-3 {
  @apply w-64 h-64 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .login-content {
    @apply px-4;
  }
  
  .login-card {
    @apply p-6 rounded-xl;
  }
  
  .title {
    @apply text-xl;
  }
  
  .captcha-container {
    @apply flex-col gap-2;
  }
  
  .captcha-image-container {
    @apply w-full h-12;
  }
  
  .social-login {
    @apply gap-6;
  }
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  .login-card {
    @apply bg-gray-800 text-white;
    background: rgba(31, 41, 55, 0.95);
  }
  
  .title {
    @apply text-white;
  }
  
  .subtitle {
    @apply text-gray-300;
  }
  
  .captcha-image-container {
    @apply bg-gray-700;
  }
}

/* 动画效果 */
.login-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单项动画 */
.login-form :deep(.el-form-item) {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

.login-form :deep(.el-form-item:nth-child(1)) { animation-delay: 0.1s; }
.login-form :deep(.el-form-item:nth-child(2)) { animation-delay: 0.2s; }
.login-form :deep(.el-form-item:nth-child(3)) { animation-delay: 0.3s; }
.login-form :deep(.el-form-item:nth-child(4)) { animation-delay: 0.4s; }
.login-form :deep(.el-form-item:nth-child(5)) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
  from {
    opacity: 0;
    transform: translateY(20px);
  }
}
</style>
