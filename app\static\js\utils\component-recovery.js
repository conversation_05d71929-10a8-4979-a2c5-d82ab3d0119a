/**
 * 组件恢复机制
 * 处理组件初始化失败和运行时错误的恢复
 */

window.ComponentRecovery = {
    
    // 组件恢复配置
    config: {
        maxRetries: 3,
        retryDelay: 1000,
        gracefulDegradation: true,
        userNotification: true
    },
    
    // 组件状态追踪
    componentStatus: {},
    
    // 安全初始化组件
    safeInitialize: function(componentName, initFunction, fallbackFunction = null) {
        console.log(`开始安全初始化组件: ${componentName}`);
        
        // 记录组件状态
        this.componentStatus[componentName] = {
            status: 'initializing',
            retryCount: 0,
            lastError: null,
            initialized: false
        };
        
        return this._tryInitialize(componentName, initFunction, fallbackFunction);
    },
    
    // 尝试初始化组件
    _tryInitialize: function(componentName, initFunction, fallbackFunction) {
        const status = this.componentStatus[componentName];
        
        return new Promise((resolve, reject) => {
            try {
                const result = initFunction();
                
                // 处理Promise返回值
                if (result && typeof result.then === 'function') {
                    result.then(data => {
                        this._handleInitializationSuccess(componentName, data);
                        resolve(data);
                    }).catch(error => {
                        this._handleInitializationError(componentName, error, initFunction, fallbackFunction)
                            .then(resolve).catch(reject);
                    });
                } else {
                    this._handleInitializationSuccess(componentName, result);
                    resolve(result);
                }
            } catch (error) {
                this._handleInitializationError(componentName, error, initFunction, fallbackFunction)
                    .then(resolve).catch(reject);
            }
        });
    },
    
    // 处理初始化成功
    _handleInitializationSuccess: function(componentName, data) {
        const status = this.componentStatus[componentName];
        status.status = 'initialized';
        status.initialized = true;
        status.lastError = null;
        
        console.log(`组件 ${componentName} 初始化成功`);
        
        // 发送成功事件
        this._dispatchEvent('componentInitialized', {
            component: componentName,
            data: data
        });
    },
    
    // 处理初始化错误
    _handleInitializationError: function(componentName, error, initFunction, fallbackFunction) {
        const status = this.componentStatus[componentName];
        status.lastError = error;
        status.retryCount++;
        
        console.error(`组件 ${componentName} 初始化失败 (尝试 ${status.retryCount}/${this.config.maxRetries}):`, error);
        
        return new Promise((resolve, reject) => {
            // 检查是否应该重试
            if (status.retryCount < this.config.maxRetries) {
                const delay = this.config.retryDelay * Math.pow(2, status.retryCount - 1);
                console.log(`${delay}ms后重试初始化组件: ${componentName}`);
                
                setTimeout(() => {
                    this._tryInitialize(componentName, initFunction, fallbackFunction)
                        .then(resolve).catch(reject);
                }, delay);
            } else {
                // 尝试优雅降级
                if (fallbackFunction && this.config.gracefulDegradation) {
                    console.warn(`尝试优雅降级组件: ${componentName}`);
                    
                    try {
                        const fallbackResult = fallbackFunction();
                        status.status = 'degraded';
                        
                        if (this.config.userNotification && typeof DataValidator !== 'undefined') {
                            DataValidator.showUserMessage('组件提示', `${componentName}功能受限，正在使用简化版本`, 'warning');
                        }
                        
                        this._dispatchEvent('componentDegraded', {
                            component: componentName,
                            error: error,
                            fallbackData: fallbackResult
                        });
                        
                        resolve(fallbackResult);
                    } catch (fallbackError) {
                        console.error(`组件 ${componentName} 优雅降级也失败:`, fallbackError);
                        this._handleFinalFailure(componentName, error, fallbackError);
                        reject(fallbackError);
                    }
                } else {
                    this._handleFinalFailure(componentName, error);
                    reject(error);
                }
            }
        });
    },
    
    // 处理最终失败
    _handleFinalFailure: function(componentName, primaryError, fallbackError = null) {
        const status = this.componentStatus[componentName];
        status.status = 'failed';
        
        console.error(`组件 ${componentName} 最终初始化失败:`, primaryError);
        
        if (this.config.userNotification && typeof DataValidator !== 'undefined') {
            DataValidator.showUserMessage('组件错误', `${componentName}功能暂时不可用`, 'error');
        }
        
        this._dispatchEvent('componentFailed', {
            component: componentName,
            primaryError: primaryError,
            fallbackError: fallbackError
        });
    },
    
    // 检查组件状态
    getComponentStatus: function(componentName) {
        return this.componentStatus[componentName] || { status: 'unknown', initialized: false };
    },
    
    // 重置组件状态
    resetComponent: function(componentName) {
        if (this.componentStatus[componentName]) {
            this.componentStatus[componentName] = {
                status: 'reset',
                retryCount: 0,
                lastError: null,
                initialized: false
            };
            console.log(`组件 ${componentName} 状态已重置`);
        }
    },
    
    // 获取所有组件状态
    getAllComponentStatus: function() {
        return Object.keys(this.componentStatus).map(name => ({
            name: name,
            ...this.componentStatus[name]
        }));
    },
    
    // 发送自定义事件
    _dispatchEvent: function(eventName, detail) {
        const event = new CustomEvent(eventName, { detail: detail });
        document.dispatchEvent(event);
    },
    
    // 监听组件事件
    onComponentEvent: function(eventName, callback) {
        document.addEventListener(eventName, callback);
    },
    
    // 安全执行函数
    safeExecute: function(functionName, func, fallback = null) {
        try {
            return func();
        } catch (error) {
            console.error(`函数 ${functionName} 执行失败:`, error);
            
            if (fallback) {
                try {
                    return fallback();
                } catch (fallbackError) {
                    console.error(`函数 ${functionName} 的fallback也失败:`, fallbackError);
                    throw fallbackError;
                }
            }
            
            throw error;
        }
    }
};

// 全局错误处理器
window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
    
    // 如果是组件相关错误，尝试恢复
    const errorMessage = event.error.message;
    if (errorMessage.includes('Chart') || errorMessage.includes('DataTable')) {
        console.log('检测到组件错误，尝试恢复...');
        
        if (typeof DataValidator !== 'undefined') {
            DataValidator.showUserMessage('系统提示', '检测到组件错误，正在尝试恢复', 'info');
        }
    }
});

// 未捕获Promise错误处理
window.addEventListener('unhandledrejection', function(event) {
    console.error('未捕获的Promise错误:', event.reason);
    
    // 防止错误导致页面崩溃
    event.preventDefault();
    
    if (typeof DataValidator !== 'undefined') {
        DataValidator.showUserMessage('系统提示', '检测到异步错误，系统正在处理', 'warning');
    }
});

console.log('组件恢复机制已加载');