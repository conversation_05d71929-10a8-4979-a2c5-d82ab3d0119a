/**
 * 增强型数据表格功能
 * 优化的表格显示、响应式布局和内容处理
 */

// 增强数据表格功能
function enhanceDataTables() {
    console.log('开始应用表格增强功能');
    
    // 获取所有数据表格
    const tables = document.querySelectorAll('.data-table');
    if (!tables.length) {
        console.log('页面上没有发现数据表格');
        return;
    }
    
    console.log(`发现 ${tables.length} 个数据表格需要增强`);
    
    // 为每个表格应用增强功能
    tables.forEach(function(table, index) {
        try {
            // 确保表格已初始化为DataTable
            if (!$.fn.DataTable.isDataTable(table)) {
                console.log(`表格 #${index+1} 尚未初始化为DataTable`);
                return;
            }
            
            // 获取DataTable实例
            const dataTable = $(table).DataTable();
            
            // 应用自适应列宽
            applyAdaptiveColumnWidths(table, dataTable);
            
            // 处理长文本内容
            processLongTextCells(table, dataTable);
            
            // 增强数字列的可读性
            enhanceNumericColumns(table, dataTable);
            
            // 应用移动端响应优化
            optimizeForMobile(table, dataTable);
            
            // 应用优化的分页控件
            optimizePagination(dataTable, window.innerWidth <= 375);
            
            // 增强移动端分页体验
            enhanceMobilePagination(table);

            // 强制重新计算列宽 - 修复表格错位问题
            dataTable.columns.adjust().responsive.recalc();
            
            console.log(`表格 #${index+1} 增强功能应用完成`);
        } catch (error) {
            console.error(`应用表格 #${index+1} 增强功能时出错:`, error);
        }
    });
}

/**
 * 应用自适应列宽
 */
function applyAdaptiveColumnWidths(table, dataTable) {
    // 设置最小和最大列宽
    const minWidth = 80;
    const maxWidth = 300;
    
    // 获取表格所有列
    const columns = dataTable.columns();
    const columnCount = columns[0].length;
    
    // 根据内容类型和长度分配合适的列宽
    for (let i = 0; i < columnCount; i++) {
        const columnData = dataTable.column(i).data();
        let maxContentLength = 0;
        let hasLongContent = false;
        
        // 分析列内容
        columnData.each(function(value) {
            if (!value || value === '-') return;
            
            const contentLength = String(value).length;
            maxContentLength = Math.max(maxContentLength, contentLength);
            
            if (contentLength > 30) {
                hasLongContent = true;
            }
        });
        
        // 设置列宽
        const headerText = $(dataTable.column(i).header()).text().trim();
        const headerLength = headerText.length;
        
        // 计算理想宽度
        let idealWidth = Math.max(headerLength * 12, maxContentLength * 8);
        idealWidth = Math.max(minWidth, Math.min(idealWidth, maxWidth));
        
        // 为有长内容的列添加类
        if (hasLongContent) {
            $(dataTable.column(i).nodes()).addClass('long-content-column');
        }
        
        // 应用宽度 (使用CSS类而不是固定宽度，以保持响应性)
        if (idealWidth > 200) {
            $(dataTable.column(i).header()).addClass('wide-column');
        } else if (idealWidth < 100) {
            $(dataTable.column(i).header()).addClass('narrow-column');
        }
    }
}

/**
 * 处理长文本单元格
 */
function processLongTextCells(table, dataTable) {
    // 获取所有可能包含长文本的单元格
    const longTextCells = table.querySelectorAll('td:not(.dtr-control)');
    
    longTextCells.forEach(function(cell) {
        // 跳过空单元格或只有短内容的单元格
        const content = cell.textContent.trim();
        if (!content || content === '-' || content.length < 20) return;
        
        // 检查单元格是否已处理过
        if (cell.querySelector('.cell-content')) return;
        
        // 创建可展开内容容器
        const expandableContent = document.createElement('span');
        expandableContent.className = 'cell-content';
        expandableContent.textContent = content;
        
        // 清空单元格并添加可展开内容
        cell.textContent = '';
        cell.appendChild(expandableContent);
        
        // 添加点击事件以展开/折叠内容
        expandableContent.addEventListener('click', function(e) {
            e.stopPropagation();
            this.classList.toggle('expanded');
            
            // 如果展开了内容，确保内容在单元格内并自动换行
            if (this.classList.contains('expanded')) {
                // 使用单元格宽度作为展开内容的最大宽度
                const parentCell = this.closest('td');
                if (parentCell) {
                    this.style.maxWidth = parentCell.offsetWidth + 'px';
                }
                
                // 重新调整表格布局
                setTimeout(function() {
                    dataTable.columns.adjust();
                }, 50);
            } else {
                // 恢复初始状态
                this.style.maxWidth = '';
            }
        });
    });
}

/**
 * 增强数字列的可读性
 */
function enhanceNumericColumns(table, dataTable) {
    // 获取表格所有列
    const columns = dataTable.columns();
    const columnCount = columns[0].length;
    
    // 识别数字列并应用格式
    for (let i = 0; i < columnCount; i++) {
        const columnData = dataTable.column(i).data();
        let isNumericColumn = true;
        let isMoneyColumn = false;
        
        // 检查该列是否都是数字
        columnData.each(function(value) {
            if (!value || value === '-') return;
            
            const numValue = parseFloat(value.toString().replace(/,/g, ''));
            if (isNaN(numValue)) {
                isNumericColumn = false;
                return;
            }
            
            // 判断是否是金额列 (通常大值且有小数点)
            if (numValue > 100 && value.toString().includes('.')) {
                isMoneyColumn = true;
            }
        });
        
        // 为数字列应用格式化
        if (isNumericColumn) {
            if (isMoneyColumn) {
                // 金额列加上特殊样式
                $(dataTable.column(i).nodes()).addClass('money-column');
            } else {
                // 普通数字列
                $(dataTable.column(i).nodes()).addClass('numeric-column');
            }
        }
    }
}

/**
 * 移动端优化
 */
function optimizeForMobile(table, dataTable) {
    // 检查是否为移动设备
    const isMobileDevice = window.innerWidth < 768;
    const isVerySmallScreen = window.innerWidth < 400;
    
    if (isMobileDevice) {
        console.log('应用移动端优化');
        
        // 为表格添加移动端样式类
        table.classList.add('mobile-optimized');
        
        // 优化列显示
        const api = dataTable;
        const columns = api.columns();
        
        // 获取表格列数
        const columnCount = columns[0].length;
        
        if (columnCount > 4) {
            // 在极小屏幕上隐藏非关键列
            for (let i = 0; i < columnCount; i++) {
                const headerText = $(api.column(i).header()).text().trim();
                const isImportant = isImportantColumnHeader(headerText);
                
                // 根据重要性和屏幕大小决定是否显示
                const shouldHide = isVerySmallScreen ? 
                    !isImportant && i > 0 : // 极小屏幕只显示重要列
                    !isImportant && i > 2;  // 普通移动屏幕隐藏部分非重要列
                
                if (shouldHide) {
                    api.column(i).visible(false);
                }
            }
        }
        
        // 优化表格布局
        $(table).addClass('table-sm');
        
        // 增强移动端分页体验
        enhanceMobilePagination(table);
        
        // 优化分页显示
        optimizePagination(dataTable, isVerySmallScreen);
    }
}

// 压缩分页 - 减少显示的页码数量
function compactPagination(api) {
    const info = api.page.info();
    const currentPage = info.page;
    const totalPages = info.pages;
    
    // 仅在移动端且页数超过5页时使用省略号模式
    if (window.innerWidth <= 767 && totalPages > 5) {
        // 清除之前可能存在的省略号
        $(api.nodes()).find('.ellipsis-indicator').remove();
        
        // 始终显示首页、尾页和当前页
        // 根据当前页位置动态调整显示哪些页码
        let visiblePages = [0]; // 首页
        
        if (currentPage > 1) {
            visiblePages.push(currentPage - 1); // 当前页的前一页
        }
        
        visiblePages.push(currentPage); // 当前页
        
        if (currentPage < totalPages - 2) {
            visiblePages.push(currentPage + 1); // 当前页的后一页
        }
        
        if (totalPages > 1) {
            visiblePages.push(totalPages - 1); // 尾页
        }
        
        // 处理页码按钮的显示
        $(api.nodes()).find('.paginate_button:not(.previous):not(.next)').each(function() {
            const pageIdx = parseInt($(this).data('dt-idx')) - 1;
            if (!visiblePages.includes(pageIdx)) {
                $(this).addClass('mobile-hidden');
            } else {
                $(this).removeClass('mobile-hidden');
            }
        });
        
        // 添加省略号指示器
        if (totalPages > 5) {
            // 如果当前页不靠近首页，添加左侧省略号
            if (currentPage > 2) {
                const firstButton = $(api.nodes()).find('.paginate_button[data-dt-idx="1"]');
                const prevButton = $(api.nodes()).find(`.paginate_button[data-dt-idx="${currentPage}"]`);
                if (firstButton.length && prevButton.length && 
                    Math.abs(parseInt(firstButton.data('dt-idx')) - parseInt(prevButton.data('dt-idx'))) > 1) {
                    const ellipsisLeft = $('<span class="ellipsis-indicator">...</span>');
                    firstButton.after(ellipsisLeft);
                }
            }
            
            // 如果当前页不靠近尾页，添加右侧省略号
            if (currentPage < totalPages - 3) {
                const lastButton = $(api.nodes()).find(`.paginate_button[data-dt-idx="${totalPages}"]`);
                const nextButton = $(api.nodes()).find(`.paginate_button[data-dt-idx="${currentPage+2}"]`);
                if (lastButton.length && nextButton.length && 
                    Math.abs(parseInt(lastButton.data('dt-idx')) - parseInt(nextButton.data('dt-idx'))) > 1) {
                    const ellipsisRight = $('<span class="ellipsis-indicator">...</span>');
                    nextButton.after(ellipsisRight);
                }
            }
        }
    }
}

// 简化移动端分页为页码指示器
function simplifyMobilePagination(dataTable) {
    // 只在小屏幕上应用
    if (window.innerWidth <= 480) {
        // 获取API实例 - 兼容不同的输入类型
        let api;
        if (dataTable && typeof dataTable.api === 'function') {
            // 如果是jQuery DataTable包装器
            api = dataTable.api();
        } else if (dataTable && typeof dataTable.page === 'function') {
            // 如果已经是API实例
            api = dataTable;
        } else {
            console.warn('simplifyMobilePagination: 无效的dataTable参数', dataTable);
            return;
        }
        const info = api.page.info();
        const wrapper = $(api.table().container());
        const paginationContainer = wrapper.find('.dataTables_paginate');
        
        // 清空并重建分页控件
        const prevBtn = paginationContainer.find('.paginate_button.previous').detach();
        const nextBtn = paginationContainer.find('.paginate_button.next').detach();
        
        // 移除所有其他分页按钮
        paginationContainer.find('.paginate_button:not(.previous):not(.next)').remove();
        paginationContainer.find('.ellipsis-indicator').remove();
        paginationContainer.find('.current-page-indicator').remove();
        
        // 重新添加上一页和下一页按钮
        paginationContainer.empty();
        paginationContainer.append(prevBtn);
        
        // 创建页码指示器
        const pageIndicator = $('<span class="current-page-indicator"></span>');
        pageIndicator.text(`${info.page + 1} / ${info.pages}`);
        paginationContainer.append(pageIndicator);
        
        // 添加下一页按钮
        paginationContainer.append(nextBtn);
        
        // 确保按钮状态正确
        if (info.page === 0) {
            prevBtn.addClass('disabled');
        } else {
            prevBtn.removeClass('disabled');
        }
        
        if (info.page === info.pages - 1) {
            nextBtn.addClass('disabled');
        } else {
            nextBtn.removeClass('disabled');
        }
    }
}

// 优化分页控件在移动端的显示
function optimizePagination(dataTable, isVerySmallScreen) {
    try {
        // 获取API实例 - 兼容不同的输入类型
        let api;
        if (dataTable && typeof dataTable.api === 'function') {
            // 如果是jQuery DataTable包装器
            api = dataTable.api();
        } else if (dataTable && typeof dataTable.page === 'function') {
            // 如果已经是API实例
            api = dataTable;
        } else {
            console.warn('optimizePagination: 无效的dataTable参数', dataTable);
            return;
        }
        const info = api.page.info();
        
        // 检查info是否有效
        if (!info || info.pages === undefined) {
            console.warn('optimizePagination: DataTable页面信息无效，跳过分页优化');
            return;
        }
        
        // 如果没有多页，不需要分页优化
        if (info.pages <= 1) return;
        
        // 检查是否是移动设备
        const isMobile = window.innerWidth <= 767;
        const isExtraSmall = window.innerWidth <= 480;
        
        if (isMobile) {
            if (isExtraSmall || isVerySmallScreen) {
                // 超小屏幕使用超简化版
                simplifyMobilePagination(api);
            } else {
                // 普通移动设备使用紧凑型分页
                compactPagination(api);
                
                // 确保页码按钮不超过5个可见
                if (info.pages > 5) {
                    $(api.table().node()).find('.paginate_button:not(.previous):not(.next):not(.current)').each(function(i) {
                        if (i > 3) { 
                            $(this).addClass('mobile-hidden');
                        }
                    });
                }
            }
            
            // 处理分页容器横向滚动
            const paginateContainer = $(api.table().container()).find('.dataTables_paginate');
            paginateContainer.addClass('mobile-pagination');
            
            // 监听滚动事件，添加滚动指示器
            paginateContainer.on('scroll', function() {
                const scrollWidth = this.scrollWidth;
                const clientWidth = this.clientWidth;
                const scrollLeft = this.scrollLeft;
                
                // 添加滚动指示器类
                if (scrollLeft <= 5) {
                    paginateContainer.addClass('at-left-edge');
                } else {
                    paginateContainer.removeClass('at-left-edge');
                }
                
                if (scrollLeft + clientWidth >= scrollWidth - 5) {
                    paginateContainer.addClass('at-right-edge');
                } else {
                    paginateContainer.removeClass('at-right-edge');
                }
            });
            
            // 触发一次滚动事件，确保滚动指示器正确显示
            paginateContainer.trigger('scroll');
        } else {
            // 桌面版使用标准分页
            const paginateContainer = $(api.table().container()).find('.dataTables_paginate');
            paginateContainer.removeClass('mobile-pagination');
            $(api.table().node()).find('.paginate_button').removeClass('mobile-hidden');
            paginateContainer.find('.ellipsis-indicator').remove();
            paginateContainer.find('.current-page-indicator').remove();
        }
    } catch (e) {
        console.error('优化分页控件失败:', e);
    }
}

// 增强移动端分页体验 - 使用EventManager
function enhanceMobilePagination(table) {
    if (!table || !$.fn.DataTable.isDataTable(table)) return;
    
    const dataTable = $(table).DataTable();
    const container = $(dataTable.table().container());
    const paginationContainer = container.find('.dataTables_paginate')[0];
    
    if (!paginationContainer) return;
    
    // 先清理旧的事件绑定
    EventManager.unbindAll(paginationContainer);
    
    // 添加触摸滑动翻页功能
    let touchStartX = 0;
    let touchEndX = 0;
    
    // 使用EventManager统一管理事件
    EventManager.bind(paginationContainer, 'touchstart', function(e) {
        touchStartX = e.touches[0].clientX;
    }, { passive: true });
    
    EventManager.bind(paginationContainer, 'touchmove', function(e) {
        touchEndX = e.touches[0].clientX;
    }, { passive: true });
    
    EventManager.bind(paginationContainer, 'touchend', function() {
        handleSwipe();
    }, { passive: true });
    
    function handleSwipe() {
        const minSwipeDistance = 80;
        const swipeDistance = touchEndX - touchStartX;
        
        if (Math.abs(swipeDistance) >= minSwipeDistance) {
            const api = dataTable.api();
            const info = api.page.info();
            
            if (swipeDistance > 0 && info.page > 0) {
                api.page('previous').draw('page');
            } else if (swipeDistance < 0 && info.page < info.pages - 1) {
                api.page('next').draw('page');
            }
        }
    }
    
    // 使用节流优化窗口大小变化事件
    EventManager.throttle(window, 'resize', function() {
        optimizePagination(dataTable, window.innerWidth <= 375);
    }, 150);
    
    // 初始执行一次优化
    optimizePagination(dataTable, window.innerWidth <= 375);
}

/**
 * 判断列是否是重要列
 */
function isImportantColumnHeader(headerText) {
    // 根据实际业务需求确定哪些列是重要的
    const importantHeaders = [
        '订单编号', '客户姓名', '联系电话', '账单日期', '金额', '状态'
    ];
    
    return importantHeaders.some(header => 
        headerText.includes(header)
    );
}

/**
 * 初始化数据表格并应用统一配置
 */
function initializeDataTable(tableElement) {
    if (!tableElement) return null;
    
    // 检查表格是否已初始化
    if ($.fn.DataTable.isDataTable(tableElement)) {
        $(tableElement).DataTable().destroy();
    }
    
    // 初始化表格
    const table = $(tableElement).DataTable({
        responsive: {
            details: {
                type: 'column',
                target: 'tr'
            }
        },
        columnDefs: [
            {
                className: 'dtr-control',
                orderable: false,
                targets: 0
            }
        ],
        order: [[1, 'asc']],
        dom: '<"dataTable-top"lf>rt<"dataTable-bottom"ip>',
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]],
        language: {
            search: "搜索:",
            lengthMenu: "显示 _MENU_ 条数据",
            info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            infoEmpty: "显示第 0 至 0 项结果，共 0 项",
            infoFiltered: "(由 _MAX_ 项结果过滤)",
            paginate: {
                first: "首页",
                previous: "上页",
                next: "下页",
                last: "末页"
            },
            zeroRecords: "没有匹配结果",
            emptyTable: "暂无数据",
            loadingRecords: "加载中...",
            processing: "处理中..."
        },
        drawCallback: function() {
            // 表格绘制完成后自动应用增强功能
            enhanceDataTableInstance(this);
            
            // 应用优化的分页控件
            optimizePagination(this, window.innerWidth <= 375);
        }
    });
    
    return table;
}

/**
 * 增强单个数据表格实例
 */
function enhanceDataTableInstance(table) {
    const dataTable = table.api();
    const tableElement = dataTable.table().node();
    
    applyAdaptiveColumnWidths(tableElement, dataTable);
    processLongTextCells(tableElement, dataTable);
    enhanceNumericColumns(tableElement, dataTable);
    optimizeForMobile(tableElement, dataTable);
    
    // 强制重新计算列宽 - 修复表格错位问题
    dataTable.columns.adjust().responsive.recalc();
}

// 当DOM加载完成后应用增强功能
document.addEventListener('DOMContentLoaded', function() {
    // 全局变量，用于标记是否需要重新初始化表格
    window.needTableReinitialization = false;
    
    // 监听窗口大小变化，动态调整分页
    window.addEventListener('resize', function() {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(function(table) {
            if ($.fn.DataTable.isDataTable(table)) {
                try {
                    const dataTable = $(table).DataTable();
                    // 添加延迟确保DataTable完全初始化
                    setTimeout(function() {
                        optimizePagination(dataTable, window.innerWidth <= 375);
                    }, 100);
                } catch (error) {
                    console.warn('优化分页控件时出错:', error);
                }
            }
        });
    });
    
    // 表格的tab切换时重新应用增强功能
    const dataTabs = document.getElementById('dataTabs');
    if (dataTabs) {
        dataTabs.addEventListener('shown.bs.tab', function(e) {
            const targetTabId = e.target.getAttribute('data-bs-target').replace('#', '');
            const targetPane = document.getElementById(targetTabId);
            
            if (targetPane) {
                const tableElement = targetPane.querySelector('.data-table');
                if (tableElement && $.fn.DataTable.isDataTable(tableElement)) {
                    console.log(`Tab切换到 ${targetTabId}，重新计算表格布局`);
                    const dataTable = $(tableElement).DataTable();
                    
                    // 使用requestAnimationFrame确保浏览器已渲染DOM
                    requestAnimationFrame(function() {
                        dataTable.columns.adjust().responsive.recalc();
                        
                        // 特别处理逾期订单表格
                        if (targetTabId === 'overdue') {
                            console.log('逾期订单表格特殊处理');
                            // 强制重新处理长文本单元格
                            processLongTextCells(tableElement, dataTable);
                            // 确保单元格点击事件正常工作
                            $(tableElement).find('.cell-content').off('click').on('click', function(e) {
                                e.stopPropagation();
                                $(this).toggleClass('expanded');
                            });
                        }
                        
                        enhanceDataTables();
                    });
                }
            }
        });
    }

    // 使用EventManager管理侧边栏导航事件
    const sidebarNav = document.querySelector('.sidebar-nav');
    if (sidebarNav) {
        EventManager.delegate(sidebarNav, 'a', 'click', function() {
            if (this.textContent.includes('逾期订单') || this.href.includes('overdue')) {
                console.log('侧边栏导航到逾期订单页面，设置标记');
                window.navigatingToOverdue = true;
                window.sessionStorage.setItem('lastInitializedTableId', '');
            }
        });
    }

    // 解决直接通过URL访问带有tab参数的页面的问题
    if (window.location.href.includes('tab=overdue')) {
        console.log('检测到tab=overdue参数，准备额外处理');
        window.overduePageLoaded = true;
    }
});

// DataTable管理器 - 安全的表格管理方案
const DataTableManager = {
    instances: new Map(),
    
    // 安全获取或创建DataTable实例
    getInstance(tableElement, options = {}) {
        if (!tableElement) {
            console.error('DataTableManager: 无效的表格元素');
            return null;
        }
        
        const tableId = tableElement.id || this.generateTableId(tableElement);
        
        // 检查是否已经存在实例
        if (this.instances.has(tableId) && $.fn.DataTable.isDataTable(tableElement)) {
            console.log(`DataTableManager: 返回现有实例 ${tableId}`);
            return this.instances.get(tableId);
        }
        
        // 如果表格已初始化但不在我们的管理中，先销毁
        if ($.fn.DataTable.isDataTable(tableElement)) {
            console.log(`DataTableManager: 销毁未管理的表格实例 ${tableId}`);
            $(tableElement).DataTable().destroy();
        }
        
        // 创建新实例
        try {
            const instance = $(tableElement).DataTable(options);
            this.instances.set(tableId, instance);
            console.log(`DataTableManager: 创建新实例 ${tableId}`);
            return instance;
        } catch (error) {
            console.error(`DataTableManager: 创建实例失败 ${tableId}:`, error);
            return null;
        }
    },
    
    // 销毁指定实例
    destroyInstance(tableElement) {
        const tableId = tableElement.id || this.generateTableId(tableElement);
        
        if (this.instances.has(tableId)) {
            try {
                this.instances.get(tableId).destroy();
                this.instances.delete(tableId);
                console.log(`DataTableManager: 销毁实例 ${tableId}`);
            } catch (error) {
                console.error(`DataTableManager: 销毁实例失败 ${tableId}:`, error);
            }
        }
    },
    
    // 销毁所有实例
    destroyAll() {
        for (const [tableId, instance] of this.instances) {
            try {
                instance.destroy();
                console.log(`DataTableManager: 销毁实例 ${tableId}`);
            } catch (error) {
                console.error(`DataTableManager: 销毁实例失败 ${tableId}:`, error);
            }
        }
        this.instances.clear();
        console.log('DataTableManager: 所有实例已销毁');
    },
    
    // 生成表格ID
    generateTableId(tableElement) {
        return `dt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};

// 重置所有表格的便捷方法 - 使用DataTableManager
function resetAllDataTables() {
    DataTableManager.destroyAll();
    // 额外清理可能遗留的DataTable实例
    $('.data-table').each(function() {
        if ($.fn.DataTable.isDataTable(this)) {
            try {
                $(this).DataTable().destroy();
            } catch (error) {
                console.warn('清理遗留DataTable实例时出错:', error);
            }
        }
    });
    console.log('所有数据表格实例已重置');
}

// 安全初始化表格方法
function safeInitializeDataTable(tableElement, options) {
    if (!tableElement) return null;
    
    // 合并选项
    const defaultOptions = {
        responsive: {
            details: {
                type: 'column',
                target: 'tr'
            }
        },
        columnDefs: [
            {
                className: 'dtr-control',
                orderable: false,
                targets: 0
            }
        ],
        order: [[1, 'asc']],
        dom: '<"dataTable-top"lf>rt<"dataTable-bottom"ip>',
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]],
        language: {
            search: "搜索:",
            lengthMenu: "显示 _MENU_ 条数据",
            info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            infoEmpty: "显示第 0 至 0 项结果，共 0 项",
            infoFiltered: "(由 _MAX_ 项结果过滤)",
            paginate: {
                first: "首页",
                previous: "上页",
                next: "下页",
                last: "末页"
            },
            zeroRecords: "没有匹配结果",
            emptyTable: "暂无数据",
            loadingRecords: "加载中...",
            processing: "处理中..."
        },
        forceReinitialize: true, // 标记为强制重新初始化
        drawCallback: function() {
            // 表格绘制完成后自动应用增强功能
            enhanceDataTableInstance(this);
        }
    };
    
    const mergedOptions = $.extend(true, {}, defaultOptions, options || {});
    
    // 如果表格已初始化，先销毁
    if ($.fn.DataTable.isDataTable(tableElement)) {
        console.log('初始化前销毁现有表格');
        $(tableElement).DataTable().destroy();
        // 移除可能的已初始化标记
        $(tableElement).removeData('dt-initialized');
    }
    
    // 初始化表格
    console.log('安全初始化表格');
    return $(tableElement).DataTable(mergedOptions);
};

// 为表格添加完全重置和重新初始化的方法
function reinitializeTable(tableElement) {
    if (!tableElement) return;
    
    console.log('开始完全重置和重新初始化表格');
    
    // 使用DataTableManager管理实例
    DataTableManager.destroyInstance(tableElement);
    
    // 使用安全初始化方法
    return safeInitializeDataTable(tableElement);
}

// 表格增强命名空间
const TableEnhancer = {
    // 核心功能
    enhance: enhanceDataTables,
    optimizeForMobile: function() {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(function(table) {
            if ($.fn.DataTable.isDataTable(table)) {
                optimizeForMobile(table, $(table).DataTable());
            }
        });
    },
    
    // 表格管理
    initialize: safeInitializeDataTable,
    reinitialize: reinitializeTable,
    reset: resetAllDataTables,
    
    // 工具函数
    processLongText: processLongTextCells,
    enhanceNumeric: enhanceNumericColumns,
    optimizePagination: optimizePagination
};

// 向后兼容的全局导出（逐步移除）
// TODO: 在确保所有引用都已更新后删除这些全局变量
if (typeof window !== 'undefined') {
    window.enhanceDataTables = TableEnhancer.enhance;
    window.initializeDataTable = TableEnhancer.initialize;
    window.reinitializeTable = TableEnhancer.reinitialize;
    window.resetAllDataTables = TableEnhancer.reset;
    window.optimizeForMobile = TableEnhancer.optimizeForMobile;
    
    // 主要命名空间
    window.TableEnhancer = TableEnhancer;
    window.DataTableManager = DataTableManager;
}
