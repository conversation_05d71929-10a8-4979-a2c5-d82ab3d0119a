import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'

// 布局组件
const DefaultLayout = () => import('@/layouts/DefaultLayout.vue')
const AuthLayout = () => import('@/layouts/AuthLayout.vue')

// 页面组件 - 懒加载
const Login = () => import('@/pages/auth/Login.vue')
const Home = () => import('@/pages/dashboard/Home.vue')
const DataQuery = () => import('@/pages/data/Query.vue')
const DataSummary = () => import('@/pages/data/Summary.vue')
const CustomerList = () => import('@/pages/customer/List.vue')
const CustomerDetail = () => import('@/pages/customer/Detail.vue')
const QRCode = () => import('@/pages/tools/QRCode.vue')
const Calculator = () => import('@/pages/tools/Calculator.vue')

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  
  // 认证相关路由
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      {
        path: 'login',
        name: 'Login',
        component: Login,
        meta: {
          title: '用户登录',
          requiresAuth: false,
          hideInMenu: true
        }
      }
    ]
  },
  
  // 主应用路由
  {
    path: '/',
    component: DefaultLayout,
    meta: {
      requiresAuth: true
    },
    children: [
      // 工作台
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Home,
        meta: {
          title: '工作台',
          icon: 'House',
          requiresAuth: true,
          permissions: ['data:read']
        }
      },
      
      // 数据查询模块
      {
        path: 'data',
        name: 'Data',
        meta: {
          title: '数据查询',
          icon: 'Search',
          requiresAuth: true
        },
        children: [
          {
            path: 'query',
            name: 'DataQuery',
            component: DataQuery,
            meta: {
              title: '数据查询',
              requiresAuth: true,
              permissions: ['data:query']
            }
          },
          {
            path: 'summary',
            name: 'DataSummary',
            component: DataSummary,
            meta: {
              title: '数据汇总',
              requiresAuth: true,
              permissions: ['data:read']
            }
          }
        ]
      },
      
      // 客户管理模块
      {
        path: 'customer',
        name: 'Customer',
        meta: {
          title: '客户管理',
          icon: 'User',
          requiresAuth: true,
          permissions: ['customer:read']
        },
        children: [
          {
            path: '',
            name: 'CustomerList',
            component: CustomerList,
            meta: {
              title: '客户列表',
              requiresAuth: true,
              permissions: ['customer:read']
            }
          },
          {
            path: ':customerName',
            name: 'CustomerDetail',
            component: CustomerDetail,
            meta: {
              title: '客户详情',
              requiresAuth: true,
              permissions: ['customer:read'],
              hideInMenu: true
            }
          }
        ]
      },
      
      // 工具模块
      {
        path: 'tools',
        name: 'Tools',
        meta: {
          title: '工具箱',
          icon: 'Tools',
          requiresAuth: true,
          permissions: ['tools:all']
        },
        children: [
          {
            path: 'qrcode',
            name: 'QRCode',
            component: QRCode,
            meta: {
              title: '二维码生成',
              requiresAuth: true,
              permissions: ['tools:all']
            }
          },
          {
            path: 'calculator',
            name: 'Calculator',
            component: Calculator,
            meta: {
              title: '计算器',
              requiresAuth: true,
              permissions: ['tools:all']
            }
          }
        ]
      }
    ]
  },
  
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/pages/error/NotFound.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 设置路由守卫
setupRouterGuards(router)

export default router
