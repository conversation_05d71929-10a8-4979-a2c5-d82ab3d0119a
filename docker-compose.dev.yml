version: '3.8'

services:
  # Flask 后端
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
    volumes:
      - .:/app
      - /app/__pycache__
    command: python run.py
    networks:
      - app-network

  # Vue 前端
  frontend:
    build:
      context: ./frontend-new
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:5000/api
    volumes:
      - ./frontend-new:/app
      - /app/node_modules
    command: npm run dev
    depends_on:
      - backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
