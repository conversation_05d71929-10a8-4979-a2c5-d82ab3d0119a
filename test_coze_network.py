#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业信用查询网络连接测试脚本
用于诊断Docker环境中Coze API连接问题
"""

import requests
import json
import time
import sys
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_basic_https():
    """测试基本HTTPS连接"""
    log_message("=== 测试1: 基本HTTPS连接 ===")
    try:
        response = requests.get('https://api.coze.cn', timeout=10)
        log_message(f"✅ 基本连接成功，状态码: {response.status_code}")
        return True
    except requests.exceptions.Timeout:
        log_message("❌ 连接超时")
        return False
    except requests.exceptions.ConnectionError as e:
        log_message(f"❌ 连接错误: {e}")
        return False
    except Exception as e:
        log_message(f"❌ 其他错误: {e}")
        return False

def test_dns_resolution():
    """测试DNS解析"""
    log_message("=== 测试2: DNS解析 ===")
    try:
        import socket
        ip = socket.gethostbyname('api.coze.cn')
        log_message(f"✅ DNS解析成功: api.coze.cn -> {ip}")
        return True
    except Exception as e:
        log_message(f"❌ DNS解析失败: {e}")
        return False

def test_coze_workflow():
    """测试Coze工作流API"""
    log_message("=== 测试3: Coze工作流API ===")
    
    # API配置
    API_TOKEN = 'pat_7Z6Qv0Mwpm6W5iWip6leB3GJsleuMTxHtlJ90GqXm05AMD7Mtf5lq0zUXXSulGzm'
    WORKFLOW_ID = '7533978519026532387'
    BASE_URL = 'https://api.coze.cn'
    STREAM_URL = f"{BASE_URL}/v1/workflow/stream_run"
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json; charset=utf-8"
    }
    
    payload = {
        "workflow_id": WORKFLOW_ID,
        "parameters": {
            "input": "测试企业",
            "streaming": True,
            "speed": 0.001
        }
    }
    
    try:
        log_message(f"发送请求到: {STREAM_URL}")
        log_message(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        log_message(f"请求体: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            STREAM_URL,
            headers=headers,
            json=payload,
            stream=True,
            timeout=30
        )
        
        log_message(f"响应状态码: {response.status_code}")
        log_message(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            log_message("✅ 工作流API连接成功")
            
            # 读取少量流式数据进行测试
            log_message("尝试读取流式响应...")
            try:
                content_length = 0
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        content_length += len(chunk)
                        log_message(f"接收到数据块，长度: {len(chunk)}")
                        # 只读取前几个块进行测试
                        if content_length > 2048:
                            log_message("✅ 流式数据接收正常，停止测试")
                            break
                return True
            except Exception as stream_error:
                log_message(f"❌ 流式数据读取错误: {stream_error}")
                return False
                
        else:
            log_message(f"❌ API请求失败，状态码: {response.status_code}")
            try:
                error_text = response.text[:1000]  # 只显示前1000字符
                log_message(f"错误内容: {error_text}")
            except:
                log_message("无法读取错误内容")
            return False
            
    except requests.exceptions.Timeout:
        log_message("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError as e:
        log_message(f"❌ 连接错误: {e}")
        return False
    except Exception as e:
        log_message(f"❌ 其他错误: {e}")
        import traceback
        log_message(f"详细错误信息:\n{traceback.format_exc()}")
        return False

def test_simple_post():
    """测试简单的POST请求"""
    log_message("=== 测试4: 简单POST请求 ===")
    try:
        response = requests.post(
            'https://httpbin.org/post',
            json={'test': 'data'},
            timeout=10
        )
        log_message(f"✅ 简单POST请求成功，状态码: {response.status_code}")
        return True
    except Exception as e:
        log_message(f"❌ 简单POST请求失败: {e}")
        return False

def main():
    """主测试函数"""
    log_message("开始网络连接诊断...")
    log_message("=" * 50)
    
    results = []
    
    # 执行所有测试
    results.append(("DNS解析", test_dns_resolution()))
    results.append(("基本HTTPS连接", test_basic_https()))
    results.append(("简单POST请求", test_simple_post()))
    results.append(("Coze工作流API", test_coze_workflow()))
    
    # 输出测试结果摘要
    log_message("=" * 50)
    log_message("测试结果摘要:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        log_message(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    log_message("=" * 50)
    if all_passed:
        log_message("🎉 所有测试通过！网络连接正常。")
        log_message("问题可能出现在应用层面的请求处理或错误处理逻辑中。")
    else:
        log_message("⚠️  部分测试失败，请检查失败的测试项。")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())