// 认证相关类型定义

export interface User {
  id: string
  username: string
  role: UserRole
  permissions: Permission[]
  lastLogin?: string
  isActive: boolean
}

export type UserRole = 'limited' | 'standard' | 'full'

export interface Permission {
  id: string
  name: string
  resource: string
  action: string
}

export interface LoginRequest {
  username: string
  password: string
  captcha: string
  captchaId: string
}

export interface LoginResponse {
  success: boolean
  message: string
  user?: User
  token?: string
  redirectUrl?: string
}

export interface CaptchaResponse {
  captchaId: string
  captchaImage: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  permissions: Permission[]
}

// 权限检查函数类型
export type PermissionChecker = (resource: string, action: string) => boolean

// 角色权限映射
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  limited: [
    'data:read',
    'export:basic'
  ],
  standard: [
    'data:read',
    'data:query',
    'export:basic',
    'export:advanced',
    'customer:read'
  ],
  full: [
    'data:read',
    'data:query',
    'data:manage',
    'export:basic',
    'export:advanced',
    'export:all',
    'customer:read',
    'customer:manage',
    'tools:all',
    'admin:read'
  ]
}

// 路由权限配置
export interface RoutePermission {
  path: string
  requiredPermissions: string[]
  roles: UserRole[]
}

export const ROUTE_PERMISSIONS: RoutePermission[] = [
  {
    path: '/dashboard',
    requiredPermissions: ['data:read'],
    roles: ['limited', 'standard', 'full']
  },
  {
    path: '/data/query',
    requiredPermissions: ['data:query'],
    roles: ['standard', 'full']
  },
  {
    path: '/data/summary',
    requiredPermissions: ['data:read'],
    roles: ['limited', 'standard', 'full']
  },
  {
    path: '/customer',
    requiredPermissions: ['customer:read'],
    roles: ['standard', 'full']
  },
  {
    path: '/tools',
    requiredPermissions: ['tools:all'],
    roles: ['full']
  }
]
