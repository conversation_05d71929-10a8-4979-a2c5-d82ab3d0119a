/**
 * 统一表格管理器
 * 确保日期筛选、客户搜索和逾期订单页面的表格样式和功能完全一致
 */
const UnifiedTableManager = {
    // 客服人员颜色缓存
    servicePersonColors: {},
    
    // 预定义的颜色方案 - 专业且易于区分的颜色
    colorSchemes: [
        { bg: '#e3f2fd', text: '#0d47a1', border: '#1976d2' }, // 蓝色系
        { bg: '#f3e5f5', text: '#4a148c', border: '#7b1fa2' }, // 紫色系
        { bg: '#e8f5e8', text: '#1b5e20', border: '#388e3c' }, // 绿色系
        { bg: '#fff3e0', text: '#e65100', border: '#f57c00' }, // 橙色系
        { bg: '#fce4ec', text: '#880e4f', border: '#c2185b' }, // 粉色系
        { bg: '#e0f2f1', text: '#004d40', border: '#00695c' }, // 青色系
        { bg: '#f1f8e9', text: '#33691e', border: '#689f38' }, // 浅绿色系
        { bg: '#fff8e1', text: '#f57f17', border: '#fbc02d' }, // 黄色系
        { bg: '#efebe9', text: '#3e2723', border: '#5d4037' }, // 棕色系
        { bg: '#e8eaf6', text: '#1a237e', border: '#3f51b5' }, // 靛蓝色系
        { bg: '#fafafa', text: '#212121', border: '#424242' }, // 灰色系
        { bg: '#e1f5fe', text: '#01579b', border: '#0288d1' }  // 浅蓝色系
    ],
    
    // 为客服人员生成唯一颜色
    getServicePersonColor: function(serviceName) {
        if (!serviceName || serviceName === '-') {
            return { bg: '#f8f9fa', text: '#6c757d', border: '#dee2e6' };
        }
        
        // 如果已经为该客服分配了颜色，直接返回
        if (this.servicePersonColors[serviceName]) {
            return this.servicePersonColors[serviceName];
        }
        
        // 使用字符串哈希算法为客服名字生成一个稳定的索引
        let hash = 0;
        for (let i = 0; i < serviceName.length; i++) {
            const char = serviceName.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        // 确保索引为正数，并映射到颜色方案数组
        const colorIndex = Math.abs(hash) % this.colorSchemes.length;
        const selectedColor = this.colorSchemes[colorIndex];
        
        // 缓存颜色方案
        this.servicePersonColors[serviceName] = selectedColor;
        
        return selectedColor;
    },
    
    // 初始化表格
    initializeTable: function(tableElement, tableType = 'default') {
        if (!tableElement) {
            console.error('表格元素不存在');
            return null;
        }

        // 确保表格有正确的类名
        if (!tableElement.classList.contains('data-table')) {
            tableElement.classList.add('data-table');
        }

        // 应用统一的DataTables配置
        const config = this.getDataTablesConfig(tableType);
        
        // 初始化DataTables
        const dataTable = $(tableElement).DataTable(config);

        // 应用样式增强
        this.enhanceTableStyles(tableElement, tableType);

        // 添加响应式处理
        this.setupResponsiveHandlers(dataTable);

        return dataTable;
    },

    // 获取DataTables配置
    getDataTablesConfig: function(tableType) {
        return {
            responsive: {
                details: {
                    type: 'column',
                    target: 0
                }
            },
            columnDefs: [
                {
                    className: 'dtr-control',
                    orderable: false,
                    targets: 0,
                    width: '40px'
                }
            ],
            order: [[1, 'asc']],
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],
            language: {
                url: '/static/vendor/datatables/i18n/zh-Hans.json'
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            processing: true,
            autoWidth: false,
            scrollX: true,
            fixedColumns: {
                leftColumns: 1
            },
            rowCallback: this.getRowCallback(tableType),
            drawCallback: this.getDrawCallback(tableType)
        };
    },

    // 获取行回调函数
    getRowCallback: function(tableType) {
        return function(row, data) {
            // 应用产品类型样式
            const cells = row.getElementsByTagName('td');
            
            for (let i = 0; i < cells.length; i++) {
                const cell = cells[i];
                const badge = cell.querySelector('.badge[data-product-type]');
                
                if (badge) {
                    const productType = badge.getAttribute('data-product-type');
                    if (productType === '电商') {
                        badge.style.backgroundColor = '#cfe2ff';
                        badge.style.color = '#084298';
                        badge.style.borderColor = '#b6d4fe';
                    } else if (productType === '租赁') {
                        badge.style.backgroundColor = '#d1e7dd';
                        badge.style.color = '#0f5132';
                        badge.style.borderColor = '#badbcc';
                    }
                }
            }
            
            return row;
        };
    },

    // 获取绘制回调函数
    getDrawCallback: function(tableType) {
        return function(settings) {
            // 表格绘制完成后的处理
            const tableElement = this.api().table().node();
            
            // 调整列宽
            this.api().columns.adjust();
            
            // 应用长文本展开功能
            UnifiedTableManager.setupExpandableContent(tableElement);
            
            // 自定义控件布局
            UnifiedTableManager.customizeTableControls(tableElement);
        };
    },

    // 增强表格样式
    enhanceTableStyles: function(tableElement, tableType) {
        // 确保表格容器有正确的类
        const wrapper = tableElement.closest('.dataTables_wrapper');
        if (wrapper) {
            wrapper.classList.add('unified-table-wrapper');
        }

        // 应用表格特定样式
        tableElement.classList.add(`table-type-${tableType}`);

        // 确保响应式控制列样式正确
        const controlCells = tableElement.querySelectorAll('td.dtr-control, th.dtr-control');
        controlCells.forEach(cell => {
            cell.style.width = '40px';
            cell.style.minWidth = '40px';
            cell.style.maxWidth = '40px';
            cell.style.textAlign = 'center';
            cell.style.verticalAlign = 'middle';
        });
    },

    // 设置响应式处理器
    setupResponsiveHandlers: function(dataTable) {
        let resizeTimer;
        
        $(window).on('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                if ($.fn.DataTable.isDataTable(dataTable.table().node())) {
                    dataTable.columns.adjust().draw(false);
                    dataTable.responsive.recalc();
                }
            }, 250);
        });

        // 标签页切换时重新计算
        $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function() {
            setTimeout(function() {
                if ($.fn.DataTable.isDataTable(dataTable.table().node())) {
                    dataTable.columns.adjust().draw(false);
                    dataTable.responsive.recalc();
                }
            }, 100);
        });
    },

    // 设置可展开内容
    setupExpandableContent: function(tableElement) {
        const expandableElements = tableElement.querySelectorAll('.expandable-content');
        
        expandableElements.forEach(element => {
            element.addEventListener('click', function() {
                this.classList.toggle('expanded');
            });
        });
    },

    // 自定义表格控件布局
    customizeTableControls: function(tableElement) {
        const wrapper = tableElement.closest('.dataTables_wrapper');
        if (!wrapper) return;

        // 获取控件容器
        const tableTop = wrapper.querySelector('.dataTables_wrapper > .row:first-child');
        if (!tableTop) return;

        // 修改搜索控件
        const searchControl = wrapper.querySelector('.dataTables_filter');
        if (searchControl) {
            const searchLabel = searchControl.querySelector('label');
            if (searchLabel) {
                const inputElement = searchLabel.querySelector('input[type="search"]');
                if (inputElement) {
                    inputElement.classList.add('form-control-sm', 'ms-1');
                    inputElement.style.maxWidth = '200px';
                    inputElement.placeholder = '搜索...';
                }
            }
        }

        // 确保顶部控件容器样式正确
        if (tableTop) {
            tableTop.style.display = 'flex';
            tableTop.style.flexWrap = 'wrap';
            tableTop.style.alignItems = 'center';
            tableTop.style.justifyContent = 'space-between';
            tableTop.style.marginBottom = '1rem';
        }
    },

    // 应用状态样式
    applyStatusStyles: function(tableElement) {
        const rows = tableElement.querySelectorAll('tbody tr[data-status]');
        
        rows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (status) {
                // 移除现有状态类
                row.classList.remove('status-逾期未还', 'status-逾期还款', 'status-账单日', 
                                   'status-按时还款', 'status-提前还款', 'status-催收中');
                
                // 添加新状态类
                row.classList.add(`status-${status.replace(/\s+/g, '-')}`);
            }
        });
    },

    // 更新表格数据并保持样式
    updateTableData: function(tableElement, newData, headers) {
        if (!$.fn.DataTable.isDataTable(tableElement)) {
            console.error('表格未初始化为DataTable');
            return;
        }

        const dataTable = $(tableElement).DataTable();
        
        // 清空现有数据
        dataTable.clear();

        // 添加新数据
        if (newData && newData.length > 0) {
            newData.forEach(item => {
                const rowData = headers.map(header => {
                    let cellValue = item[header] || '';
                    
                    // 应用格式化
                    cellValue = this.formatCellValue(cellValue, header);
                    
                    return cellValue;
                });
                
                dataTable.row.add(rowData);
            });
        }

        // 重新绘制表格
        dataTable.draw();

        // 重新应用样式
        setTimeout(() => {
            this.applyStatusStyles(tableElement);
            this.setupExpandableContent(tableElement);
        }, 100);
    },

    // 格式化单元格值
    formatCellValue: function(value, header) {
        if (!value && value !== 0) return '';

        // 金额字段格式化
        if (['当前待收', '总待收', '成本', '待收金额', '金额'].includes(header)) {
            const numValue = parseFloat(value);
            if (!isNaN(numValue) && numValue > 0) {
                return numValue.toFixed(2);
            }
            return value;
        }

        // 逾期天数和期数字段 - 添加徽章
        if (['逾期天数', '逾期期数'].includes(header)) {
            const numValue = parseInt(value);
            if (!isNaN(numValue) && numValue > 0) {
                return `<span class="badge bg-danger">${value}</span>`;
            }
            return value;
        }

        // 业务和客服字段 - 添加徽章
        if (['业务', '客服'].includes(header)) {
            if (header === '客服') {
                // 为客服字段使用颜色区分
                const serviceColor = this.getServicePersonColor(value);
                return `<span class="badge" style="background-color: ${serviceColor.bg}; color: ${serviceColor.text}; border: 1px solid ${serviceColor.border};">${value}</span>`;
            } else {
                // 业务字段保持原有样式
                return `<span class="badge bg-info">${value}</span>`;
            }
        }

        // 产品和产品类型字段 - 添加徽章
        if (['产品', '产品类型'].includes(header)) {
            if (value.includes('电商')) {
                return `<span class="badge bg-info text-dark" data-product-type="电商">${value}</span>`;
            } else if (value.includes('租赁')) {
                return `<span class="badge bg-info text-dark" data-product-type="租赁">${value}</span>`;
            } else {
                return `<span class="badge bg-info text-dark">${value}</span>`;
            }
        }

        // 日期字段 - 添加徽章
        if (['订单日期', '账单日期', '首次逾期日期'].includes(header)) {
            return `<span class="badge bg-secondary">${value}</span>`;
        }

        // 状态字段 - 添加徽章和颜色
        if (['备注', '贷后状态', '账单状态'].includes(header)) {
            return this.formatStatusValue(value);
        }

        return value;
    },

    // 格式化状态值
    formatStatusValue: function(status) {
        if (!status) return '';

        let badgeClass = 'bg-light text-dark';

        switch(status) {
            case '账单日':
                badgeClass = 'bg-warning text-dark';
                break;
            case '逾期未还':
            case '严重逾期':
                badgeClass = 'bg-danger text-white';
                break;
            case '逾期还款':
            case '轻微逾期':
            case '催收中':
                badgeClass = 'bg-warning text-dark';
                break;
            case '提前还款':
                badgeClass = 'bg-primary text-white';
                break;
            case '按时还款':
            case '已结清':
            case '正常':
                badgeClass = 'bg-success text-white';
                break;
            case '已取消':
                badgeClass = 'bg-secondary text-white';
                break;
            default:
                badgeClass = 'bg-light text-dark';
        }

        return `<span class="badge ${badgeClass}">${status}</span>`;
    }
};

// 全局暴露
window.UnifiedTableManager = UnifiedTableManager;