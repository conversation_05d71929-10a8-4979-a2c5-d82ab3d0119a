{% extends "base.html" %}

{% block title %}太享查询_{{ version }} - 数据汇总{% endblock %}

{% block styles %}
<!-- 引入汇总页面核心样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/pages/summary-page.css') }}">
<!-- 引入增强样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/summary-enhanced.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 引入侧边栏模板 -->
        {% include 'sidebar.html' %}

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                <h1 class="h2">数据汇总分析</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group mr-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportChartsBtn">
                            <i class="bi bi-download"></i> 导出图表
                        </button>
                    </div>
                </div>
            </div>

            <!-- 日期范围选择表单 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('main.summary_view') }}" class="row g-3" data-show-global-loading="true">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date or today }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date or today }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 查询
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row">
                <!-- 订单汇总图表 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">订单月度汇总</h5>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm chart-type-selector" data-chart="orderChart">
                                    <option value="bar">柱状图</option>
                                    <option value="line">折线图</option>
                                    <option value="pie">饼图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="orderChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期汇总图表 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">逾期月度汇总</h5>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm chart-type-selector" data-chart="overdueChart">
                                    <option value="bar">柱状图</option>
                                    <option value="line">折线图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="overdueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格区域 -->
            <div class="row">
                <!-- 订单汇总表格 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">订单数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>月份</th>
                                            <th>电商台数/订单数量</th>
                                            <th>租赁台数/订单数量</th>
                                            <th>总台数/订单数量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if order_summary and order_summary|length > 0 %}
                                            {% for item in order_summary %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td>{{ item.月份|default('未知') }}</td>
                                                <td>{{ item.电商台数|default(item.电商订单数量|default(0)) }}</td>
                                                <td>{{ item.租赁台数|default(item.租赁订单数量|default(0)) }}</td>
                                                <td>{{ (item.电商台数|default(item.电商订单数量|default(0))|int) + (item.租赁台数|default(item.租赁订单数量|default(0))|int) }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td class="text-center auto-dismiss-alert">暂无数据</td>
                                                <td>-</td>
                                                <td>-</td>
                                                <td>-</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期汇总表格 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">逾期数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>月份</th>
                                            <th>电商逾期订单数</th>
                                            <th>租赁逾期订单数</th>
                                            <th>总逾期订单数</th>
                                            <th>逾期金额</th>
                                            <th>逾期率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if overdue_summary and overdue_summary|length > 0 %}
                                            {% for item in overdue_summary %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td>{{ item.月份|default('未知') }}</td>
                                                <td>{{ item.电商逾期订单数量|default(0) }}</td>
                                                <td>{{ item.租赁逾期订单数量|default(0) }}</td>
                                                <td>{{ (item.电商逾期订单数量|default(0)|int) + (item.租赁逾期订单数量|default(0)|int) }}</td>
                                                <td>{{ item.逾期金额|default(0) }}</td>
                                                <td>{{ item.逾期率|default(0) }}%</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td class="text-center auto-dismiss-alert">暂无数据</td>
                                                <td>-</td>
                                                <td>-</td>
                                                <td>-</td>
                                                <td>-</td>
                                                <td>-</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 原始汇总数据表格 -->
            {% if summary_data_results and summary_data_results|length > 0 %}
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">详细汇总数据</h5>
                            <div class="btn-toolbar">
                                <div class="dropdown me-2">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="dataViewSelector" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-grid"></i> 视图
                                    </button>
                                    <ul class="dropdown-menu view-selector" aria-labelledby="dataViewSelector">
                                        <li><a class="dropdown-item active" href="#" data-view="tabs">店铺选项卡</a></li>
                                        <li><a class="dropdown-item" href="#" data-view="table">完整表格</a></li>
                                        <li><a class="dropdown-item" href="#" data-view="cards">指标卡片</a></li>
                                    </ul>
                                </div>
                                <button class="btn btn-sm btn-outline-secondary" id="exportSummaryData">
                                    <i class="bi bi-download"></i> 导出
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 视图内容将通过JavaScript动态加载 -->
                            <div id="summaryDataContainer"></div>
                            
                            <!-- 用于存储汇总数据的隐藏元素 -->
                            <script id="summaryData" type="application/json">
                                {
                                    "headers": {{ summary_data_headers|tojson }},
                                    "summary": {{ summary_data_results|tojson }},
                                    "timing_stats": {{ timing_stats|tojson }}
                                }
                            </script>
                            
                            <!-- 增强的数据解析脚本 -->
                            <script>
                            // 渲染汇总数据表格函数定义
                            function renderSummaryTable(data) {
                                const container = document.getElementById('summaryDataContainer');
                                if (!container) {
                                    console.error('找不到summaryDataContainer元素');
                                    return;
                                }

                                try {
                                    let html = '';
                                    
                                    if (data.summary && data.summary.length > 0) {
                                        html = `
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover" id="summaryTable">
                                                    <thead class="table-dark">
                                                        <tr>`;
                                        
                                        // 渲染表头
                                        if (data.headers && data.headers.length > 0) {
                                            data.headers.forEach(header => {
                                                html += `<th>${header}</th>`;
                                            });
                                        }
                                        
                                        html += `</tr></thead><tbody>`;
                                        
                                        // 渲染数据行
                                        data.summary.forEach(row => {
                                            html += '<tr>';
                                            if (Array.isArray(row)) {
                                                row.forEach(cell => {
                                                    html += `<td>${cell || ''}</td>`;
                                                });
                                            } else {
                                                // 如果行不是数组，尝试按headers顺序获取值
                                                if (data.headers) {
                                                    data.headers.forEach(header => {
                                                        html += `<td>${row[header] || ''}</td>`;
                                                    });
                                                }
                                            }
                                            html += '</tr>';
                                        });
                                        
                                        html += `</tbody></table></div>`;
                                        
                                        // 添加统计信息
                                        if (data.timing_stats) {
                                            html += `
                                                <div class="row mt-3">
                                                    <div class="col-12">
                                                        <div class="alert alert-info">
                                                            <small>
                                                                <strong>数据统计:</strong> 
                                                                共 ${data.summary.length} 条记录
                                                                ${data.timing_stats.total_time ? ` | 查询耗时: ${data.timing_stats.total_time}` : ''}
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            `;
                                        }
                                    } else {
                                        html = `
                                            <div class="alert alert-warning text-center">
                                                <i class="bi bi-info-circle"></i>
                                                暂无汇总数据
                                            </div>
                                        `;
                                    }
                                    
                                    container.innerHTML = html;
                                    
                                    // 如果有数据表格，应用DataTables
                                    const tableElement = document.getElementById('summaryTable');
                                    if (tableElement && typeof $ !== 'undefined' && $.fn.DataTable) {
                                        $(tableElement).DataTable({
                                            language: {
                                                url: '/static/vendor/datatables/i18n/zh-CN.json'
                                            },
                                            responsive: true,
                                            pageLength: 25,
                                            order: []
                                        });
                                    }
                                    
                                    console.log('汇总数据表格渲染完成');
                                    
                                } catch (error) {
                                    console.error('渲染汇总数据表格失败:', error);
                                    container.innerHTML = `
                                        <div class="alert alert-danger text-center">
                                            <i class="bi bi-exclamation-triangle"></i>
                                            数据渲染失败，请刷新页面重试
                                        </div>
                                    `;
                                }
                            }

                            // 安全解析汇总表格数据
                            var summaryTableData = null;
                            try {
                                const rawData = document.getElementById('summaryData').textContent;
                                if (typeof DataValidator !== 'undefined') {
                                    summaryTableData = DataValidator.safeJsonParse(rawData, null, '汇总表格数据');
                                    
                                    if (summaryTableData) {
                                        // 验证表格数据格式
                                        const tableValidation = DataValidator.validateTableData(summaryTableData, '汇总表格');
                                        if (!tableValidation.valid) {
                                            console.error('汇总表格数据验证失败:', tableValidation.errors);
                                            DataValidator.showUserMessage('数据验证', '汇总表格数据格式有误', 'warning');
                                        } else if (tableValidation.warnings.length > 0) {
                                            console.warn('汇总表格数据验证警告:', tableValidation.warnings);
                                        }
                                    }
                                } else {
                                    // 降级处理
                                    summaryTableData = JSON.parse(rawData);
                                }
                            } catch (error) {
                                console.error('解析汇总表格数据失败:', error);
                                if (typeof DataValidator !== 'undefined') {
                                    DataValidator.showUserMessage('数据解析', '汇总表格数据解析失败', 'error');
                                }
                                summaryTableData = { headers: [], summary: [], timing_stats: {} };
                            }
                            
                            // 立即渲染数据到页面
                            if (summaryTableData && summaryTableData.summary && summaryTableData.summary.length > 0) {
                                renderSummaryTable(summaryTableData);
                            }
                            </script>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 全局加载指示器 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- 标记页面需要图表支持 -->
<div data-needs-charts style="display: none;"></div>

<!-- 视图切换功能（使用Bootstrap原生下拉菜单） -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化视图切换功能');
    
    // 等待Bootstrap下拉菜单初始化完成
    setTimeout(function() {
        // 处理视图切换的菜单项点击
        const menuItems = document.querySelectorAll('.dropdown-menu.view-selector .dropdown-item');
        menuItems.forEach(function(item) {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有active类
                menuItems.forEach(function(mi) {
                    mi.classList.remove('active');
                });
                
                // 添加active类到当前项
                this.classList.add('active');
                
                // 调用视图切换函数
                const viewType = this.getAttribute('data-view');
                if (typeof switchDataView === 'function') {
                    switchDataView(viewType);
                    console.log(`切换到视图: ${viewType}`);
                } else {
                    console.warn('switchDataView函数未找到');
                }
                
                // Bootstrap会自动关闭下拉菜单，无需手动处理
            });
        });
        
        console.log('视图切换功能初始化完成');
    }, 100); // 短暂延迟确保Bootstrap初始化完成
});
</script>

<!-- 引入增强型数据表格JavaScript -->
<!-- data-table-enhanced.js已在base.html中引用，无需重复引用 -->
<script src="{{ url_for('static', filename='js/summary-enhanced.js') }}"></script>

<!-- 引入简化版图表系统 -->
<script src="{{ url_for('static', filename='js/simple-chart.js') }}"></script>
<script>
// 图表数据初始化
window.orderChartData = {};
window.overdueChartData = {};

// 从服务器获取订单图表数据
{% if order_chart_data %}
try {
    window.orderChartData = JSON.parse('{{ order_chart_data|tojson }}');
    console.log('订单图表数据加载成功:', window.orderChartData);
} catch(e) {
    console.error('解析订单图表数据失败:', e);
    window.orderChartData = {};
}
{% else %}
console.log('服务器未提供订单图表数据，将使用默认数据');
{% endif %}

// 从服务器获取逾期图表数据
{% if overdue_chart_data %}
try {
    window.overdueChartData = JSON.parse('{{ overdue_chart_data|tojson }}');
    console.log('逾期图表数据加载成功:', window.overdueChartData);
} catch(e) {
    console.error('解析逾期图表数据失败:', e);
    window.overdueChartData = {};
}
{% else %}
console.log('服务器未提供逾期图表数据，将使用默认数据');
{% endif %}

// 简化的页面初始化
function initializePage() {
    console.log('开始初始化汇总页面...');
    
    // 初始化基础功能
    initializeBasicFeatures();
    
    // 初始化图表
    if (typeof initSummaryCharts === 'function') {
        initSummaryCharts();
    } else {
        console.error('图表初始化函数未找到');
    }
    
    console.log('汇总页面初始化完成');
}
function initializeBasicFeatures() {
    console.log('初始化基础功能...');
    
    // 隐藏加载状态指示器
    if (typeof hideLoading === 'function') {
        hideLoading();
    }
    
    // 暂时禁用全局表格管理器，避免冲突
    const originalEnhanceDataTables = window.enhanceDataTables;
    window.enhanceDataTables = function() {
        console.log('汇总页面跳过全局表格增强功能，使用专用初始化');
    };
    
    // 初始化表格 - 使用专门的汇总表格初始化函数
    try {
        if (typeof initSummaryTables === 'function') {
            initSummaryTables();
            console.log('汇总表格初始化完成');
        }
    } catch (error) {
        console.error('汇总表格初始化失败:', error);
    }
    
    // 恢复全局表格管理器（给其他可能的表格使用）
    setTimeout(() => {
        window.enhanceDataTables = originalEnhanceDataTables;
    }, 1000);
    
    // 初始化其他基础功能
    if (typeof checkApiStatus === 'function') {
        checkApiStatus();
    }
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
    
    console.log('基础功能初始化完成');
}
// 处理窗口大小变化
function handleResize() {
    // 简单的窗口大小变化处理
    console.log('窗口大小变化');
}

// 页面加载完成后初始化 - 修复时序问题
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，延迟初始化页面以确保CSS完全加载');
    
    // 等待CSS完全加载和渲染
    setTimeout(() => {
        initializePage();
    }, 200);
});

// 兼容jQuery ready - 避免重复初始化
if (typeof $ !== 'undefined') {
    $(document).ready(function() {
        console.log('jQuery ready - 等待DOM初始化完成');
        // 不在这里重复调用initializePage()，避免重复初始化
    });
}

// 页面完全加载后的最终检查
window.addEventListener('load', function() {
    console.log('页面完全加载，进行最终检查');
    
    // 延迟检查表格状态
    setTimeout(() => {
        const tables = document.querySelectorAll('.data-table');
        let allTablesOk = true;
        
        // 静默检查表格状态
        tables.forEach((table, index) => {
            if (!$.fn.DataTable.isDataTable(table)) {
                console.warn(`表格 #${index+1} 未正确初始化`);
                allTablesOk = false;
            } else {
                try {
                    const dataTable = $(table).DataTable();
                    // 执行一次安全的列宽调整
                    dataTable.columns.adjust();
                } catch (err) {
                    console.warn(`表格 #${index+1} 状态异常`);
                    // 不标记为严重问题，因为表格可能仍然可用
                }
            }
        });
        
        if (allTablesOk) {
            console.log('✅ 所有表格状态正常');
        } else {
            console.log('⚠️ 部分表格存在问题，但不影响基本功能');
        }
    }, 1000);
});

// 优化后的汇总页面表格初始化函数 - 使用统一分页管理器
function initSummaryTables() {
    console.log('开始初始化汇总页面表格');

    // 等待DOM完全渲染和CSS计算完成
    setTimeout(() => {
        const tables = document.querySelectorAll('.data-table');
        console.log(`找到${tables.length}个表格需要初始化`);

        tables.forEach((table, index) => {
            console.log(`初始化表格 #${index+1}`);

            // 确保表格未被初始化或销毁现有实例
            if ($.fn.DataTable.isDataTable(table)) {
                $(table).DataTable().destroy();
                console.log(`表格 #${index+1} 已存在，已销毁`);
            }

            try {
                // 获取表格的实际列数和容器信息
                const tableColumns = table.querySelectorAll('th').length;
                const isMobile = window.innerWidth <= 768;

                console.log(`表格 #${index+1} 初始化: ${tableColumns}列, ${isMobile ? '移动端' : '桌面端'} (${window.innerWidth}px)`);

                // 使用统一分页配置，指定为窄容器
                const summaryTableConfig = window.getSummaryPagePaginationConfig(isMobile, true);

                // 根据表格列数设置响应式优先级
                if (tableColumns === 5) {
                    // 订单表格（5列）
                    summaryTableConfig.columnDefs.push(
                        { responsivePriority: 1, targets: 1 }, // 月份列
                        { responsivePriority: 2, targets: -1 }, // 最后一列
                        { responsivePriority: 3, targets: [2, 3] } // 中间列
                    );
                } else if (tableColumns === 7) {
                    // 逾期表格（7列）
                    summaryTableConfig.columnDefs.push(
                        { responsivePriority: 1, targets: 1 }, // 月份列
                        { responsivePriority: 2, targets: [5, 6] }, // 逾期金额和逾期率
                        { responsivePriority: 3, targets: -1 }, // 最后一列
                        { responsivePriority: 4, targets: [2, 3] }, // 电商和租赁逾期
                        { responsivePriority: 5, targets: 4 } // 总逾期数
                    );
                }

                const dataTable = $(table).DataTable(summaryTableConfig);

                if (dataTable) {
                    console.log(`表格 #${index+1} 初始化成功`);

                    // 添加标记类，防止其他表格管理器重复处理
                    table.classList.add('summary-table-initialized');

                    // 使用统一分页管理器优化分页
                    if (window.paginationManager) {
                        setTimeout(() => {
                            window.paginationManager.enhanceTable(table);
                        }, 100);
                    }

                } else {
                    console.error(`表格 #${index+1} 初始化失败`);
                }
            } catch(err) {
                console.error(`表格 #${index+1} 初始化失败:`, err);
            }
        });

        console.log('汇总表格初始化完成，使用统一分页管理器');

    }, 100); // 等待100ms确保DOM和CSS完全渲染
}

// 图表初始化将由simple-chart.js处理

// 简化的窗口大小变化处理 - 委托给统一分页管理器
function handleResize() {
    // 统一分页管理器会自动处理窗口大小变化
    // 这里只需要处理表格特有的响应式重计算
    clearTimeout(window.resizeTimer);
    window.resizeTimer = setTimeout(function() {
        try {
            const tables = document.querySelectorAll('.data-table.summary-table-initialized');
            tables.forEach((table) => {
                if ($.fn.DataTable.isDataTable(table)) {
                    try {
                        const dataTable = $(table).DataTable();
                        dataTable.columns.adjust();
                        if (dataTable.responsive) {
                            dataTable.responsive.recalc();
                        }
                    } catch (err) {
                        // 静默处理错误
                    }
                }
            });
        } catch(e) {
            console.warn('窗口大小变化处理失败:', e);
        }
    }, 300);
}

// 导出图表功能
function exportCharts() {
    try {
        // 创建一个临时画布
        var tempCanvas = document.createElement('canvas');
        tempCanvas.width = 1200;
        tempCanvas.height = 800;
        
        // 将订单图表合并到临时画布
        if (orderChart) {
            var orderCanvas = document.getElementById('orderChart');
            var ctx = tempCanvas.getContext('2d');
            ctx.drawImage(orderCanvas, 0, 0, tempCanvas.width, tempCanvas.height / 2);
        }
        
        // 将逾期图表合并到临时画布
        if (overdueChart) {
            var overdueCanvas = document.getElementById('overdueChart');
            var ctx = tempCanvas.getContext('2d');
            ctx.drawImage(overdueCanvas, 0, tempCanvas.height / 2, tempCanvas.width, tempCanvas.height / 2);
        }
        
        // 导出为图片
        var link = document.createElement('a');
        link.download = '数据汇总图表_' + new Date().toISOString().slice(0, 10) + '.png';
        link.href = tempCanvas.toDataURL('image/png');
        link.click();
    } catch (err) {
        console.error('导出图表失败:', err);
        alert('导出图表失败，请重试');
    }
}

// 客户搜索函数
function searchCustomer() {
    var customerName = document.getElementById('customerName').value.trim();
    if (!customerName) {
        alert('请输入客户姓名');
        return;
    }
    
    window.location.href = "/?customerName=" + encodeURIComponent(customerName) + "&tab=customer";
}

// 检测API状态
function checkApiStatus() {
    var apiStatusElement = document.getElementById('apiStatus');
    if (!apiStatusElement) return;
    
    // 设置超时时间（5秒）
    const timeout = 5000;
    
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    fetch("{{ url_for('api.ping') }}", {
        signal: controller.signal
    })
    .then(function(response) {
        clearTimeout(timeoutId);
        if (!response.ok) {
            throw new Error('API响应状态: ' + response.status);
        }
        return response.json();
    })
    .then(function(data) {
        if (data.status === 'ok') {
            apiStatusElement.innerHTML = '<span class="badge bg-success">正常</span>';
        } else {
            apiStatusElement.innerHTML = '<span class="badge bg-warning">异常</span>';
        }
    })
    .catch(function(error) {
        clearTimeout(timeoutId);
        console.error('API状态检查失败:', error);
        if (error.name === 'AbortError') {
            apiStatusElement.innerHTML = '<span class="badge bg-danger">超时</span>';
        } else {
            apiStatusElement.innerHTML = '<span class="badge bg-danger">连接失败</span>';
        }
        
        // 5秒后重试
        setTimeout(checkApiStatus, 5000);
    });
}

// 错误处理将由simple-chart.js处理
</script>
{% endblock %}