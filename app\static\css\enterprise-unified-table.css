/**
 * 企业级统一表格样式 - 基于逾期订单页面的优化设计
 * 确保整个项目的表格风格完全一致
 */

/* ==================== 基础表格样式 ==================== */
.data-table {
    width: 100% !important;
    font-size: 0.9rem;
    border-collapse: separate;
    border-spacing: 0;
    font-family: "Microsoft YaHei", sans-serif;
}

/* 表格容器 */
.dataTables_wrapper {
    width: 100% !important;
    margin: 0 auto;
}

/* 表格响应式容器 */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    background-color: #fff;
}

/* ==================== 表头样式 ==================== */
.data-table thead th {
    background-color: #f8f9fa !important;
    color: #495057;
    font-weight: 600;
    text-align: center !important;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid #dee2e6;
    padding: 12px 8px;
    vertical-align: middle !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ==================== 表格内容单元格样式 ==================== */
.data-table tbody td {
    padding: 10px 8px;
    text-align: center !important;
    vertical-align: middle !important;
    font-size: 14px;
    border-bottom: 1px solid #eee;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 奇偶行颜色区分 */
.data-table tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.02);
}

.data-table tbody tr:hover {
    background-color: rgba(0,0,0,.075);
    transition: background-color 0.15s ease;
}

/* ==================== 响应式控制列样式 ==================== */
.data-table td.dtr-control,
.data-table th.dtr-control {
    position: relative;
    text-align: center !important;
    cursor: pointer;
    padding-left: 0;
    padding-right: 0;
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
    vertical-align: middle;
}

/* 展开/折叠按钮样式 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    content: '+';
    background-color: #007bff;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 0.25rem rgba(0, 123, 255, 0.5);
    position: absolute;
    line-height: 1;
    font-size: 14px;
    font-weight: bold;
}

/* 展开状态按钮样式 */
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
    content: '-';
    background-color: #dc3545;
}

/* ==================== 特定字段样式 ==================== */

/* 订单编号列加宽 */
.data-table th:nth-child(2), 
.data-table td:nth-child(2) {
    min-width: 150px;
}

/* 金额字段样式 */
.amount-field {
    font-weight: bold;
    text-align: right !important;
    color: #007bff;
    font-family: Consolas, monospace;
}

/* 分类字段标签样式 */
.category-field .badge {
    font-size: 0.85em;
    font-weight: normal;
    padding: 0.25em 0.6em;
    border-radius: 0.375rem;
}

/* 产品类型字段样式 - 区分不同产品类型 */
.category-field .badge[data-product-type="电商"] {
    background-color: #cfe2ff !important;
    color: #084298 !important;
    border: 1px solid #b6d4fe;
}

.category-field .badge[data-product-type="租赁"] {
    background-color: #d1e7dd !important;
    color: #0f5132 !important;
    border: 1px solid #badbcc;
}

/* 日期字段标签样式 */
.date-field .badge {
    font-size: 0.85em;
    font-weight: normal;
    background-color: #6c757d;
    color: white;
}

/* 状态字段标签样式 */
.status-field .badge {
    font-size: 0.9em;
    font-weight: 500;
}

/* 业务和客服字段样式 */
.category-field .badge.bg-primary {
    background-color: #0d6efd !important;
    color: white !important;
}

/* 危险状态样式 */
.status-field .badge.bg-danger {
    background-color: #dc3545 !important;
    color: white !important;
}

/* ==================== 期数字段优化样式 ==================== */

/* 期数字段容器 */
.installment-field {
    white-space: normal !important;
    min-width: 150px;
    padding: 8px 6px !important;
}

/* 期数显示容器 */
.installment-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 0.85rem;
}

/* 期数日期显示 */
.installment-date {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 2px;
    white-space: nowrap;
}

/* 期数状态徽章基础样式 */
.installment-status {
    font-size: 0.75rem !important;
    font-weight: 500;
    padding: 0.2em 0.5em;
    border-radius: 0.3rem;
    display: inline-flex;
    align-items: center;
    gap: 0.2em;
    text-decoration: none;
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
}

/* 按时还款状态 */
.installment-status.status-ontime {
    background-color: #d1e7dd !important;
    color: #0f5132 !important;
    border-color: #badbcc;
}

.installment-status.status-ontime:hover {
    background-color: #c3e6cb !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(15, 81, 50, 0.15);
}

/* 逾期未还状态 */
.installment-status.status-overdue {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    border-color: #f5c6cb;
    animation: pulse-red 2s infinite;
}

.installment-status.status-overdue:hover {
    background-color: #f1aeb5 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(114, 28, 36, 0.15);
}

/* 逾期还款状态 */
.installment-status.status-late {
    background-color: #fff3cd !important;
    color: #856404 !important;
    border-color: #ffeaa7;
}

.installment-status.status-late:hover {
    background-color: #ffeaa7 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(133, 100, 4, 0.15);
}

/* 提前还款状态 */
.installment-status.status-early {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
    border-color: #b8daff;
}

.installment-status.status-early:hover {
    background-color: #bee5eb !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(12, 84, 96, 0.15);
}

/* 逾期状态闪烁动画 */
@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.1);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* 期数字段图标样式 */
.installment-status i {
    font-size: 0.8em;
    margin-right: 0.1em;
}

/* ==================== 排序图标位置修复 ==================== */
table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:before {
    bottom: 0.5em;
    right: 1em;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
    top: 0.5em;
    right: 1em;
}

/* ==================== 表格控件样式 ==================== */

/* 表格控制区样式 */
.table-controls-row {
    margin-bottom: 15px;
}

/* DataTable控件紧凑布局 */
.dataTable-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0;
    flex-wrap: nowrap;
}

.dataTables_length {
    margin-right: 1rem;
    white-space: nowrap;
}

.dataTables_length select {
    width: auto;
    display: inline-block;
    margin: 0 0.25rem;
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
}

.dataTables_filter {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.dataTables_filter label {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    white-space: nowrap;
}

.dataTables_filter input {
    width: 200px !important;
    display: inline-block;
    margin-left: 0.5rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.dataTables_filter input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 分页控件样式 */
.pagination-container {
    margin-top: 20px;
    margin-bottom: 30px;
}

.dataTables_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    color: #007bff;
    text-decoration: none;
    background-color: white;
    transition: all 0.15s ease;
}

.dataTables_paginate .paginate_button:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.dataTables_paginate .paginate_button.current {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.dataTables_paginate .paginate_button.disabled {
    color: #6c757d;
    background-color: white;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* ==================== 企业级智能分页系统 ==================== */

/* 智能隐藏的页码按钮 */
.dataTables_paginate .paginate_button.mobile-hidden {
    display: none !important;
}

/* 页码指示器样式 */
.current-page-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 0.375rem 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
    min-width: 80px;
    justify-content: center;
    margin: 0 0.25rem;
    white-space: nowrap;
}

/* 省略号指示器样式 */
.ellipsis-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    color: #6c757d;
    font-size: 1.2rem;
    font-weight: bold;
    margin: 0 0.125rem;
    user-select: none;
}

/* 移动端分页容器激活状态 */
.mobile-pagination-active {
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.mobile-pagination-active::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 移动端分页按钮优化 */
.mobile-pagination-active .paginate_button {
    flex-shrink: 0;
    min-width: 36px;
    min-height: 36px;
}

/* 触摸友好的分页控件 */
.mobile-pagination-active .current-page-indicator {
    background: linear-gradient(145deg, #e9ecef, #f8f9fa);
    border-color: #adb5bd;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    cursor: default;
}

.mobile-pagination-active .current-page-indicator i {
    color: #007bff;
    font-size: 0.85rem;
}

/* ==================== 状态行样式 ==================== */
tr[data-status="逾期未还"], 
tr[data-status="严重逾期"] {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

tr[data-status="逾期还款"], 
tr[data-status="轻微逾期"], 
tr[data-status="催收中"] {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

tr[data-status="按时还款"], 
tr[data-status="已结清"], 
tr[data-status="正常"] {
    background-color: rgba(25, 135, 84, 0.05) !important;
}

tr[data-status="提前还款"] {
    background-color: rgba(13, 110, 253, 0.05) !important;
}

tr[data-status="账单日"] {
    background-color: rgba(255, 193, 7, 0.05) !important;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .data-table {
        font-size: 0.85rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 6px 8px;
    }

    .amount-field {
        font-size: 0.9em;
    }

    /* 在移动设备上保持紧凑布局 */
    .dataTable-top {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .dataTables_length,
    .dataTables_filter {
        margin: 0.25rem 0;
    }

    .dataTables_filter input {
        width: 150px !important;
    }

    /* 移动端分页样式调整 */
    .pagination-container .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .pagination-container .page-item {
        margin-bottom: 5px;
    }

    /* 响应式控制按钮移动端优化 */
    table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
    table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
        width: 18px;
        height: 18px;
        font-size: 12px;
    }

    .installment-field {
        min-width: 120px;
        padding: 6px 4px !important;
    }

    .installment-display {
        font-size: 0.8rem;
        gap: 3px;
    }

    .installment-date {
        font-size: 0.75rem;
    }

    .installment-status {
        font-size: 0.7rem !important;
        padding: 0.15em 0.4em;
    }
}

@media (max-width: 576px) {
    .data-table {
        font-size: 0.8rem;
    }
    
    .data-table th, 
    .data-table td {
        padding: 4px 6px;
    }
    
    .dataTables_filter input {
        width: 120px !important;
    }
    
    .category-field .badge,
    .date-field .badge,
    .status-field .badge {
        font-size: 0.75em;
        padding: 0.2em 0.4em;
    }
    
    .installment-field {
        min-width: 100px;
    }
    
    .installment-display {
        font-size: 0.75rem;
    }
    
    .installment-status {
        font-size: 0.65rem !important;
        padding: 0.1em 0.3em;
    }
}

/* ==================== 打印样式 ==================== */
@media print {
    .data-table {
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td {
        padding: 4px;
        border: 1px solid #000;
    }
    
    .table-responsive {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    /* 隐藏控制列 */
    .dtr-control {
        display: none !important;
    }
}

/* ==================== 工具提示样式 ==================== */
.text-nowrap {
    white-space: nowrap !important;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* ==================== 加载状态样式 ==================== */
.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}