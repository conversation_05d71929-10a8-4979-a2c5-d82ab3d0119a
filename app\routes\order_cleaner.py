from flask import (
    Blueprint, render_template, request, redirect,
    flash, send_file
)
from flask_login import login_required, current_user
import pandas as pd
import datetime
import logging
from io import BytesIO

from config import Config

# 配置日志
logger = logging.getLogger(__name__)

# 创建订单清洗蓝图
order_cleaner_bp = Blueprint('order_cleaner', __name__)


@order_cleaner_bp.route('/order_cleaner', methods=['GET', 'POST'])
@login_required
def order_cleaner():
    """订单清洗工具 - 上传Excel文件并按规则处理数据"""
    if request.method == 'POST':
        # 检查是否有文件上传
        if 'file' not in request.files:
            flash('未选择文件', 'danger')
            return redirect(request.url)

        file = request.files['file']

        # 检查文件名是否为空
        if file.filename == '':
            flash('未选择文件', 'danger')
            return redirect(request.url)

        # 检查文件扩展名
        if not file.filename.endswith(('.xlsx', '.xls')):
            flash('请上传Excel文件（.xlsx或.xls格式）', 'danger')
            return redirect(request.url)
        
        try:
            # 读取上传的Excel文件
            df = pd.read_excel(file)
            
            # 检查必要的列是否存在
            required_columns = ['起租日期', '结束日期', '订单ID', '下单姓名', '商品名称', '总租金', '总期数', '用户备注']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                flash(f'上传的文件缺少必要的列: {", ".join(missing_columns)}', 'danger')
                return redirect(request.url)
            
            # 创建结果DataFrame
            result_df = pd.DataFrame()
            
            # 根据规则处理数据
            # 1. 日期：使用起租日期
            result_df['日期'] = df['起租日期']
            
            # 2. 订单编号：使用订单ID
            result_df['订单编号'] = df['订单ID']
            
            # 3. 客户姓名：使用下单姓名
            result_df['客户姓名'] = df['下单姓名']
            
            # 4. 型号：使用商品名称
            result_df['型号'] = df['商品名称']
            
            # 5. 客户属性：空
            result_df['客户属性'] = ''
            
            # 6. 用途：默认填充"自用"
            result_df['用途'] = '自用'
            
            # 7. 计算租期跨度（月）
            def calculate_months_between(start_date, end_date):
                # 确保日期格式正确
                if isinstance(start_date, str):
                    try:
                        start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S').date()
                    except ValueError:
                        try:
                            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
                        except ValueError:
                            return 0
                            
                if isinstance(end_date, str):
                    try:
                        end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S').date()
                    except ValueError:
                        try:
                            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
                        except ValueError:
                            return 0
                
                # 计算月份差
                months = (end_date.year - start_date.year) * 12 + (end_date.month - start_date.month)
                return months
            
            # 计算每行的月份差
            months_diff = df.apply(lambda row: calculate_months_between(row['起租日期'], row['结束日期']), axis=1)
            
            # 8. 还款周期：判断跨度是否大于5个月
            result_df['还款周期'] = months_diff.apply(lambda x: '月还' if x > 5 else '天还')
            
            # 9. 产品：判断跨度是否大于5个月
            result_df['产品'] = months_diff.apply(lambda x: '租赁' if x > 5 else '电商')
            
            # 10. 期数：总期数 + "期"
            result_df['期数'] = df['总期数'].astype(str) + '期'
            
            # 11. 业务：空
            result_df['业务'] = ''
            
            # 12. 计算台数
            def calculate_devices(row):
                if row['产品'] == '租赁':
                    return row['总租金'] / 13644 if row['总租金'] > 0 else 0
                else:  # 电商
                    return row['总租金'] / 11879.4 if row['总租金'] > 0 else 0
            
            # 创建产品列（临时）
            df['产品'] = months_diff.apply(lambda x: '租赁' if x > 5 else '电商')
            
            # 计算台数
            df['台数'] = df.apply(calculate_devices, axis=1)
            result_df['台数'] = df['台数'].round(2)  # 保留两位小数
            
            # 13. 总待收
            def calculate_total_receivable(row):
                if row['产品'] == '租赁':
                    return row['总租金'] + (116 * row['台数'])
                else:  # 电商
                    return row['总租金'] + (119.6 * row['台数'])
            
            result_df['总待收'] = df.apply(calculate_total_receivable, axis=1).round(2)  # 保留两位小数
            
            # 14. 当前待收：空
            result_df['当前待收'] = ''
            
            # 15. 备注：用户备注
            result_df['备注'] = df['用户备注']
            
            # 16. 每期还款金：空
            result_df['每期还款金'] = ''
            
            # 17. 成本：空
            result_df['成本'] = ''
            
            # 18. 店铺归属
            result_df['店铺归属'] = df['产品'].apply(lambda x: '林林租物' if x == '租赁' else '刚刚好物')
            
            # 创建Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                result_df.to_excel(writer, index=False, sheet_name='订单清洗结果')
                # 设置列宽
                worksheet = writer.sheets['订单清洗结果']
                for i, col in enumerate(result_df.columns):
                    worksheet.set_column(i, i, max(len(col) * 1.5, result_df[col].astype(str).map(len).max() * 1.2))
            
            output.seek(0)
            
            # 生成文件名
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'订单清洗结果_{timestamp}.xlsx'
            
            # 返回处理后的文件
            return send_file(
                output,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            
        except Exception as e:
            logger.error(f"订单清洗处理失败: {str(e)}", exc_info=True)
            flash(f'处理失败: {str(e)}', 'danger')
            return redirect(request.url)
    
    # GET请求渲染页面
    return render_template(
        'order_cleaner.html',
        user=current_user,
        version=Config.VERSION
    )
