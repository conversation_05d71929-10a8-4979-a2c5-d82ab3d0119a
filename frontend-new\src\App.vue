<template>
  <div id="app" class="app-container">
    <!-- 全局加载状态 -->
    <div v-if="isInitializing" class="app-loading">
      <div class="loading-content">
        <div class="loading-logo">
          <img src="/logo.png" alt="太享查询系统" class="logo-image" />
        </div>
        <div class="loading-text">
          <h2>太享查询系统</h2>
          <p>正在加载中...</p>
        </div>
        <div class="loading-spinner">
          <el-progress
            :percentage="loadingProgress"
            :show-text="false"
            :stroke-width="3"
            color="#409eff"
          />
        </div>
      </div>
    </div>
    
    <!-- 主应用内容 -->
    <router-view v-else />
    
    <!-- 全局消息提示容器 -->
    <div id="message-container"></div>
    
    <!-- 全局确认对话框容器 -->
    <div id="dialog-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const isInitializing = ref(true)
const loadingProgress = ref(0)

// 初始化应用
const initializeApp = async () => {
  try {
    // 模拟加载进度
    const updateProgress = (progress: number) => {
      loadingProgress.value = progress
    }
    
    updateProgress(20)
    
    // 检查认证状态
    const isAuthenticated = await authStore.checkAuth()
    updateProgress(60)
    
    // 等待路由准备就绪
    await router.isReady()
    updateProgress(80)
    
    // 检查当前路由是否需要认证
    const currentRoute = router.currentRoute.value
    
    if (currentRoute.meta.requiresAuth !== false && !isAuthenticated) {
      // 需要认证但未登录，跳转到登录页
      await router.push({
        name: 'Login',
        query: { redirect: currentRoute.fullPath }
      })
    } else if (currentRoute.name === 'Login' && isAuthenticated) {
      // 已登录但在登录页，跳转到首页
      await router.push('/dashboard')
    }
    
    updateProgress(100)
    
    // 延迟隐藏加载页面，确保过渡效果
    await new Promise(resolve => setTimeout(resolve, 500))
    
  } catch (error) {
    console.error('App initialization failed:', error)
    ElMessage.error('应用初始化失败，请刷新页面重试')
  } finally {
    isInitializing.value = false
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()
  await initializeApp()
})
</script>

<style scoped>
.app-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 加载页面样式 */
.app-loading {
  @apply fixed inset-0 bg-white z-50 flex items-center justify-center;
}

.loading-content {
  @apply text-center;
  max-width: 400px;
  padding: 2rem;
}

.loading-logo {
  @apply mb-6;
}

.logo-image {
  @apply w-16 h-16 mx-auto;
}

.loading-text {
  @apply mb-8;
}

.loading-text h2 {
  @apply text-2xl font-bold text-gray-800 mb-2;
}

.loading-text p {
  @apply text-gray-600;
}

.loading-spinner {
  @apply w-full;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .loading-content {
    padding: 1rem;
  }
  
  .loading-text h2 {
    @apply text-xl;
  }
  
  .logo-image {
    @apply w-12 h-12;
  }
}

/* 全局样式重置 */
:deep(*) {
  box-sizing: border-box;
}

:deep(body) {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Element Plus 样式覆盖 */
:deep(.el-message) {
  min-width: 300px;
  z-index: 9999;
}

:deep(.el-loading-mask) {
  z-index: 9998;
}

:deep(.el-overlay) {
  z-index: 9997;
}

/* 滚动条样式 */
:deep(::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 选择文本样式 */
:deep(::selection) {
  background-color: #409eff;
  color: white;
}

/* 焦点样式 */
:deep(:focus-visible) {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}
</style>
