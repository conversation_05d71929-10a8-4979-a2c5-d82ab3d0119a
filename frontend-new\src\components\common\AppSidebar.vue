<template>
  <div class="app-sidebar" :class="{ 'sidebar-collapsed': collapsed, 'sidebar-mobile': mobile }">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="logo-container" :class="{ 'logo-collapsed': collapsed }">
        <img src="/logo.png" alt="Logo" class="logo-image" />
        <transition name="fade">
          <span v-if="!collapsed" class="logo-text">太享查询系统</span>
        </transition>
      </div>
    </div>
    
    <!-- 导航菜单 -->
    <div class="sidebar-content">
      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed && !mobile"
        :unique-opened="true"
        class="sidebar-menu"
        @select="handleMenuSelect"
      >
        <!-- 工作台 -->
        <el-menu-item index="/dashboard" class="menu-item">
          <el-icon><House /></el-icon>
          <template #title>工作台</template>
        </el-menu-item>
        
        <!-- 数据查询 -->
        <el-sub-menu index="data" class="menu-item">
          <template #title>
            <el-icon><Search /></el-icon>
            <span>数据查询</span>
          </template>
          <el-menu-item index="/data/query">数据查询</el-menu-item>
          <el-menu-item index="/data/summary">数据汇总</el-menu-item>
        </el-sub-menu>
        
        <!-- 客户管理 -->
        <el-menu-item index="/customer" class="menu-item" v-if="hasPermission('customer:read')">
          <el-icon><User /></el-icon>
          <template #title>客户管理</template>
        </el-menu-item>
        
        <!-- 工具箱 -->
        <el-sub-menu index="tools" class="menu-item" v-if="hasPermission('tools:all')">
          <template #title>
            <el-icon><Tools /></el-icon>
            <span>工具箱</span>
          </template>
          <el-menu-item index="/tools/qrcode">二维码生成</el-menu-item>
          <el-menu-item index="/tools/calculator">计算器</el-menu-item>
        </el-sub-menu>
        
        <!-- 系统管理 -->
        <el-sub-menu index="admin" class="menu-item" v-if="hasPermission('admin:read')">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/admin/users">用户管理</el-menu-item>
          <el-menu-item index="/admin/logs">系统日志</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
    
    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 用户信息 -->
      <div class="user-info" :class="{ 'user-collapsed': collapsed }">
        <el-avatar :size="32" :src="userAvatar">
          {{ userInitial }}
        </el-avatar>
        <transition name="fade">
          <div v-if="!collapsed" class="user-details">
            <div class="user-name">{{ authStore.displayName }}</div>
            <div class="user-role">{{ getRoleText(authStore.userRole) }}</div>
          </div>
        </transition>
        <transition name="fade">
          <el-dropdown v-if="!collapsed" @command="handleUserAction">
            <el-icon class="user-action"><MoreFilled /></el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </transition>
      </div>
      
      <!-- 折叠按钮 -->
      <div v-if="!mobile" class="collapse-btn" @click="toggleCollapse">
        <el-icon>
          <component :is="collapsed ? 'Expand' : 'Fold'" />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  House,
  Search,
  User,
  Tools,
  Setting,
  MoreFilled,
  Expand,
  Fold
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import type { UserRole } from '@/types/auth'

interface Props {
  collapsed: boolean
  mobile: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'menu-select': [path: string]
  'toggle-collapse': []
}>()

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 计算属性
const activeMenu = computed(() => {
  const path = route.path
  
  // 处理子路由的激活状态
  if (path.startsWith('/data/')) {
    return path
  } else if (path.startsWith('/tools/')) {
    return path
  } else if (path.startsWith('/admin/')) {
    return path
  }
  
  return path
})

const userAvatar = computed(() => {
  return authStore.user?.avatar || ''
})

const userInitial = computed(() => {
  return authStore.user?.username?.charAt(0).toUpperCase() || 'U'
})

// 方法
const hasPermission = (permission: string) => {
  const [resource, action] = permission.split(':')
  return authStore.hasPermission(resource, action)
}

const getRoleText = (role: UserRole | null) => {
  const roleMap: Record<string, string> = {
    limited: '有限权限',
    standard: '标准权限',
    full: '完全权限'
  }
  return role ? roleMap[role] : '未知角色'
}

const handleMenuSelect = (path: string) => {
  emit('menu-select', path)
}

const handleUserAction = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await authStore.logout()
        router.push('/auth/login')
      } catch (error) {
        console.error('Logout failed:', error)
      }
      break
  }
}

const toggleCollapse = () => {
  emit('toggle-collapse')
}
</script>

<style scoped>
.app-sidebar {
  @apply h-full bg-white border-r border-gray-200 flex flex-col;
  width: 240px;
  transition: width 0.3s ease;
}

.sidebar-collapsed {
  width: 64px;
}

.sidebar-mobile {
  width: 280px;
}

/* 侧边栏头部 */
.sidebar-header {
  @apply p-4 border-b border-gray-100;
}

.logo-container {
  @apply flex items-center gap-3;
  transition: all 0.3s ease;
}

.logo-collapsed {
  @apply justify-center;
}

.logo-image {
  @apply w-8 h-8 flex-shrink-0;
}

.logo-text {
  @apply text-lg font-semibold text-gray-800 truncate;
}

/* 侧边栏内容 */
.sidebar-content {
  @apply flex-1 overflow-y-auto;
}

.sidebar-menu {
  @apply border-none;
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  @apply h-12 leading-12;
  border-radius: 6px;
  margin: 4px 8px;
  transition: all 0.3s ease;
}

.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  @apply bg-blue-50 text-blue-600;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  @apply bg-blue-500 text-white;
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item) {
  @apply h-10 leading-10 ml-4;
  border-radius: 4px;
  margin: 2px 8px 2px 12px;
}

/* 侧边栏底部 */
.sidebar-footer {
  @apply p-4 border-t border-gray-100 space-y-3;
}

.user-info {
  @apply flex items-center gap-3;
  transition: all 0.3s ease;
}

.user-collapsed {
  @apply justify-center;
}

.user-details {
  @apply flex-1 min-w-0;
}

.user-name {
  @apply text-sm font-medium text-gray-800 truncate;
}

.user-role {
  @apply text-xs text-gray-500 truncate;
}

.user-action {
  @apply text-gray-400 hover:text-gray-600 cursor-pointer;
}

.collapse-btn {
  @apply flex items-center justify-center w-8 h-8 rounded bg-gray-100 hover:bg-gray-200 cursor-pointer transition-colors;
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .sidebar-mobile .sidebar-menu :deep(.el-menu-item),
  .sidebar-mobile .sidebar-menu :deep(.el-sub-menu__title) {
    @apply h-14 leading-14 text-base;
  }
  
  .sidebar-mobile .user-info {
    @apply p-2;
  }
  
  .sidebar-mobile .logo-container {
    @apply p-2;
  }
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  .app-sidebar {
    @apply bg-gray-800 border-gray-700;
  }
  
  .logo-text {
    @apply text-white;
  }
  
  .sidebar-menu :deep(.el-menu-item),
  .sidebar-menu :deep(.el-sub-menu__title) {
    @apply text-gray-300;
  }
  
  .sidebar-menu :deep(.el-menu-item:hover),
  .sidebar-menu :deep(.el-sub-menu__title:hover) {
    @apply bg-gray-700 text-blue-400;
  }
  
  .user-name {
    @apply text-gray-200;
  }
  
  .user-role {
    @apply text-gray-400;
  }
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
</style>
