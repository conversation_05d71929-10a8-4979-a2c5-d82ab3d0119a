import { ref, computed, onMounted, onUnmounted } from 'vue'

// 响应式断点配置
const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
}

export function useResponsive() {
  const windowWidth = ref(0)
  const windowHeight = ref(0)

  // 更新窗口尺寸
  const updateSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }

  // 计算当前断点
  const currentBreakpoint = computed(() => {
    const width = windowWidth.value
    
    if (width >= breakpoints['2xl']) return '2xl'
    if (width >= breakpoints.xl) return 'xl'
    if (width >= breakpoints.lg) return 'lg'
    if (width >= breakpoints.md) return 'md'
    if (width >= breakpoints.sm) return 'sm'
    return 'xs'
  })

  // 各种尺寸判断
  const isMobile = computed(() => windowWidth.value < breakpoints.md)
  const isTablet = computed(() => 
    windowWidth.value >= breakpoints.md && windowWidth.value < breakpoints.lg
  )
  const isDesktop = computed(() => windowWidth.value >= breakpoints.lg)
  const isSmallScreen = computed(() => windowWidth.value < breakpoints.lg)
  const isLargeScreen = computed(() => windowWidth.value >= breakpoints.xl)

  // 断点匹配函数
  const matches = (breakpoint: keyof typeof breakpoints) => {
    return computed(() => windowWidth.value >= breakpoints[breakpoint])
  }

  // 范围匹配函数
  const between = (min: keyof typeof breakpoints, max: keyof typeof breakpoints) => {
    return computed(() => 
      windowWidth.value >= breakpoints[min] && windowWidth.value < breakpoints[max]
    )
  }

  // 获取响应式列数
  const getResponsiveCols = (config: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
  }) => {
    return computed(() => {
      const bp = currentBreakpoint.value
      return config[bp] || config.md || config.sm || config.xs || 1
    })
  }

  // 获取响应式间距
  const getResponsiveSpacing = (config: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
  }) => {
    return computed(() => {
      const bp = currentBreakpoint.value
      return config[bp] || config.md || config.sm || config.xs || 16
    })
  }

  // 生命周期钩子
  onMounted(() => {
    updateSize()
    window.addEventListener('resize', updateSize, { passive: true })
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateSize)
  })

  return {
    // 窗口尺寸
    windowWidth: readonly(windowWidth),
    windowHeight: readonly(windowHeight),
    
    // 当前断点
    currentBreakpoint,
    
    // 尺寸判断
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isLargeScreen,
    
    // 工具函数
    matches,
    between,
    getResponsiveCols,
    getResponsiveSpacing,
    
    // 断点常量
    breakpoints: readonly(breakpoints)
  }
}

// 全局响应式实例（可选）
let globalResponsive: ReturnType<typeof useResponsive> | null = null

export function useGlobalResponsive() {
  if (!globalResponsive) {
    globalResponsive = useResponsive()
  }
  return globalResponsive
}
