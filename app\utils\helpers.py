"""
辅助函数模块，提供各种通用功能
"""
from flask import current_app
import datetime

def status_class_filter(status):
    """
    根据状态返回对应的CSS类名
    
    Args:
        status: 状态文本
    
    Returns:
        对应的CSS类名
    """
    status_mapping = {
        '逾期还款': 'status-overdue',
        '提前还款': 'status-early',
        '按时还款': 'status-ontime',
        '账单日': 'status-upcoming',
        '逾期未还': 'status-overdue',
        '催收': 'status-collection',
        '诉讼': 'status-litigation'
    }
    
    return status_mapping.get(status, '')

def format_date(date_str, format_in='%Y-%m-%d', format_out='%Y-%m-%d'):
    """
    格式化日期字符串
    
    Args:
        date_str: 日期字符串
        format_in: 输入日期格式
        format_out: 输出日期格式
    
    Returns:
        格式化后的日期字符串
    """
    try:
        if not date_str:
            return ''
        date_obj = datetime.datetime.strptime(date_str, format_in)
        return date_obj.strftime(format_out)
    except Exception:
        return date_str

def format_currency(amount):
    """
    格式化货币金额
    
    Args:
        amount: 金额数值
    
    Returns:
        格式化后的金额字符串
    """
    try:
        return f"¥{float(amount):,.2f}"
    except (ValueError, TypeError):
        return str(amount)

def register_template_filters(app):
    """
    注册模板过滤器
    
    Args:
        app: Flask应用实例
    """
    app.jinja_env.filters['status_class'] = status_class_filter
    app.jinja_env.filters['format_date'] = format_date
    app.jinja_env.filters['format_currency'] = format_currency 