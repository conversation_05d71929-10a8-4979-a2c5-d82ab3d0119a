import os
import logging
from logging.handlers import RotatingFileHandler

class Config:
    # ... 其他配置 ...
    
    @staticmethod
    def init_app(app):
        # 配置日志
        if not os.path.exists('logs'):
            os.mkdir('logs')
            
        # 文件处理器 - 记录所有级别的日志
        file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器 - 只记录INFO及以上级别的日志
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s'
        ))
        console_handler.setLevel(logging.INFO)
        
        # 移除所有现有的处理器
        app.logger.handlers = []
        
        # 添加处理器
        app.logger.addHandler(file_handler)
        app.logger.addHandler(console_handler)
        
        # 设置日志级别
        app.logger.setLevel(logging.INFO)
        
        app.logger.info('应用启动')
        
        # 记录API密钥（仅记录前几位）
        api_key = app.config.get('API_KEY', '')
        if api_key:
            app.logger.info(f'API密钥已加载: {api_key[:4]}...')
        else:
            app.logger.warning('未找到API密钥') 