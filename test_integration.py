#!/usr/bin/env python3
"""
集成测试脚本 - 测试前后端连接
"""

import requests
import time
import sys
from urllib.parse import urljoin

# 配置
BACKEND_URL = "http://localhost:5000"
FRONTEND_URL = "http://localhost:3000"

def test_backend_health():
    """测试后端健康状态"""
    try:
        response = requests.get(f"{BACKEND_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False

def test_frontend_health():
    """测试前端健康状态"""
    try:
        response = requests.get(f"{FRONTEND_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 前端服务连接失败: {e}")
        return False

def test_api_endpoints():
    """测试主要API端点"""
    endpoints = [
        "/api/auth/captcha",
        "/api/data/stats",
        "/api/data/last-update"
    ]
    
    success_count = 0
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BACKEND_URL}{endpoint}", timeout=5)
            if response.status_code in [200, 401]:  # 401也是正常的（未认证）
                print(f"✅ API端点正常: {endpoint}")
                success_count += 1
            else:
                print(f"❌ API端点异常: {endpoint} - {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ API端点连接失败: {endpoint} - {e}")
    
    return success_count == len(endpoints)

def test_cors():
    """测试CORS配置"""
    try:
        headers = {
            'Origin': FRONTEND_URL,
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options(f"{BACKEND_URL}/api/auth/captcha", headers=headers, timeout=5)
        
        if 'Access-Control-Allow-Origin' in response.headers:
            print("✅ CORS配置正常")
            return True
        else:
            print("❌ CORS配置可能有问题")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ CORS测试失败: {e}")
        return False

def wait_for_services():
    """等待服务启动"""
    print("等待服务启动...")
    
    backend_ready = False
    frontend_ready = False
    
    for i in range(30):  # 等待最多30秒
        if not backend_ready:
            try:
                response = requests.get(f"{BACKEND_URL}/", timeout=2)
                backend_ready = True
                print("后端服务已启动")
            except:
                pass
        
        if not frontend_ready:
            try:
                response = requests.get(f"{FRONTEND_URL}/", timeout=2)
                frontend_ready = True
                print("前端服务已启动")
            except:
                pass
        
        if backend_ready and frontend_ready:
            break
        
        time.sleep(1)
        print(f"等待中... ({i+1}/30)")
    
    return backend_ready and frontend_ready

def main():
    """主测试函数"""
    print("🚀 开始集成测试")
    print("=" * 50)
    
    # 等待服务启动
    if not wait_for_services():
        print("❌ 服务启动超时")
        sys.exit(1)
    
    # 测试各个组件
    tests = [
        ("后端健康检查", test_backend_health),
        ("前端健康检查", test_frontend_health),
        ("API端点测试", test_api_endpoints),
        ("CORS配置测试", test_cors)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"   测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
