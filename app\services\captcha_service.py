"""
验证码生成与验证服务
"""
import io
import random
import string
import base64
from captcha.image import ImageCaptcha
from flask import session


class CaptchaService:
    """验证码服务类"""
    
    @staticmethod
    def generate_captcha_code(length=4):
        """生成随机验证码字符串
        
        Args:
            length: 验证码长度，默认为4
            
        Returns:
            随机字符串
        """
        # 生成包含数字和大写字母的验证码
        characters = string.ascii_uppercase + string.digits
        # 排除容易混淆的字符
        characters = characters.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
        
        return ''.join(random.choice(characters) for _ in range(length))
    
    @staticmethod
    def generate_captcha_image(code):
        """生成验证码图片
        
        Args:
            code: 验证码字符串
            
        Returns:
            验证码图片的base64编码
        """
        # 创建验证码图像生成器
        image = ImageCaptcha(width=160, height=60)
        
        # 生成验证码图片数据
        image_bytes = io.BytesIO()
        image.write(code, image_bytes)
        image_bytes.seek(0)
        
        # 返回base64编码
        return 'data:image/png;base64,' + base64.b64encode(image_bytes.getvalue()).decode('utf-8')
    
    @staticmethod
    def generate_and_save():
        """生成验证码并保存到session
        
        Returns:
            验证码图片的base64编码
        """
        # 生成随机验证码
        code = CaptchaService.generate_captcha_code()
        
        # 保存到session
        session['captcha_code'] = code
        
        # 生成图片
        return CaptchaService.generate_captcha_image(code)
    
    @staticmethod
    def validate(user_input):
        """验证用户输入的验证码
        
        Args:
            user_input: 用户输入的验证码
            
        Returns:
            bool: 验证结果
        """
        if not user_input or not session.get('captcha_code'):
            return False
        
        # 不区分大小写进行比较
        return user_input.upper() == session.get('captcha_code').upper()
