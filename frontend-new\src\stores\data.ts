import { defineStore } from 'pinia'
import type { 
  OrderData, 
  CustomerSummary, 
  QueryParams, 
  SummaryStats,
  ChartData,
  PaginationConfig
} from '@/types/data'
import { dataApi } from '@/api/data'

interface DataState {
  // 订单数据
  orders: OrderData[]
  ordersLoading: boolean
  ordersTotal: number
  
  // 客户汇总数据
  customerSummary: CustomerSummary | null
  customerSummaryLoading: boolean
  
  // 汇总统计
  summaryStats: SummaryStats | null
  summaryStatsLoading: boolean
  
  // 图表数据
  chartData: {
    orders: ChartData | null
    overdue: ChartData | null
    finance: ChartData | null
  }
  chartLoading: boolean
  
  // 查询参数
  queryParams: QueryParams
  
  // 分页配置
  pagination: PaginationConfig
  
  // 缓存
  cache: Map<string, any>
  cacheExpiry: Map<string, number>
}

export const useDataStore = defineStore('data', {
  state: (): DataState => ({
    orders: [],
    ordersLoading: false,
    ordersTotal: 0,
    
    customerSummary: null,
    customerSummaryLoading: false,
    
    summaryStats: null,
    summaryStatsLoading: false,
    
    chartData: {
      orders: null,
      overdue: null,
      finance: null
    },
    chartLoading: false,
    
    queryParams: {
      page: 1,
      pageSize: 20,
      sortBy: 'orderDate',
      sortOrder: 'desc'
    },
    
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0,
      pageSizes: [10, 20, 50, 100]
    },
    
    cache: new Map(),
    cacheExpiry: new Map()
  }),

  getters: {
    // 获取逾期订单
    overdueOrders: (state): OrderData[] => {
      return state.orders.filter(order => order.status === 'overdue')
    },
    
    // 获取活跃订单
    activeOrders: (state): OrderData[] => {
      return state.orders.filter(order => order.status === 'active')
    },
    
    // 计算总金额
    totalFinanceAmount: (state): number => {
      return state.orders.reduce((sum, order) => sum + order.totalFinance, 0)
    },
    
    // 计算待收金额
    totalReceivableAmount: (state): number => {
      return state.orders.reduce((sum, order) => sum + order.currentReceivable, 0)
    },
    
    // 检查是否有数据
    hasData: (state): boolean => {
      return state.orders.length > 0
    },
    
    // 检查是否正在加载
    isLoading: (state): boolean => {
      return state.ordersLoading || state.customerSummaryLoading || 
             state.summaryStatsLoading || state.chartLoading
    }
  },

  actions: {
    // 查询订单数据
    async fetchOrders(params?: Partial<QueryParams>) {
      const queryKey = this.generateCacheKey('orders', params)
      
      // 检查缓存
      if (this.isCacheValid(queryKey)) {
        const cached = this.cache.get(queryKey)
        this.orders = cached.orders
        this.ordersTotal = cached.total
        this.updatePagination(cached.pagination)
        return
      }
      
      this.ordersLoading = true
      
      try {
        const mergedParams = { ...this.queryParams, ...params }
        const response = await dataApi.getOrders(mergedParams)
        
        if (response.success && response.data) {
          this.orders = response.data.orders
          this.ordersTotal = response.data.total
          this.updatePagination(response.data.pagination)
          
          // 缓存数据
          this.setCache(queryKey, {
            orders: this.orders,
            total: this.ordersTotal,
            pagination: this.pagination
          })
        }
      } catch (error) {
        console.error('Fetch orders error:', error)
        ElMessage.error('获取订单数据失败')
      } finally {
        this.ordersLoading = false
      }
    },

    // 获取客户汇总
    async fetchCustomerSummary(customerName: string) {
      const queryKey = this.generateCacheKey('customer', { customerName })
      
      if (this.isCacheValid(queryKey)) {
        this.customerSummary = this.cache.get(queryKey)
        return
      }
      
      this.customerSummaryLoading = true
      
      try {
        const response = await dataApi.getCustomerSummary(customerName)
        
        if (response.success && response.data) {
          this.customerSummary = response.data
          this.setCache(queryKey, response.data)
        }
      } catch (error) {
        console.error('Fetch customer summary error:', error)
        ElMessage.error('获取客户汇总失败')
      } finally {
        this.customerSummaryLoading = false
      }
    },

    // 获取汇总统计
    async fetchSummaryStats(params?: Partial<QueryParams>) {
      const queryKey = this.generateCacheKey('summary', params)
      
      if (this.isCacheValid(queryKey)) {
        this.summaryStats = this.cache.get(queryKey)
        return
      }
      
      this.summaryStatsLoading = true
      
      try {
        const response = await dataApi.getSummaryStats(params)
        
        if (response.success && response.data) {
          this.summaryStats = response.data
          this.setCache(queryKey, response.data)
        }
      } catch (error) {
        console.error('Fetch summary stats error:', error)
        ElMessage.error('获取汇总统计失败')
      } finally {
        this.summaryStatsLoading = false
      }
    },

    // 获取图表数据
    async fetchChartData(type: 'orders' | 'overdue' | 'finance', params?: any) {
      this.chartLoading = true
      
      try {
        const response = await dataApi.getChartData(type, params)
        
        if (response.success && response.data) {
          this.chartData[type] = response.data
        }
      } catch (error) {
        console.error(`Fetch ${type} chart data error:`, error)
        ElMessage.error('获取图表数据失败')
      } finally {
        this.chartLoading = false
      }
    },

    // 更新查询参数
    updateQueryParams(params: Partial<QueryParams>) {
      this.queryParams = { ...this.queryParams, ...params }
    },

    // 更新分页配置
    updatePagination(pagination: Partial<PaginationConfig>) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 重置数据
    resetData() {
      this.orders = []
      this.ordersTotal = 0
      this.customerSummary = null
      this.summaryStats = null
      this.chartData = {
        orders: null,
        overdue: null,
        finance: null
      }
    },

    // 清除缓存
    clearCache() {
      this.cache.clear()
      this.cacheExpiry.clear()
    },

    // 生成缓存键
    generateCacheKey(type: string, params?: any): string {
      return `${type}_${JSON.stringify(params || {})}`
    },

    // 设置缓存
    setCache(key: string, data: any, ttl: number = 5 * 60 * 1000) {
      this.cache.set(key, data)
      this.cacheExpiry.set(key, Date.now() + ttl)
    },

    // 检查缓存是否有效
    isCacheValid(key: string): boolean {
      const expiry = this.cacheExpiry.get(key)
      if (!expiry || Date.now() > expiry) {
        this.cache.delete(key)
        this.cacheExpiry.delete(key)
        return false
      }
      return this.cache.has(key)
    }
  }
})
