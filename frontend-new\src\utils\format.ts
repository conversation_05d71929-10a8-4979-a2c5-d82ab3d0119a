import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'

// 配置dayjs
dayjs.locale('zh-cn')
dayjs.extend(relativeTime)
dayjs.extend(duration)

/**
 * 格式化金额
 * @param amount 金额
 * @param options 格式化选项
 */
export function formatCurrency(
  amount: number | string,
  options: {
    currency?: string
    minimumFractionDigits?: number
    maximumFractionDigits?: number
    showSymbol?: boolean
  } = {}
): string {
  const {
    currency = 'CNY',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    showSymbol = true
  } = options

  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(num)) return '0.00'

  const formatter = new Intl.NumberFormat('zh-CN', {
    style: showSymbol ? 'currency' : 'decimal',
    currency,
    minimumFractionDigits,
    maximumFractionDigits
  })

  return formatter.format(num)
}

/**
 * 格式化数字
 * @param num 数字
 * @param precision 精度
 */
export function formatNumber(
  num: number | string,
  precision: number = 0
): string {
  const n = typeof num === 'string' ? parseFloat(num) : num
  
  if (isNaN(n)) return '0'

  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  }).format(n)
}

/**
 * 格式化百分比
 * @param value 值
 * @param precision 精度
 */
export function formatPercent(
  value: number | string,
  precision: number = 2
): string {
  const num = typeof value === 'string' ? parseFloat(value) : value
  
  if (isNaN(num)) return '0%'

  return new Intl.NumberFormat('zh-CN', {
    style: 'percent',
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  }).format(num / 100)
}

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 */
export function formatDate(
  date: string | number | Date | dayjs.Dayjs,
  format: string = 'YYYY-MM-DD'
): string {
  if (!date) return ''
  
  try {
    return dayjs(date).format(format)
  } catch {
    return ''
  }
}

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式
 */
export function formatDateTime(
  date: string | number | Date | dayjs.Dayjs,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  return formatDate(date, format)
}

/**
 * 格式化相对时间
 * @param date 日期
 */
export function formatRelativeTime(
  date: string | number | Date | dayjs.Dayjs
): string {
  if (!date) return ''
  
  try {
    return dayjs(date).fromNow()
  } catch {
    return ''
  }
}

/**
 * 格式化时长
 * @param seconds 秒数
 */
export function formatDuration(seconds: number): string {
  if (!seconds || seconds < 0) return '0秒'
  
  const duration = dayjs.duration(seconds, 'seconds')
  
  if (duration.asDays() >= 1) {
    return `${Math.floor(duration.asDays())}天${duration.hours()}小时`
  } else if (duration.asHours() >= 1) {
    return `${duration.hours()}小时${duration.minutes()}分钟`
  } else if (duration.asMinutes() >= 1) {
    return `${duration.minutes()}分钟${duration.seconds()}秒`
  } else {
    return `${duration.seconds()}秒`
  }
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param precision 精度
 */
export function formatFileSize(
  bytes: number,
  precision: number = 2
): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(precision))} ${sizes[i]}`
}

/**
 * 格式化手机号
 * @param phone 手机号
 */
export function formatPhone(phone: string): string {
  if (!phone) return ''
  
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  
  return phone
}

/**
 * 脱敏手机号
 * @param phone 手机号
 */
export function maskPhone(phone: string): string {
  if (!phone) return ''
  
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
  
  return phone
}

/**
 * 脱敏身份证号
 * @param idCard 身份证号
 */
export function maskIdCard(idCard: string): string {
  if (!idCard) return ''
  
  if (idCard.length === 18) {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  } else if (idCard.length === 15) {
    return idCard.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2')
  }
  
  return idCard
}

/**
 * 脱敏银行卡号
 * @param cardNumber 银行卡号
 */
export function maskBankCard(cardNumber: string): string {
  if (!cardNumber) return ''
  
  const cleaned = cardNumber.replace(/\s/g, '')
  
  if (cleaned.length >= 16) {
    return cleaned.replace(/(\d{4})\d+(\d{4})/, '$1 **** **** $2')
  }
  
  return cardNumber
}

/**
 * 格式化订单状态
 * @param status 状态
 */
export function formatOrderStatus(status: string): {
  text: string
  type: 'success' | 'warning' | 'danger' | 'info'
} {
  const statusMap: Record<string, { text: string; type: any }> = {
    active: { text: '进行中', type: 'success' },
    completed: { text: '已完成', type: 'info' },
    cancelled: { text: '已取消', type: 'danger' },
    overdue: { text: '逾期', type: 'warning' }
  }
  
  return statusMap[status] || { text: status, type: 'info' }
}

/**
 * 格式化逾期天数
 * @param days 天数
 */
export function formatOverdueDays(days: number): string {
  if (days <= 0) return '正常'
  
  if (days === 1) return '逾期1天'
  
  return `逾期${days}天`
}

/**
 * 千分位格式化
 * @param num 数字
 */
export function formatThousands(num: number | string): string {
  const n = typeof num === 'string' ? parseFloat(num) : num
  
  if (isNaN(n)) return '0'
  
  return n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 首字母大写
 * @param str 字符串
 */
export function capitalize(str: string): string {
  if (!str) return ''
  
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 截断文本
 * @param text 文本
 * @param length 长度
 * @param suffix 后缀
 */
export function truncate(
  text: string,
  length: number = 50,
  suffix: string = '...'
): string {
  if (!text) return ''
  
  if (text.length <= length) return text
  
  return text.slice(0, length) + suffix
}
