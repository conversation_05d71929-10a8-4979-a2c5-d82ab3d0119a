<template>
  <div class="query-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">数据查询</h1>
        <p class="page-subtitle">查询订单数据、客户信息和财务记录</p>
      </div>
      <div class="header-actions">
        <el-button :icon="Refresh" @click="handleRefresh">
          刷新
        </el-button>
        <el-button type="primary" :icon="Download" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm
      :fields="searchFields"
      :quick-filters="quickFilters"
      :loading="searchLoading"
      :default-values="defaultSearchValues"
      @search="handleSearch"
      @reset="handleReset"
      @change="handleSearchChange"
    >
      <template #actions="{ formData }">
        <el-button
          type="success"
          :icon="DataAnalysis"
          @click="handleAnalyze(formData)"
        >
          数据分析
        </el-button>
      </template>
    </SearchForm>
    
    <!-- 数据表格 -->
    <DataTable
      :data="tableData"
      :columns="tableColumns"
      :loading="tableLoading"
      :pagination="pagination"
      :search-config="tableSearchConfig"
      :quick-filters="tableQuickFilters"
      :selectable="true"
      :exportable="true"
      @search="handleTableSearch"
      @refresh="handleTableRefresh"
      @export="handleTableExport"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @page-change="handlePageChange"
    >
      <!-- 自定义列内容 -->
      <template #column-orderNumber="{ row }">
        <el-button
          type="primary"
          text
          @click="viewOrderDetail(row)"
        >
          {{ row.orderNumber }}
        </el-button>
      </template>
      
      <template #column-status="{ row }">
        <el-tag
          :type="getStatusType(row.status)"
          size="small"
        >
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
      
      <template #column-totalFinance="{ row }">
        <span class="font-medium text-green-600">
          {{ formatCurrency(row.totalFinance) }}
        </span>
      </template>
      
      <template #column-currentReceivable="{ row }">
        <span class="font-medium" :class="row.currentReceivable > 0 ? 'text-orange-600' : 'text-gray-500'">
          {{ formatCurrency(row.currentReceivable) }}
        </span>
      </template>
      
      <template #column-overdueStatus="{ row }">
        <div v-if="row.overdueStatus?.isOverdue" class="flex items-center gap-1">
          <el-icon class="text-red-500"><WarningFilled /></el-icon>
          <span class="text-red-600 text-sm">
            逾期{{ row.overdueStatus.overdueDays }}天
          </span>
        </div>
        <span v-else class="text-green-600 text-sm">正常</span>
      </template>
      
      <!-- 操作列 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          text
          size="small"
          @click="viewOrderDetail(row)"
        >
          查看详情
        </el-button>
        <el-button
          type="success"
          text
          size="small"
          @click="viewCustomerSummary(row)"
        >
          客户汇总
        </el-button>
        <el-dropdown @command="(command) => handleRowAction(command, row)">
          <el-button type="info" text size="small">
            更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export">导出订单</el-dropdown-item>
              <el-dropdown-item command="print">打印订单</el-dropdown-item>
              <el-dropdown-item divided command="history">查看历史</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </DataTable>
    
    <!-- 数据统计 -->
    <div v-if="tableData.length > 0" class="data-summary">
      <div class="summary-cards">
        <div class="summary-card">
          <div class="summary-label">查询结果</div>
          <div class="summary-value">{{ pagination.total }} 条记录</div>
        </div>
        <div class="summary-card">
          <div class="summary-label">总金额</div>
          <div class="summary-value text-green-600">
            {{ formatCurrency(totalAmount) }}
          </div>
        </div>
        <div class="summary-card">
          <div class="summary-label">待收金额</div>
          <div class="summary-value text-orange-600">
            {{ formatCurrency(totalReceivable) }}
          </div>
        </div>
        <div class="summary-card">
          <div class="summary-label">逾期订单</div>
          <div class="summary-value text-red-600">
            {{ overdueCount }} 个
          </div>
        </div>
      </div>
    </div>
    
    <!-- 订单详情弹窗 -->
    <el-dialog
      v-model="orderDetailVisible"
      title="订单详情"
      width="80%"
      :close-on-click-modal="false"
    >
      <OrderDetailView
        v-if="selectedOrder"
        :order="selectedOrder"
        @close="orderDetailVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  Refresh,
  Download,
  DataAnalysis,
  WarningFilled,
  ArrowDown
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import SearchForm from '@/components/business/SearchForm.vue'
import DataTable from '@/components/business/DataTable.vue'
import OrderDetailView from '@/components/business/OrderDetailView.vue'
import { useDataStore } from '@/stores/data'
import { formatCurrency, formatDate } from '@/utils/format'
import type { 
  SearchField, 
  QuickFilter, 
  TableColumn, 
  QueryParams,
  OrderData 
} from '@/types/data'

const router = useRouter()
const route = useRoute()
const dataStore = useDataStore()

// 响应式数据
const searchLoading = ref(false)
const tableLoading = ref(false)
const tableData = ref<OrderData[]>([])
const selectedRows = ref<OrderData[]>([])
const selectedOrder = ref<OrderData | null>(null)
const orderDetailVisible = ref(false)

// 搜索表单配置
const searchFields: SearchField[] = [
  {
    key: 'dateRange',
    label: '日期范围',
    type: 'daterange',
    placeholder: '选择日期范围',
    colSpan: { md: 8, lg: 6 }
  },
  {
    key: 'customerName',
    label: '客户名称',
    type: 'text',
    placeholder: '请输入客户名称',
    colSpan: { md: 8, lg: 6 }
  },
  {
    key: 'orderNumber',
    label: '订单编号',
    type: 'text',
    placeholder: '请输入订单编号',
    colSpan: { md: 8, lg: 6 }
  },
  {
    key: 'status',
    label: '订单状态',
    type: 'select',
    placeholder: '选择订单状态',
    options: [
      { label: '全部', value: '' },
      { label: '进行中', value: 'active' },
      { label: '已完成', value: 'completed' },
      { label: '已取消', value: 'cancelled' },
      { label: '逾期', value: 'overdue' }
    ],
    colSpan: { md: 8, lg: 6 }
  }
]

const quickFilters: QuickFilter[] = [
  { label: '今日订单', value: 'today', field: 'dateRange' },
  { label: '本周订单', value: 'thisWeek', field: 'dateRange' },
  { label: '本月订单', value: 'thisMonth', field: 'dateRange' },
  { label: '逾期订单', value: 'overdue', field: 'status' },
  { label: '大额订单', value: 'large', field: 'amount' }
]

const defaultSearchValues = ref({
  dateRange: [],
  customerName: '',
  orderNumber: '',
  status: ''
})

// 表格配置
const tableColumns: TableColumn[] = [
  {
    key: 'orderDate',
    label: '订单日期',
    width: 120,
    sortable: true,
    formatter: (value) => formatDate(value)
  },
  {
    key: 'orderNumber',
    label: '订单编号',
    width: 150,
    sortable: true
  },
  {
    key: 'customerName',
    label: '客户名称',
    width: 150,
    sortable: true
  },
  {
    key: 'productType',
    label: '产品类型',
    width: 120
  },
  {
    key: 'totalFinance',
    label: '总融资额',
    width: 120,
    sortable: true,
    align: 'right'
  },
  {
    key: 'currentReceivable',
    label: '当前待收',
    width: 120,
    sortable: true,
    align: 'right'
  },
  {
    key: 'devicesCount',
    label: '设备台数',
    width: 100,
    sortable: true,
    align: 'center'
  },
  {
    key: 'status',
    label: '状态',
    width: 100,
    align: 'center'
  },
  {
    key: 'overdueStatus',
    label: '逾期状态',
    width: 120,
    align: 'center'
  }
]

const tableSearchConfig = {
  placeholder: '搜索订单编号、客户名称...',
  fields: []
}

const tableQuickFilters: QuickFilter[] = [
  { label: '进行中', value: 'active', field: 'status' },
  { label: '逾期', value: 'overdue', field: 'status' },
  { label: '大额', value: 'large', field: 'amount' }
]

// 分页配置
const pagination = computed(() => dataStore.pagination)

// 计算属性
const totalAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.totalFinance, 0)
})

const totalReceivable = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.currentReceivable, 0)
})

const overdueCount = computed(() => {
  return tableData.value.filter(item => item.status === 'overdue').length
})

// 方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    completed: 'info',
    cancelled: 'danger',
    overdue: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '进行中',
    completed: '已完成',
    cancelled: '已取消',
    overdue: '逾期'
  }
  return textMap[status] || status
}

const handleSearch = async (searchData: Record<string, any>) => {
  searchLoading.value = true
  tableLoading.value = true
  
  try {
    const params: QueryParams = {
      ...searchData,
      page: 1,
      pageSize: pagination.value.pageSize
    }
    
    // 处理日期范围
    if (searchData.dateRange && searchData.dateRange.length === 2) {
      params.startDate = searchData.dateRange[0]
      params.endDate = searchData.dateRange[1]
    }
    
    await dataStore.fetchOrders(params)
    tableData.value = dataStore.orders
    
    // 保存查询记录
    saveQueryRecord(searchData)
    
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    searchLoading.value = false
    tableLoading.value = false
  }
}

const handleReset = () => {
  defaultSearchValues.value = {
    dateRange: [],
    customerName: '',
    orderNumber: '',
    status: ''
  }
  
  // 重新加载默认数据
  loadDefaultData()
}

const handleSearchChange = (key: string, value: any, formData: Record<string, any>) => {
  // 可以在这里处理字段联动逻辑
  console.log('Search field changed:', key, value)
}

const handleRefresh = () => {
  loadDefaultData()
}

const handleExport = async () => {
  try {
    await dataStore.exportData({
      format: 'excel',
      filename: `订单数据_${new Date().toISOString().split('T')[0]}.xlsx`
    })
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const handleAnalyze = (formData: Record<string, any>) => {
  // 跳转到数据分析页面
  router.push({
    path: '/data/summary',
    query: formData
  })
}

const loadDefaultData = async () => {
  tableLoading.value = true
  
  try {
    await dataStore.fetchOrders({
      page: 1,
      pageSize: pagination.value.pageSize
    })
    tableData.value = dataStore.orders
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    tableLoading.value = false
  }
}

const saveQueryRecord = (searchData: Record<string, any>) => {
  const record = {
    id: Date.now().toString(),
    title: generateQueryTitle(searchData),
    params: searchData,
    time: new Date().toISOString()
  }
  
  const saved = localStorage.getItem('recentQueries')
  let queries = saved ? JSON.parse(saved) : []
  
  // 添加到开头，保留最近10条
  queries.unshift(record)
  queries = queries.slice(0, 10)
  
  localStorage.setItem('recentQueries', JSON.stringify(queries))
}

const generateQueryTitle = (searchData: Record<string, any>) => {
  const parts = []
  
  if (searchData.customerName) {
    parts.push(`客户: ${searchData.customerName}`)
  }
  
  if (searchData.orderNumber) {
    parts.push(`订单: ${searchData.orderNumber}`)
  }
  
  if (searchData.status) {
    parts.push(`状态: ${getStatusText(searchData.status)}`)
  }
  
  if (searchData.dateRange && searchData.dateRange.length === 2) {
    parts.push(`日期: ${searchData.dateRange[0]} 至 ${searchData.dateRange[1]}`)
  }
  
  return parts.length > 0 ? parts.join(', ') : '数据查询'
}

// 生命周期
onMounted(() => {
  // 检查URL参数
  const query = route.query
  if (Object.keys(query).length > 0) {
    // 从URL参数恢复搜索条件
    Object.assign(defaultSearchValues.value, query)
    handleSearch(defaultSearchValues.value)
  } else {
    loadDefaultData()
  }
})
</script>

<style scoped>
.query-page {
  @apply space-y-6;
}

.page-header {
  @apply flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6;
}

.header-content h1 {
  @apply text-2xl font-bold text-gray-800 mb-1;
}

.header-content p {
  @apply text-gray-600;
}

.header-actions {
  @apply flex gap-2;
}

.data-summary {
  @apply mt-6;
}

.summary-cards {
  @apply grid grid-cols-2 lg:grid-cols-4 gap-4;
}

.summary-card {
  @apply bg-white rounded-lg p-4 border border-gray-200 text-center;
}

.summary-label {
  @apply text-sm text-gray-600 mb-1;
}

.summary-value {
  @apply text-lg font-semibold;
}

@media (max-width: 640px) {
  .page-header {
    @apply flex-col items-stretch;
  }
  
  .header-actions {
    @apply justify-center;
  }
  
  .summary-cards {
    @apply grid-cols-1;
  }
}
</style>
