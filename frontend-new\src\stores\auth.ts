import { defineStore } from 'pinia'
import type { User, AuthState, LoginRequest, UserRole } from '@/types/auth'
import { authApi } from '@/api/auth'

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: localStorage.getItem('token'),
    isAuthenticated: false,
    isLoading: false,
    permissions: []
  }),

  getters: {
    // 获取用户角色
    userRole: (state): UserRole | null => state.user?.role || null,
    
    // 检查是否有特定权限
    hasPermission: (state) => (resource: string, action: string): boolean => {
      if (!state.user) return false
      
      const permission = `${resource}:${action}`
      return state.permissions.some(p => 
        `${p.resource}:${p.action}` === permission
      )
    },
    
    // 检查是否有特定角色
    hasRole: (state) => (role: UserRole): boolean => {
      return state.user?.role === role
    },
    
    // 检查是否有任一角色
    hasAnyRole: (state) => (roles: UserRole[]): boolean => {
      return state.user ? roles.includes(state.user.role) : false
    },
    
    // 获取用户显示名称
    displayName: (state): string => {
      return state.user?.username || '未知用户'
    }
  },

  actions: {
    // 登录
    async login(credentials: LoginRequest) {
      this.isLoading = true
      
      try {
        const response = await authApi.login(credentials)
        
        if (response.success && response.user && response.token) {
          this.user = response.user
          this.token = response.token
          this.isAuthenticated = true
          this.permissions = response.user.permissions
          
          // 保存token到localStorage
          localStorage.setItem('token', response.token)
          
          return { success: true, redirectUrl: response.redirectUrl }
        } else {
          return { success: false, message: response.message }
        }
      } catch (error) {
        console.error('Login error:', error)
        return { 
          success: false, 
          message: '登录失败，请检查网络连接' 
        }
      } finally {
        this.isLoading = false
      }
    },

    // 登出
    async logout() {
      try {
        await authApi.logout()
      } catch (error) {
        console.error('Logout error:', error)
      } finally {
        this.user = null
        this.token = null
        this.isAuthenticated = false
        this.permissions = []
        localStorage.removeItem('token')
      }
    },

    // 检查认证状态
    async checkAuth() {
      const token = localStorage.getItem('token')
      if (!token) {
        return false
      }

      this.isLoading = true
      
      try {
        const response = await authApi.getCurrentUser()
        
        if (response.success && response.data) {
          this.user = response.data
          this.token = token
          this.isAuthenticated = true
          this.permissions = response.data.permissions
          return true
        } else {
          this.clearAuth()
          return false
        }
      } catch (error) {
        console.error('Check auth error:', error)
        this.clearAuth()
        return false
      } finally {
        this.isLoading = false
      }
    },

    // 清除认证信息
    clearAuth() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      this.permissions = []
      localStorage.removeItem('token')
    },

    // 更新用户信息
    updateUser(userData: Partial<User>) {
      if (this.user) {
        this.user = { ...this.user, ...userData }
      }
    },

    // 刷新权限
    async refreshPermissions() {
      if (!this.isAuthenticated) return
      
      try {
        const response = await authApi.getUserPermissions()
        if (response.success && response.data) {
          this.permissions = response.data
        }
      } catch (error) {
        console.error('Refresh permissions error:', error)
      }
    }
  }
})
