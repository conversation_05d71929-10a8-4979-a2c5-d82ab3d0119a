/*
 * 桌面端数据网格控制器
 * Desktop Data Grid Controller for Enterprise Customer Summary
 * Version: 1.0
 * Created: 2024-12-28
 */

/**
 * 桌面端数据网格管理器
 * 负责桌面端数据展示的交互功能，采用轻量级设计
 */
class DesktopDataGridManager {
    constructor() {
        this.currentActiveTab = 'receivable';
        this.isInitialized = false;
        
        // 懒加载配置
        this.lazyLoadConfig = {
            batchSize: 20,           // 每批加载数量
            loadThreshold: 200,      // 距离底部多少像素开始加载
            maxInitialLoad: 30       // 初始最大加载数量
        };
        
        // 数据管理
        this.dataCache = {
            receivable: { all: [], rendered: [], currentIndex: 0 },
            orders: { all: [], rendered: [], currentIndex: 0 },
            finance: { all: [], rendered: [], currentIndex: 0 }
        };
        
        // 加载状态
        this.loadingStates = {
            receivable: false,
            orders: false,
            finance: false
        };
        
        // 搜索状态管理
        this.searchState = {
            currentQuery: '',
            searchResults: {
                receivable: { visible: 0, total: 0 },
                orders: { visible: 0, total: 0 },
                finance: { visible: 0, total: 0 }
            },
            searchHistory: [],
            isSearchActive: false
        };
        
        console.log('桌面端数据网格管理器已创建（支持懒加载和搜索）');
    }

    /**
     * 初始化管理器
     */
    init() {
        if (this.isInitialized) {
            console.log('桌面端数据网格管理器已经初始化');
            return;
        }

        try {
            console.log('开始初始化桌面端数据网格管理器...');
            
            // 初始化标签页切换
            this.initTabSwitching();
            
            // 初始化数据卡片交互
            this.initCardInteractions();
            
            // 设置响应式监听
            this.setupResponsiveListeners();
            
            // 初始化导出功能
            this.initExportFunctions();
            
            // 初始化懒加载
            this.initLazyLoading();
            
            // 初始化搜索功能
            this.initSearchSystem();
            
            this.isInitialized = true;
            console.log('桌面端数据网格管理器初始化完成（搜索功能已启用）');
            
        } catch (error) {
            console.error('桌面端数据网格管理器初始化失败:', error);
        }
    }

    /**
     * 初始化标签页切换功能
     */
    initTabSwitching() {
        const tabItems = document.querySelectorAll('.nav-tab-item');
        const tabPanes = document.querySelectorAll('.tab-pane');
        
        tabItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const target = e.currentTarget.getAttribute('data-target');
                this.switchTab(target, e.currentTarget);
            });
        });
        
        console.log('标签页切换功能初始化完成');
    }

    /**
     * 切换标签页
     */
    switchTab(targetId, clickedTab) {
        // 更新标签页状态
        document.querySelectorAll('.nav-tab-item').forEach(tab => {
            tab.classList.remove('active');
        });
        clickedTab.classList.add('active');
        
        // 更新内容区域
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('show', 'active');
        });
        
        const targetPane = document.getElementById(targetId);
        if (targetPane) {
            targetPane.classList.add('show', 'active');
        }
        
        // 更新当前活动标签页
        this.currentActiveTab = targetId;
        
        // 触发标签页切换事件
        this.onTabSwitch(targetId);
        
        console.log(`切换到标签页: ${targetId}`);
    }

    /**
     * 标签页切换后的处理
     */
    onTabSwitch(tabId) {
        // 延迟执行，确保动画完成
        setTimeout(() => {
            // 触发数据卡片动画
            this.animateCards(tabId);
            
            // 滚动到顶部
            const tabContent = document.querySelector(`#${tabId}`);
            if (tabContent) {
                tabContent.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }, 100);
    }

    /**
     * 为数据卡片添加动画
     */
    animateCards(tabId) {
        const cards = document.querySelectorAll(`#${tabId} .desktop-data-card`);
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.3s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }

    /**
     * 初始化数据卡片交互功能
     */
    initCardInteractions() {
        // 为所有数据卡片添加键盘导航支持
        document.querySelectorAll('.desktop-data-card').forEach(card => {
            card.setAttribute('tabindex', '0');
            
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleCardClick(card);
                }
            });
            
            card.addEventListener('click', () => {
                this.handleCardClick(card);
            });
        });
        
        console.log('数据卡片交互功能初始化完成');
    }

    /**
     * 处理数据卡片点击
     */
    handleCardClick(card) {
        // 移除其他卡片的选中状态
        document.querySelectorAll('.desktop-data-card').forEach(c => {
            c.classList.remove('selected');
        });
        
        // 添加选中状态
        card.classList.add('selected');
        
        // 可以在这里添加更多的交互逻辑
        console.log('数据卡片被选中:', card);
    }

    /**
     * 设置响应式监听器
     */
    setupResponsiveListeners() {
        let resizeTimer;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
        
        console.log('响应式监听器设置完成');
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 重新计算卡片布局
        const containers = document.querySelectorAll('.data-grid-container');
        containers.forEach(container => {
            // 触发重新计算
            container.style.display = 'none';
            container.offsetHeight; // 强制重绘
            container.style.display = 'grid';
        });
        
        console.log('响应式布局已更新');
    }

    /**
     * 初始化导出功能
     */
    initExportFunctions() {
        // 这些函数将被HTML中的按钮调用
        window.exportReceivableData = () => this.exportData('receivable');
        window.exportOrdersData = () => this.exportData('orders');
        window.exportFinanceData = () => this.exportData('finance');
        
        window.refreshReceivableData = () => this.refreshData('receivable');
        window.refreshOrdersData = () => this.refreshData('orders');
        window.refreshFinanceData = () => this.refreshData('finance');
        
        console.log('导出功能初始化完成');
    }

    /**
     * 导出数据
     */
    exportData(dataType) {
        console.log(`导出${dataType}数据`);
        
        // 显示导出提示
        this.showNotification(`正在导出${this.getDataTypeName(dataType)}数据...`, 'info');
        
        // 模拟导出过程
        setTimeout(() => {
            this.showNotification(`${this.getDataTypeName(dataType)}数据导出完成`, 'success');
        }, 1500);
    }

    /**
     * 刷新数据
     */
    refreshData(dataType) {
        console.log(`刷新${dataType}数据`);
        
        // 显示刷新提示
        this.showNotification(`正在刷新${this.getDataTypeName(dataType)}数据...`, 'info');
        
        // 模拟刷新过程
        setTimeout(() => {
            // 重置懒加载状态
            this.resetLazyLoading(dataType);
            
            this.showNotification(`${this.getDataTypeName(dataType)}数据刷新完成`, 'success');
            
            // 重新动画化当前标签页的卡片
            if (this.currentActiveTab === dataType) {
                this.animateCards(dataType);
            }
        }, 1000);
    }

    /**
     * 获取数据类型中文名称
     */
    getDataTypeName(dataType) {
        const names = {
            'receivable': '待收明细',
            'orders': '订单详情',
            'finance': '财务流水'
        };
        return names[dataType] || '数据';
    }

    /**
     * 显示通知消息
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'info' ? 'primary' : type === 'success' ? 'success' : 'warning'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;
        
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-${type === 'info' ? 'info-circle' : type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    /**
     * 初始化搜索系统
     */
    initSearchSystem() {
        // 加载搜索历史
        this.searchState.searchHistory = this.loadSearchHistory();

        // 为每个标签页初始化搜索功能
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            this.initSearchInput(input);
        });

        // 初始化快捷键支持
        this.initSearchShortcuts();

        // 初始化统计信息
        this.updateSearchStats();

        console.log('搜索系统初始化完成，支持实时搜索和快捷键');
    }

    /**
     * 初始化搜索输入框
     */
    initSearchInput(input) {
        const tabType = input.getAttribute('data-tab');
        const wrapper = input.closest('.search-input-wrapper');
        const clearBtn = wrapper.querySelector('.clear-search-btn');

        // 实时搜索监听
        let searchTimeout;
        input.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value.trim(), tabType);
            }, 300); // 防抖延迟300ms
        });

        // 清除搜索按钮
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                input.value = '';
                this.clearSearch(tabType);
                input.focus();
            });
        }

        // 回车键触发搜索
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(input.value.trim(), tabType);
                this.saveToSearchHistory(input.value.trim());
            }
        });

        // 焦点事件
        input.addEventListener('focus', () => {
            this.searchState.isSearchActive = true;
        });

        input.addEventListener('blur', () => {
            setTimeout(() => {
                this.searchState.isSearchActive = false;
            }, 200);
        });

        console.log(`搜索输入框初始化完成: ${tabType}`);
    }

    /**
     * 执行搜索操作
     */
    performSearch(query, tabType) {
        this.searchState.currentQuery = query;

        if (!query) {
            this.clearSearch(tabType);
            return;
        }

        const container = document.querySelector(`#${tabType} .data-grid-container`);
        if (!container) return;

        // 获取缓存中的所有数据（包括未加载的）
        const allData = this.dataCache[tabType].all;

        if (allData.length === 0) {
            this.toggleEmptySearchState(container, true, query);
            return;
        }

        console.log(`开始在缓存数据中搜索: "${query}" (共${allData.length}条数据)`);

        // 清空容器准备重新渲染搜索结果
        container.innerHTML = '';
        this.hideEmptySearchState(container);

        // 存储匹配结果用于排序
        const matchResults = [];

        // 在缓存数据中进行搜索
        allData.forEach(dataItem => {
            // 创建临时元素用于搜索匹配
            const tempCard = dataItem.element.cloneNode(true);
            const matchResult = this.getMatchResult(tempCard, query, tabType);
            
            if (matchResult.isMatch) {
                matchResults.push({
                    card: tempCard,
                    dataItem: dataItem,
                    priority: matchResult.priority,
                    matchType: matchResult.matchType
                });
            }
        });

        // 按优先级排序匹配结果
        matchResults.sort((a, b) => b.priority - a.priority);

        // 渲染搜索匹配的结果
        if (matchResults.length > 0) {
            const fragment = document.createDocumentFragment();
            
            matchResults.forEach((result, index) => {
                const card = result.card;
                
                // 根据标签页类型决定是否使用高亮或卡片样式
                if (tabType === 'finance') {
                    // 财务流水：使用卡片整体样式变化（与订单详情保持一致）
                    // 不使用文字高亮，改用卡片变色和角标
                } else {
                    // 其他标签页：保持原有的文字高亮方式
                    this.highlightMatches(card, query);
                }
                
                // 根据匹配类型添加不同的样式
                if (result.matchType === 'exact') {
                    card.classList.add('search-matched', 'exact-match');
                } else if (result.matchType === 'order-related') {
                    card.classList.add('search-matched', 'order-related');
                } else {
                    card.classList.add('search-matched');
                }
                
                // 为最高优先级的匹配项添加特殊标识
                if (index === 0 && result.priority > 8) {
                    card.classList.add('primary-match');
                }
                
                // 添加进入动画
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                fragment.appendChild(card);
                
                // 延迟显示动画
                setTimeout(() => {
                    card.style.transition = 'all 0.3s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
            });
            
            container.appendChild(fragment);
            
            // 显示搜索结果摘要
            this.showSearchSummary(tabType, query, matchResults);
        } else {
            // 显示空状态
            this.toggleEmptySearchState(container, true, query);
        }

        // 更新搜索统计
        this.searchState.searchResults[tabType] = {
            visible: matchResults.length,
            total: allData.length
        };
        this.updateSearchStats(tabType);

        // 标记搜索状态
        this.searchState.isSearchActive = true;

        console.log(`搜索完成: 在${allData.length}条数据中找到${matchResults.length}个匹配项`);
    }

    /**
     * 获取匹配结果（增强版）
     */
    getMatchResult(card, query, tabType) {
        const searchableText = this.getSearchableText(card, tabType);
        const normalizedQuery = query.toLowerCase().trim();
        
        // 提取关键信息用于精确匹配
        const keyInfo = this.extractKeyInfo(card, tabType);
        
        let priority = 0;
        let matchType = 'partial';
        let isMatch = false;

        // 1. 精确匹配检查（最高优先级）
        if (this.isExactMatch(keyInfo, normalizedQuery)) {
            priority = 10;
            matchType = 'exact';
            isMatch = true;
        }
        // 2. 订单号相关匹配（高优先级）
        else if (this.isOrderRelatedMatch(keyInfo, normalizedQuery)) {
            priority = 8;
            matchType = 'order-related';
            isMatch = true;
        }
        // 3. 关键字段开头匹配（中高优先级）
        else if (this.isKeyFieldStartMatch(keyInfo, normalizedQuery)) {
            priority = 6;
            matchType = 'key-start';
            isMatch = true;
        }
        // 4. 多关键词全匹配（中等优先级）
        else if (this.isMultiKeywordMatch(searchableText, normalizedQuery)) {
            priority = 4;
            matchType = 'multi-keyword';
            isMatch = true;
        }
        // 5. 部分包含匹配（低优先级）
        else if (searchableText.includes(normalizedQuery)) {
            priority = 2;
            matchType = 'partial';
            isMatch = true;
        }

        return {
            isMatch,
            priority,
            matchType
        };
    }

    /**
     * 提取关键信息
     */
    extractKeyInfo(card, tabType) {
        let keyInfo = {};

        switch (tabType) {
            case 'receivable':
                keyInfo = {
                    period: card.querySelector('.period-number')?.textContent?.trim() || '',
                    amount: card.querySelector('.data-value.amount')?.textContent?.trim() || '',
                    status: card.querySelector('.status-badge')?.textContent?.trim() || '',
                    orderCount: card.querySelector('.data-item .data-value')?.textContent?.trim() || ''
                };
                break;

            case 'orders':
                keyInfo = {
                    orderNumber: card.querySelector('.order-number')?.textContent?.replace(/[^\w\d]/g, '').trim() || '',
                    orderDate: card.querySelector('.order-date')?.textContent?.trim() || '',
                    businessType: card.querySelector('.type-badge')?.textContent?.trim() || '',
                    totalFinance: card.querySelector('.amount-value')?.textContent?.trim() || ''
                };
                break;

            case 'finance':
                keyInfo = {
                    transactionId: Array.from(card.querySelectorAll('.detail-value')).find(el =>
                        el.textContent.match(/\d{6,}/))?.textContent?.trim() || '',
                    transactionType: card.querySelector('.flow-badge')?.textContent?.trim() || '',
                    amount: card.querySelector('.amount-value')?.textContent?.replace(/[^\d.-]/g, '').trim() || '',
                    orderNumber: Array.from(card.querySelectorAll('.detail-value')).find(el =>
                        el.textContent.includes('HD') || el.textContent.includes('订单'))?.textContent?.trim() || '',
                    direction: card.querySelector('.flow-badge')?.textContent?.includes('收入') ? '收入' :
                               card.querySelector('.flow-badge')?.textContent?.includes('支出') ? '支出' : ''
                };
                break;
        }

        return keyInfo;
    }

    /**
     * 检查精确匹配
     */
    isExactMatch(keyInfo, query) {
        // 修复中文字符处理问题：使用更精确的正则表达式
        // 只移除标点符号，保留中文、英文、数字
        const normalizedQuery = query.replace(/[^\u4e00-\u9fa5\w\d]/g, '').toLowerCase().trim();

        // 如果查询为空（只包含标点符号），则不匹配
        if (!normalizedQuery) {
            return false;
        }

        return Object.values(keyInfo).some(value => {
            const normalizedValue = value.replace(/[^\u4e00-\u9fa5\w\d]/g, '').toLowerCase().trim();
            return normalizedValue === normalizedQuery ||
                   (normalizedValue.length > 3 && normalizedValue.includes(normalizedQuery) && normalizedQuery.length >= 3);
        });
    }

    /**
     * 检查订单相关匹配
     */
    isOrderRelatedMatch(keyInfo, query) {
        const orderPattern = /^(hd|订单|order)/i;

        // 如果查询看起来像订单号（支持中文）
        if (orderPattern.test(query) || /^[\u4e00-\u9fa5\w]{2,}\d+/.test(query)) {
            return Object.values(keyInfo).some(value =>
                value.toLowerCase().includes(query.toLowerCase()) && value.length > query.length
            );
        }

        return false;
    }

    /**
     * 检查关键字段开头匹配
     */
    isKeyFieldStartMatch(keyInfo, query) {
        const normalizedQuery = query.toLowerCase().trim();
        return Object.values(keyInfo).some(value =>
            value.toLowerCase().startsWith(normalizedQuery) && normalizedQuery.length >= 2
        );
    }

    /**
     * 检查多关键词匹配
     */
    isMultiKeywordMatch(searchableText, query) {
        const keywords = query.split(/\s+/).filter(k => k.length > 0);

        if (keywords.length < 2) return false;

        return keywords.every(keyword =>
            searchableText.includes(keyword.toLowerCase())
        );
    }

    /**
     * 显示搜索结果摘要
     */
    showSearchSummary(tabType, query, matchResults) {
        // 移除之前的摘要
        const existingSummary = document.querySelector(`#${tabType} .search-summary`);
        if (existingSummary) {
            existingSummary.remove();
        }

        if (matchResults.length === 0) return;

        let summaryStats = '';

        if (tabType === 'finance') {
            // 财务流水：按交易类型分类
            const transactionTypes = {};
            matchResults.forEach(result => {
                const transactionTypeElement = result.card.querySelector('.transaction-type');
                const transactionType = transactionTypeElement ? transactionTypeElement.textContent.trim() : '未知类型';

                // 对交易类型进行归类
                let category = this.categorizeTransactionType(transactionType);

                if (!transactionTypes[category]) {
                    transactionTypes[category] = 0;
                }
                transactionTypes[category]++;
            });

            // 生成分类统计
            const sortedTypes = Object.entries(transactionTypes)
                .sort((a, b) => b[1] - a[1]) // 按数量降序排列
                .map(([type, count]) => `<span class="stat-finance-${type.replace(/[^a-zA-Z0-9]/g, '')}">${type} ${count} 条</span>`)
                .join('');
            
            summaryStats = sortedTypes;
        } else {
            // 其他标签页：保持原有的匹配类型分类
            const exactMatches = matchResults.filter(r => r.matchType === 'exact').length;
            const orderMatches = matchResults.filter(r => r.matchType === 'order-related').length;
            const otherMatches = matchResults.length - exactMatches - orderMatches;

            summaryStats = `
                ${exactMatches > 0 ? `<span class="stat-exact">精确匹配 ${exactMatches} 条</span>` : ''}
                ${orderMatches > 0 ? `<span class="stat-order">订单相关 ${orderMatches} 条</span>` : ''}
                ${otherMatches > 0 ? `<span class="stat-other">其他匹配 ${otherMatches} 条</span>` : ''}
            `;
        }

        // 创建摘要元素
        const summary = document.createElement('div');
        summary.className = 'search-summary';
        summary.innerHTML = `
            <div class="search-summary-content">
                <div class="summary-title">
                    <i class="bi bi-search"></i>
                    搜索 "${query}" 的结果
                </div>
                <div class="summary-stats">
                    ${summaryStats}
                </div>
            </div>
        `;

        // 插入到容器顶部
        const container = document.querySelector(`#${tabType} .data-grid-container`);
        if (container) {
            container.insertBefore(summary, container.firstChild);
        }
    }

    /**
     * 财务流水交易类型分类
     */
    categorizeTransactionType(transactionType) {
        const type = transactionType.toLowerCase();

        // 租金相关
        if (type.includes('租金') || type.includes('月租') || type.includes('季租') ||
            type.includes('年租') || type.includes('租赁费')) {
            return '租金相关';
        }

        // 首付款相关
        if (type.includes('首付') || type.includes('定金') || type.includes('预付') ||
            type.includes('首期') || type.includes('头期')) {
            return '首付款相关';
        }

        // 保证金相关
        if (type.includes('保证金') || type.includes('押金') || type.includes('风险金') ||
            type.includes('履约金') || type.includes('担保金')) {
            return '保证金相关';
        }

        // 手续费相关
        if (type.includes('手续费') || type.includes('服务费') || type.includes('管理费') ||
            type.includes('咨询费') || type.includes('评估费')) {
            return '手续费相关';
        }

        // 退款相关
        if (type.includes('退款') || type.includes('退费') || type.includes('返还') ||
            type.includes('退回') || type.includes('退付')) {
            return '退款相关';
        }

        // 违约金相关
        if (type.includes('违约金') || type.includes('罚金') || type.includes('滞纳金') ||
            type.includes('逾期费') || type.includes('罚息')) {
            return '违约金相关';
        }

        // 其他收支
        return '其他收支';
    }

    /**
     * 获取卡片的可搜索文本
     */
    getSearchableText(card, tabType) {
        let searchableText = '';

        switch (tabType) {
            case 'receivable':
                // 待收明细：期数、金额、状态、订单数量、设备台数
                searchableText = [
                    card.querySelector('.period-number')?.textContent || '',
                    card.querySelector('.data-value.amount')?.textContent || '',
                    card.querySelector('.status-badge')?.textContent || '',
                    ...Array.from(card.querySelectorAll('.data-value')).map(el => el.textContent || '')
                ].join(' ').toLowerCase();
                break;

            case 'orders':
                // 订单详情：订单号、日期、业务类型、产品类型、金额等
                searchableText = [
                    card.querySelector('.order-number')?.textContent || '',
                    card.querySelector('.order-date')?.textContent || '',
                    card.querySelector('.type-badge')?.textContent || '',
                    ...Array.from(card.querySelectorAll('.detail-value')).map(el => el.textContent || ''),
                    ...Array.from(card.querySelectorAll('.amount-value')).map(el => el.textContent || '')
                ].join(' ').toLowerCase();
                break;

            case 'finance':
                // 财务流水：流水号、交易类型、金额、方向、订单号等
                searchableText = [
                    card.querySelector('.transaction-type')?.textContent || '',
                    card.querySelector('.flow-badge')?.textContent || '',
                    card.querySelector('.amount-value')?.textContent || '',
                    ...Array.from(card.querySelectorAll('.detail-value')).map(el => el.textContent || '')
                ].join(' ').toLowerCase();
                break;

            default:
                searchableText = card.textContent.toLowerCase();
        }

        return searchableText;
    }

    /**
     * 高亮匹配的文本
     */
    highlightMatches(card, query) {
        const keywords = query.toLowerCase().split(/\s+/).filter(k => k.length > 0);
        const textNodes = this.getTextNodes(card);

        textNodes.forEach(node => {
            const text = node.textContent.toLowerCase();
            let hasMatch = false;
            let highlightedText = node.textContent;

            keywords.forEach(keyword => {
                if (text.includes(keyword)) {
                    const regex = new RegExp(`(${this.escapeRegex(keyword)})`, 'gi');
                    highlightedText = highlightedText.replace(regex, '<span class="search-highlight">$1</span>');
                    hasMatch = true;
                }
            });

            if (hasMatch) {
                const wrapper = document.createElement('span');
                wrapper.innerHTML = highlightedText;
                node.parentNode.replaceChild(wrapper, node);
            }
        });
    }

    /**
     * 获取所有文本节点
     */
    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    if (node.parentElement.tagName === 'SCRIPT' || 
                        node.parentElement.tagName === 'STYLE') {
                        return NodeFilter.FILTER_REJECT;
                    }
                    return node.textContent.trim() ? 
                        NodeFilter.FILTER_ACCEPT : 
                        NodeFilter.FILTER_REJECT;
                }
            }
        );

        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        return textNodes;
    }

    /**
     * 转义正则表达式特殊字符
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * 显示卡片
     */
    showCard(card) {
        card.classList.remove('search-hidden');
        card.style.display = '';
    }

    /**
     * 隐藏卡片
     */
    hideCard(card) {
        card.classList.add('search-hidden');
    }

    /**
     * 清除搜索（支持懒加载重置）
     */
    clearSearch(tabType) {
        const container = document.querySelector(`#${tabType} .data-grid-container`);
        if (!container) return;

        // 清空容器并重置懒加载状态
        container.innerHTML = '';
        this.hideEmptySearchState(container);

        // 重置缓存状态
        this.dataCache[tabType].currentIndex = 0;
        this.dataCache[tabType].all.forEach(item => {
            item.visible = false;
        });

        // 重新进行初始渲染
        const initialCount = Math.min(
            this.lazyLoadConfig.maxInitialLoad,
            this.dataCache[tabType].all.length
        );

        if (initialCount > 0) {
            this.renderBatch(tabType, 0, initialCount);
        }

        // 重新显示加载触发器
        const loadTrigger = document.getElementById(`load-trigger-${tabType}`);
        if (loadTrigger && this.dataCache[tabType].all.length > initialCount) {
            loadTrigger.style.display = 'flex';
        }

        // 重置搜索统计
        this.searchState.searchResults[tabType] = {
            visible: this.dataCache[tabType].all.length,
            total: this.dataCache[tabType].all.length
        };

        this.updateSearchStats(tabType);
        this.searchState.currentQuery = '';
        this.searchState.isSearchActive = false;

        console.log(`清除搜索并重置懒加载: ${tabType}`);
    }

    /**
     * 清除高亮显示
     */
    clearHighlights(container) {
        const highlights = container.querySelectorAll('.search-highlight');
        highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
        });
    }

    /**
     * 更新搜索统计信息（支持缓存数据）
     */
    updateSearchStats(tabType = null) {
        const tabs = tabType ? [tabType] : ['receivable', 'orders', 'finance'];

        tabs.forEach(tab => {
            const container = document.querySelector(`#${tab} .search-stats`);
            if (!container) return;

            const visibleCount = container.querySelector('.visible-count');
            const totalCount = container.querySelector('.total-count');

            if (visibleCount && totalCount) {
                // 使用搜索结果统计或缓存数据统计
                if (this.searchState.searchResults[tab]) {
                    visibleCount.textContent = this.searchState.searchResults[tab].visible;
                    totalCount.textContent = this.searchState.searchResults[tab].total;
                } else {
                    // 使用缓存数据的实际数量
                    const cacheTotal = this.dataCache[tab].all.length;
                    visibleCount.textContent = cacheTotal;
                    totalCount.textContent = cacheTotal;
                }
            }
        });
    }

    /**
     * 切换空搜索状态
     */
    toggleEmptySearchState(container, show, query) {
        let emptyState = container.querySelector('.search-empty-state');

        if (show) {
            if (!emptyState) {
                emptyState = document.createElement('div');
                emptyState.className = 'search-empty-state';
                emptyState.innerHTML = `
                    <i class="bi bi-search"></i>
                    <h5>未找到匹配结果</h5>
                    <p>没有找到包含 "${query}" 的数据项</p>
                `;
                container.appendChild(emptyState);
            }
        } else {
            if (emptyState) {
                emptyState.remove();
            }
        }
    }

    /**
     * 隐藏空搜索状态
     */
    hideEmptySearchState(container) {
        const emptyState = container.querySelector('.search-empty-state');
        if (emptyState) {
            emptyState.remove();
        }
    }

    /**
     * 初始化搜索快捷键
     */
    initSearchShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+F 激活搜索
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.focusCurrentTabSearch();
            }
            
            // Escape 清除搜索
            if (e.key === 'Escape' && this.searchState.isSearchActive) {
                this.clearCurrentTabSearch();
            }
        });

        console.log('搜索快捷键初始化完成：Ctrl+F 激活搜索，Escape 清除搜索');
    }

    /**
     * 聚焦当前标签页的搜索框
     */
    focusCurrentTabSearch() {
        const currentTab = this.currentActiveTab;
        const searchInput = document.querySelector(`#${currentTab} .search-input`);
        
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
            console.log(`激活搜索框: ${currentTab}`);
        }
    }

    /**
     * 清除当前标签页的搜索
     */
    clearCurrentTabSearch() {
        const currentTab = this.currentActiveTab;
        const searchInput = document.querySelector(`#${currentTab} .search-input`);
        
        if (searchInput && searchInput.value) {
            searchInput.value = '';
            this.clearSearch(currentTab);
            console.log(`清除搜索: ${currentTab}`);
        }
    }

    /**
     * 保存搜索历史
     */
    saveToSearchHistory(query) {
        if (!query || query.length < 2) return;

        const history = this.searchState.searchHistory;
        const index = history.indexOf(query);
        
        if (index > -1) {
            history.splice(index, 1);
        }
        
        history.unshift(query);
        
        // 限制历史记录数量
        if (history.length > 10) {
            history.splice(10);
        }
        
        this.searchState.searchHistory = history;
        this.saveSearchHistory();
    }

    /**
     * 加载搜索历史
     */
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('enterprise_search_history');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.warn('加载搜索历史失败:', error);
            return [];
        }
    }

    /**
     * 保存搜索历史到本地存储
     */
    saveSearchHistory() {
        try {
            localStorage.setItem('enterprise_search_history', 
                JSON.stringify(this.searchState.searchHistory));
        } catch (error) {
            console.warn('保存搜索历史失败:', error);
        }
    }

    /**
     * 初始化懒加载功能
     */
    initLazyLoading() {
        console.log('初始化懒加载功能...');
        
        // 收集所有原始数据
        this.collectOriginalData();
        
        // 设置滚动监听
        this.setupScrollListeners();
        
        // 执行初始渲染
        this.performInitialRender();
        
        console.log('懒加载功能初始化完成');
    }

    /**
     * 收集页面中的原始数据
     */
    collectOriginalData() {
        const tabTypes = ['receivable', 'orders', 'finance'];
        
        tabTypes.forEach(tabType => {
            const container = document.querySelector(`#${tabType} .data-grid-container`);
            if (container) {
                // 收集所有卡片元素
                const cards = Array.from(container.querySelectorAll('.desktop-data-card'));
                this.dataCache[tabType].all = cards.map(card => ({
                    element: card.cloneNode(true),
                    html: card.outerHTML,
                    visible: false
                }));
                
                // 清空容器，准备懒加载
                container.innerHTML = '';
                
                console.log(`收集${tabType}数据: ${this.dataCache[tabType].all.length}条`);
            }
        });
    }

    /**
     * 设置滚动监听器
     */
    setupScrollListeners() {
        const tabTypes = ['receivable', 'orders', 'finance'];
        
        tabTypes.forEach(tabType => {
            const tabPane = document.getElementById(tabType);
            if (tabPane) {
                // 创建Intersection Observer来检测滚动
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting && !this.loadingStates[tabType]) {
                            this.loadMoreData(tabType);
                        }
                    });
                }, {
                    root: null,
                    rootMargin: `${this.lazyLoadConfig.loadThreshold}px`,
                    threshold: 0.1
                });

                // 创建加载触发器
                const loadTrigger = this.createLoadTrigger(tabType);
                tabPane.appendChild(loadTrigger);
                observer.observe(loadTrigger);
            }
        });
    }

    /**
     * 创建加载触发器元素
     */
    createLoadTrigger(tabType) {
        const trigger = document.createElement('div');
        trigger.className = 'lazy-load-trigger';
        trigger.id = `load-trigger-${tabType}`;
        trigger.style.cssText = `
            height: 20px;
            width: 100%;
            margin-top: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
        `;
        
        return trigger;
    }

    /**
     * 执行初始渲染
     */
    performInitialRender() {
        const tabTypes = ['receivable', 'orders', 'finance'];
        
        tabTypes.forEach(tabType => {
            const initialCount = Math.min(
                this.lazyLoadConfig.maxInitialLoad,
                this.dataCache[tabType].all.length
            );
            
            if (initialCount > 0) {
                this.renderBatch(tabType, 0, initialCount);
            }
        });
    }

    /**
     * 渲染一批数据
     */
    renderBatch(tabType, startIndex, count) {
        const container = document.querySelector(`#${tabType} .data-grid-container`);
        if (!container) return;

        const endIndex = Math.min(startIndex + count, this.dataCache[tabType].all.length);
        const batch = this.dataCache[tabType].all.slice(startIndex, endIndex);

        // 创建文档片段提高性能
        const fragment = document.createDocumentFragment();

        batch.forEach((item, index) => {
            const card = item.element.cloneNode(true);
            
            // 添加懒加载动画
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            fragment.appendChild(card);
            
            // 延迟显示动画
            setTimeout(() => {
                card.style.transition = 'all 0.3s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 50);
            
            item.visible = true;
        });

        container.appendChild(fragment);
        this.dataCache[tabType].currentIndex = endIndex;

        console.log(`${tabType}: 渲染第${startIndex}-${endIndex}项，共${this.dataCache[tabType].all.length}项`);
    }

    /**
     * 加载更多数据
     */
    loadMoreData(tabType) {
        if (this.loadingStates[tabType]) return;
        
        const cache = this.dataCache[tabType];
        const remainingCount = cache.all.length - cache.currentIndex;
        
        if (remainingCount <= 0) {
            this.hideLoadTrigger(tabType);
            return;
        }

        this.loadingStates[tabType] = true;
        this.showLoadingIndicator(tabType);

        // 模拟网络延迟
        setTimeout(() => {
            const batchSize = Math.min(this.lazyLoadConfig.batchSize, remainingCount);
            this.renderBatch(tabType, cache.currentIndex, batchSize);
            
            this.loadingStates[tabType] = false;
            this.hideLoadingIndicator(tabType);
            
            // 如果没有更多数据，隐藏触发器
            if (cache.currentIndex >= cache.all.length) {
                this.hideLoadTrigger(tabType);
            }
        }, 300);
    }

    /**
     * 显示加载指示器
     */
    showLoadingIndicator(tabType) {
        const trigger = document.getElementById(`load-trigger-${tabType}`);
        if (trigger) {
            trigger.innerHTML = `
                <div class="d-flex align-items-center text-muted">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <span>正在加载更多数据...</span>
                </div>
            `;
        }
    }

    /**
     * 隐藏加载指示器
     */
    hideLoadingIndicator(tabType) {
        const trigger = document.getElementById(`load-trigger-${tabType}`);
        if (trigger) {
            trigger.innerHTML = '';
        }
    }

    /**
     * 隐藏加载触发器
     */
    hideLoadTrigger(tabType) {
        const trigger = document.getElementById(`load-trigger-${tabType}`);
        if (trigger) {
            trigger.style.display = 'none';
        }
    }

    /**
     * 重置懒加载状态
     */
    resetLazyLoading(tabType) {
        const container = document.querySelector(`#${tabType} .data-grid-container`);
        if (container) {
            container.innerHTML = '';
        }
        
        this.dataCache[tabType].currentIndex = 0;
        this.dataCache[tabType].rendered = [];
        this.loadingStates[tabType] = false;
        
        // 重新显示触发器
        const trigger = document.getElementById(`load-trigger-${tabType}`);
        if (trigger) {
            trigger.style.display = 'flex';
            this.hideLoadingIndicator(tabType);
        }
        
        // 重新执行初始渲染
        const initialCount = Math.min(
            this.lazyLoadConfig.maxInitialLoad,
            this.dataCache[tabType].all.length
        );
        
        if (initialCount > 0) {
            this.renderBatch(tabType, 0, initialCount);
        }
    }

    /**
     * 销毁管理器
     */
    destroy() {
        console.log('销毁桌面端数据网格管理器...');
        
        // 移除事件监听器
        window.removeEventListener('resize', this.handleResize);
        
        // 清理Intersection Observers
        document.querySelectorAll('.lazy-load-trigger').forEach(trigger => {
            trigger.remove();
        });
        
        // 清理全局函数
        delete window.exportReceivableData;
        delete window.exportOrdersData;
        delete window.exportFinanceData;
        delete window.refreshReceivableData;
        delete window.refreshOrdersData;
        delete window.refreshFinanceData;
        
        this.isInitialized = false;
        console.log('桌面端数据网格管理器已销毁');
    }
}

// 全局实例
window.DesktopDataGridManager = DesktopDataGridManager;

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('桌面端数据网格页面DOM加载完成');
    
    // 检查是否存在桌面端网格
    if (document.querySelector('.desktop-data-grid')) {
        setTimeout(() => {
            try {
                window.desktopDataGridManager = new DesktopDataGridManager();
                window.desktopDataGridManager.init();
                
                // 可选：添加搜索功能
                // window.desktopDataGridManager.addSearchCapability();
                
            } catch (error) {
                console.error('桌面端数据网格管理器启动失败:', error);
            }
        }, 300);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.desktopDataGridManager) {
        window.desktopDataGridManager.destroy();
    }
});

// 移动端适配管理器集成
document.addEventListener('tabShown', (e) => {
    if (window.desktopDataGridManager && e.detail && e.detail.tabId) {
        // 同步移动端的标签页切换到桌面端
        const desktopTab = document.querySelector(`[data-target="${e.detail.tabId}"]`);
        if (desktopTab) {
            window.desktopDataGridManager.switchTab(e.detail.tabId, desktopTab);
        }
    }
});

console.log('桌面端数据网格脚本加载完成'); 