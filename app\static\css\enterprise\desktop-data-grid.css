/*
 * 桌面端数据网格样式
 * Desktop Data Grid Styles for Enterprise Customer Summary
 * Version: 1.0
 * Created: 2024-12-28
 */

/* ========== 桌面端标签导航 ========== */
.desktop-tab-nav {
    margin-bottom: 1.25rem;
}

.nav-tabs-container {
    display: flex;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.nav-tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #6c757d;
    background: transparent;
    border: none;
    min-height: 48px;
}

.nav-tab-item:hover {
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color: #495057;
}

.nav-tab-item.active {
    background: #ffffff;
    color: #0d6efd;
    box-shadow: 0 2px 12px rgba(13, 110, 253, 0.15);
}

.nav-tab-item i {
    font-size: 1.1rem;
}

.nav-tab-item span {
    font-size: 0.95rem;
}

/* ========== 数据网格容器 ========== */
.desktop-data-grid {
    margin-top: 1rem;
}

.data-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e9ecef;
}

.grid-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.grid-actions {
    display: flex;
    gap: 8px;
}

.grid-actions .btn {
    border-radius: 8px;
    font-size: 0.875rem;
    padding: 6px 12px;
    transition: all 0.2s ease;
}

.grid-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* ========== 数据网格布局 ========== */
.data-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
    overflow: visible;
    padding-top: 5px;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
    .data-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
        gap: 2rem;
    }
}

/* 中等屏幕适配 */
@media (max-width: 1200px) {
    .data-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.25rem;
    }
}

/* ========== 桌面端数据卡片 ========== */
.desktop-data-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: visible;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
    min-height: auto;
}

/* 强制显示所有边框 */
.desktop-data-card * {
    box-sizing: border-box;
}

.desktop-data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #dee2e6;
}

.card-header-section {
    padding: 1rem 1.25rem 0.75rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #f1f3f4;
}

.card-body-section {
    padding: 1rem 1.25rem 1.25rem;
    overflow: visible;
}

/* ========== 待收明细卡片样式 ========== */
.receivable-card .period-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.period-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
}

.period-status .status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.normal {
    background: #d1edff;
    color: #0969da;
}

.status-badge.overdue {
    background: #ffebe9;
    color: #cf222e;
}

.receivable-card .data-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
}

.data-item {
    text-align: center;
}

.data-item.primary {
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
    border-radius: 8px;
    padding: 1rem;
}

.data-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
}

.data-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.data-value.amount {
    color: #0d6efd;
    font-size: 1.2rem;
}

/* ========== 订单详情卡片样式 ========== */
.order-card .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.order-number {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 1.05rem;
}

.order-date {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6c757d;
    font-size: 0.9rem;
}

.business-type .type-badge {
    background: #e7f3ff;
    color: #0969da;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.order-amounts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.amount-item {
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}

.amount-item.primary {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border: 1px solid #c3e6c3;
}

.amount-item.secondary {
    background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
    border: 1px solid #f0e68c;
}

.amount-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
}

.amount-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #198754;
}

.amount-item.secondary .amount-value {
    color: #ffc107;
}

.order-details .detail-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    overflow: visible;
}

/* 订单详情的最后一行不显示下边距 */
.order-details .detail-row:last-child {
    margin-bottom: 0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    min-height: 36px;
    position: relative;
}

/* 确保最后一行的detail-item边框完整显示 */
.detail-row:last-child .detail-item {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 4px;
    padding-bottom: 0.5rem;
}

/* 为最后一行detail-item添加额外空间 */
.order-details .detail-row:last-child,
.transaction-details .detail-row:last-child {
    margin-bottom: 0.5rem;
}

/* 防止边框被截断 */
.order-details, .transaction-details {
    overflow: visible;
    padding-bottom: 0.5rem;
    position: relative;
}

/* 确保detail-row容器有足够空间显示边框 */
.order-details .detail-row,
.transaction-details .detail-row {
    overflow: visible;
    padding-bottom: 2px;
}

.detail-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.detail-value {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

/* ========== 财务流水卡片样式 ========== */
.finance-card .finance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.transaction-type {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 1.05rem;
}

.transaction-date {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6c757d;
    font-size: 0.9rem;
}

.flow-direction .flow-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.flow-badge.flow-in {
    background: #d1edff;
    color: #0969da;
}

.flow-badge.flow-out {
    background: #ffebe9;
    color: #cf222e;
}

.transaction-amount {
    text-align: center;
    margin-bottom: 1rem;
}

.amount-section {
    display: inline-flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-size: 1.5rem;
    font-weight: 700;
}

.amount-section.positive {
    background: linear-gradient(135deg, #d1edff 0%, #e3f2fd 100%);
    color: #0969da;
}

.amount-section.negative {
    background: linear-gradient(135deg, #ffebe9 0%, #ffeaa7 100%);
    color: #cf222e;
}

.amount-symbol {
    margin-right: 4px;
}

.finance-card .detail-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    overflow: visible;
}

/* 财务卡片的最后一行不显示下边距 */
.finance-card .detail-row:last-child {
    margin-bottom: 0;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

/* ========== 空状态样式 ========== */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #cbd5e1;
}

.empty-state h5 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* ========== 响应式优化 ========== */
@media (max-width: 768px) {
    .desktop-data-grid {
        display: none !important;
    }
}

/* 中等屏幕优化 */
@media (max-width: 992px) {
    .data-grid-container {
        grid-template-columns: 1fr;
    }
    
    .order-card .order-header,
    .finance-card .finance-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .receivable-card .data-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .order-amounts {
        grid-template-columns: 1fr;
    }
    
    .finance-card .detail-row {
        grid-template-columns: 1fr;
    }
}

/* ========== 打印样式 ========== */
@media print {
    .desktop-data-grid {
        box-shadow: none !important;
    }
    
    .desktop-data-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        page-break-inside: avoid;
        margin-bottom: 1rem !important;
    }
    
    .grid-actions {
        display: none !important;
    }
}

/* ========== 无障碍访问优化 ========== */
.nav-tab-item:focus,
.desktop-data-card:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

.desktop-data-card[tabindex="0"]:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(13, 110, 253, 0.2);
}

/* ========== 懒加载样式 ========== */
.lazy-load-trigger {
    position: relative;
    min-height: 20px;
    margin: 2rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.lazy-load-trigger .spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.1em;
}

.lazy-load-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 0.9rem;
}

/* 懒加载动画优化 */
.desktop-data-card.lazy-loaded {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease-out;
}

.desktop-data-card.lazy-loaded.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 数据加载状态 */
.data-grid-container.loading {
    position: relative;
}

.data-grid-container.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* 性能优化 */
.desktop-data-card {
    contain: layout style paint;
    will-change: transform, opacity;
}

/* ========== 动画效果 ========== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.desktop-data-card {
    animation: fadeInUp 0.3s ease-out;
}

.desktop-data-card:nth-child(even) {
    animation-delay: 0.1s;
}

.desktop-data-card:nth-child(odd) {
    animation-delay: 0.05s;
}

/* 懒加载卡片的特殊动画 */
.desktop-data-card.lazy-batch {
    animation: slideInFromBottom 0.4s ease-out;
}

/* 数据量统计显示 */
.data-stats-indicator {
    position: absolute;
    top: -10px;
    right: 10px;
    background: #0d6efd;
    color: white;
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 12px;
    z-index: 5;
}

.data-grid-header {
    position: relative;
}

/* ===== 搜索功能样式 ===== */

/* 搜索容器 */
.search-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-right: 16px;
    padding: 8px 12px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
}

.search-container:hover {
    background: rgba(248, 250, 252, 0.95);
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

/* 搜索输入框包装器 */
.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 280px;
}

/* 搜索输入框 */
.search-input {
    background: white;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    padding: 6px 32px 6px 12px;
    font-size: 14px;
    line-height: 1.4;
    color: #374151;
    transition: all 0.2s ease;
    width: 100%;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
}

.search-input::placeholder {
    color: #9ca3af;
    font-size: 13px;
}

/* 搜索图标 */
.search-icon {
    position: absolute;
    right: 32px;
    color: #6b7280;
    font-size: 14px;
    pointer-events: none;
    transition: color 0.2s ease;
}

.search-input:focus + .search-icon {
    color: #3b82f6;
}

/* 清除搜索按钮 */
.clear-search-btn {
    position: absolute;
    right: 6px;
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 16px;
    padding: 2px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.clear-search-btn:hover {
    background: #fee2e2;
    color: #dc2626;
}

.search-input:not(:placeholder-shown) + .search-icon + .clear-search-btn {
    display: flex;
}

/* 搜索统计信息 */
.search-stats {
    font-size: 12px;
    color: #6b7280;
    white-space: nowrap;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.search-count {
    font-weight: 500;
}

.visible-count {
    color: #059669;
    font-weight: 600;
}

.total-count {
    color: #374151;
    font-weight: 600;
}

/* 搜索结果高亮 */
.search-highlight {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: 600;
    box-shadow: 0 1px 2px rgba(251, 191, 36, 0.2);
}

/* 搜索过滤后的卡片状态 */
.desktop-data-card.search-hidden {
    display: none !important;
}

.desktop-data-card.search-matched {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
}

/* 空搜索结果状态 */
.search-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
    margin-top: 20px;
}

.search-empty-state i {
    font-size: 3rem;
    color: #cbd5e1;
    margin-bottom: 16px;
    display: block;
}

.search-empty-state h5 {
    color: #374151;
    margin-bottom: 8px;
    font-weight: 600;
}

.search-empty-state p {
    color: #6b7280;
    margin: 0;
    font-size: 14px;
}

/* 搜索建议提示 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 32px;
    background: white;
    border: 1px solid #e2e8f0;
    border-top: none;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.search-suggestion-item {
    padding: 8px 12px;
    color: #374151;
    font-size: 14px;
    cursor: pointer;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
}

.search-suggestion-item:hover {
    background: #f8fafc;
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

.search-suggestion-item .suggestion-type {
    font-size: 12px;
    color: #6b7280;
    float: right;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .search-input-wrapper {
        min-width: 240px;
    }
    
    .search-container {
        gap: 8px;
        margin-right: 12px;
    }
}

@media (max-width: 992px) {
    .search-container {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
        padding: 12px;
        margin-right: 0;
        margin-bottom: 12px;
    }
    
    .search-input-wrapper {
        min-width: auto;
    }
    
    .search-stats {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .search-container {
        padding: 8px;
        gap: 6px;
    }
    
    .search-input {
        font-size: 13px;
        padding: 8px 28px 8px 10px;
    }
    
    .search-stats {
        font-size: 11px;
        padding: 6px;
    }
}

/* ===== 搜索结果摘要样式 ===== */

/* 搜索摘要容器 */
.search-summary {
    background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
    border: 1px solid #d1d9ff;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.search-summary-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;
}

.summary-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #1e40af;
    font-size: 14px;
}

.summary-title i {
    color: #3b82f6;
    font-size: 16px;
}

.summary-stats {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.summary-stats span {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.stat-exact {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.stat-order {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.stat-other {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

/* ===== 精确匹配样式增强 ===== */

/* 精确匹配卡片 */
.desktop-data-card.exact-match {
    border: 2px solid #16a34a !important;
    box-shadow: 0 4px 16px rgba(22, 163, 74, 0.2);
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

/* 订单相关匹配卡片 */
.desktop-data-card.order-related {
    border: 2px solid #2563eb !important;
    box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

/* 财务流水搜索匹配样式 - 与订单详情保持一致 */
#finance .desktop-data-card.exact-match {
    border: 2px solid #16a34a !important;
    box-shadow: 0 4px 16px rgba(22, 163, 74, 0.2);
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

#finance .desktop-data-card.order-related {
    border: 2px solid #2563eb !important;
    box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

#finance .desktop-data-card.search-matched {
    border: 2px solid #3b82f6 !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: translateY(-1px);
}

/* 主要匹配项（最高优先级） */
.desktop-data-card.primary-match {
    position: relative;
    transform: translateY(-2px);
    overflow: visible !important;
    padding-top: 10px !important;
}

.desktop-data-card.primary-match::before {
    content: "最佳匹配";
    position: absolute;
    top: -6px;
    right: 12px;
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    color: white;
    font-size: 11px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 6px;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
    border: 2px solid white;
    letter-spacing: 0.5px;
    transform: rotate(-2deg);
}

/* 财务流水最佳匹配样式 */
#finance .desktop-data-card.primary-match {
    position: relative;
    transform: translateY(-2px);
    overflow: visible !important;
    padding-top: 10px !important;
}

#finance .desktop-data-card.primary-match::before {
    content: "最佳匹配";
    position: absolute;
    top: -6px;
    right: 12px;
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    color: white;
    font-size: 11px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 6px;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
    border: 2px solid white;
    letter-spacing: 0.5px;
    transform: rotate(-2deg);
}

/* 匹配类型标识 */
.desktop-data-card.exact-match::after {
    content: "精确";
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: #16a34a;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 2px;
    opacity: 0.8;
}

.desktop-data-card.order-related::after {
    content: "订单";
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: #2563eb;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 2px;
    opacity: 0.8;
}

/* 财务流水匹配类型标识 */
#finance .desktop-data-card.exact-match::after {
    content: "精确";
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: #16a34a;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 2px;
    opacity: 0.8;
    z-index: 10;
}

#finance .desktop-data-card.order-related::after {
    content: "流水";
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: #2563eb;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 2px;
    opacity: 0.8;
    z-index: 10;
}

#finance .desktop-data-card.search-matched::after {
    content: "匹配";
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: #3b82f6;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 2px;
    opacity: 0.8;
    z-index: 10;
}

/* ===== 搜索结果排序视觉提示 ===== */

/* 搜索匹配项的优先级视觉提示 */
.desktop-data-card.search-matched {
    transition: all 0.3s ease;
}

.desktop-data-card.search-matched:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .search-summary-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .summary-stats {
        width: 100%;
        justify-content: flex-start;
    }
    
    .desktop-data-card.primary-match::before {
        top: -2px;
        right: 8px;
        font-size: 10px;
        padding: 3px 6px;
        transform: rotate(-1deg);
        box-shadow: 0 1px 4px rgba(220, 38, 38, 0.3);
    }
    
    .desktop-data-card.exact-match::after,
    .desktop-data-card.order-related::after {
        bottom: 4px;
        right: 4px;
        font-size: 9px;
        padding: 1px 3px;
    }
}

/* ===== 财务流水搜索分类样式 ===== */

/* 租金相关 */
.stat-finance-租金相关 {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border: 1px solid #86efac;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    display: inline-block;
}

/* 首付款相关 */
.stat-finance-首付款相关 {
    background: linear-gradient(135deg, #dbeafe 0%, #93c5fd 100%);
    color: #1e40af;
    border: 1px solid #60a5fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    display: inline-block;
}

/* 保证金相关 */
.stat-finance-保证金相关 {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border: 1px solid #f59e0b;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    display: inline-block;
}

/* 手续费相关 */
.stat-finance-手续费相关 {
    background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    color: #3730a3;
    border: 1px solid #8b5cf6;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    display: inline-block;
}

/* 退款相关 */
.stat-finance-退款相关 {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #065f46;
    border: 1px solid #34d399;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    display: inline-block;
}

/* 违约金相关 */
.stat-finance-违约金相关 {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #f87171;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    display: inline-block;
}

/* 其他收支 */
.stat-finance-其他收支 {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #374151;
    border: 1px solid #9ca3af;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    display: inline-block;
}

/* 财务流水搜索摘要特殊样式 */
#finance .search-summary .summary-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
}

#finance .search-summary .summary-stats > span {
    margin-right: 0;
} 