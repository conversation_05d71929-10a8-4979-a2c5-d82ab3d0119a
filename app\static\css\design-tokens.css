/**
 * 设计令牌系统 (Design Tokens)
 * 统一管理所有设计元素，确保视觉一致性
 * 版本: 2.0 - 完全重构版本
 */

:root {
  /* ========================================
     颜色系统 (Color System)
   ======================================== */
  
  /* 主色调 */
  --color-primary-50: #e6f2ff;
  --color-primary-100: #b3d9ff;
  --color-primary-200: #80c1ff;
  --color-primary-300: #4da8ff;
  --color-primary-400: #1a8fff;
  --color-primary-500: #007bff;    /* 主色 */
  --color-primary-600: #0066d9;
  --color-primary-700: #0052b3;
  --color-primary-800: #003d8a;
  --color-primary-900: #002961;
  
  /* 语义色彩 */
  --color-success-50: #e8f5e8;
  --color-success-100: #c3e6c3;
  --color-success-200: #9dd69d;
  --color-success-300: #77c677;
  --color-success-400: #51b651;
  --color-success-500: #28a745;    /* 成功色 */
  --color-success-600: #228b3c;
  --color-success-700: #1c6e30;
  --color-success-800: #155224;
  --color-success-900: #0f3618;
  
  --color-warning-50: #fffbf0;
  --color-warning-100: #fff3cd;
  --color-warning-200: #ffecb5;
  --color-warning-300: #ffe69c;
  --color-warning-400: #ffdf84;
  --color-warning-500: #ffc107;    /* 警告色 */
  --color-warning-600: #e0a800;
  --color-warning-700: #c69500;
  --color-warning-800: #a77f00;
  --color-warning-900: #856700;
  
  --color-danger-50: #fdf2f2;
  --color-danger-100: #fde8e8;
  --color-danger-200: #fbd5d5;
  --color-danger-300: #f8b4b4;
  --color-danger-400: #f98080;
  --color-danger-500: #dc3545;     /* 危险色 */
  --color-danger-600: #c82333;
  --color-danger-700: #a02622;
  --color-danger-800: #7d1f1f;
  --color-danger-900: #5a1a1a;
  
  --color-info-50: #e6f7ff;
  --color-info-100: #b3e5ff;
  --color-info-200: #80d4ff;
  --color-info-300: #4dc2ff;
  --color-info-400: #1ab1ff;
  --color-info-500: #17a2b8;      /* 信息色 */
  --color-info-600: #138496;
  --color-info-700: #0f6674;
  --color-info-800: #0c4851;
  --color-info-900: #082a2f;
  
  /* 中性色彩 */
  --color-gray-50: #f8f9fa;
  --color-gray-100: #e9ecef;
  --color-gray-200: #dee2e6;
  --color-gray-300: #ced4da;
  --color-gray-400: #adb5bd;
  --color-gray-500: #6c757d;
  --color-gray-600: #495057;
  --color-gray-700: #343a40;
  --color-gray-800: #212529;
  --color-gray-900: #000000;
  
  /* 特殊用途色彩 */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-transparent: transparent;
  
  /* 业务状态色彩 */
  --color-status-overdue: #fff2f2;     /* 逾期背景 */
  --color-status-overdue-text: #721c24; /* 逾期文字 */
  --color-status-overdue-border: #f1aeb5; /* 逾期边框 */
  
  --color-status-early: #f0f9ff;       /* 提前背景 */
  --color-status-early-text: #1e3a8a;  /* 提前文字 */
  --color-status-early-border: #93c5fd; /* 提前边框 */
  
  --color-status-ontime: #f0fdf4;      /* 按时背景 */
  --color-status-ontime-text: #166534; /* 按时文字 */
  --color-status-ontime-border: #86efac; /* 按时边框 */
  
  --color-status-upcoming: #fffbeb;    /* 即将到期背景 */
  --color-status-upcoming-text: #92400e; /* 即将到期文字 */
  --color-status-upcoming-border: #fde68a; /* 即将到期边框 */
  
  /* ========================================
     字体系统 (Typography System)
   ======================================== */
  
  /* 字体族 */
  --font-family-primary: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif;
  --font-family-mono: "SF Mono", "Monaco", "Cascadia Code", "Roboto Mono", Consolas, monospace;
  --font-family-serif: "Times New Roman", Times, serif;
  
  /* 字体大小 */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */
  
  /* 字重 */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* 字间距 */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
  
  /* ========================================
     间距系统 (Spacing System)
   ======================================== */
  
  --spacing-0: 0;
  --spacing-0-5: 0.125rem;     /* 2px */
  --spacing-1: 0.25rem;        /* 4px */
  --spacing-1-5: 0.375rem;     /* 6px */
  --spacing-2: 0.5rem;         /* 8px */
  --spacing-2-5: 0.625rem;     /* 10px */
  --spacing-3: 0.75rem;        /* 12px */
  --spacing-3-5: 0.875rem;     /* 14px */
  --spacing-4: 1rem;           /* 16px */
  --spacing-5: 1.25rem;        /* 20px */
  --spacing-6: 1.5rem;         /* 24px */
  --spacing-7: 1.75rem;        /* 28px */
  --spacing-8: 2rem;           /* 32px */
  --spacing-9: 2.25rem;        /* 36px */
  --spacing-10: 2.5rem;        /* 40px */
  --spacing-11: 2.75rem;       /* 44px */
  --spacing-12: 3rem;          /* 48px */
  --spacing-14: 3.5rem;        /* 56px */
  --spacing-16: 4rem;          /* 64px */
  --spacing-20: 5rem;          /* 80px */
  --spacing-24: 6rem;          /* 96px */
  --spacing-28: 7rem;          /* 112px */
  --spacing-32: 8rem;          /* 128px */
  --spacing-36: 9rem;          /* 144px */
  --spacing-40: 10rem;         /* 160px */
  --spacing-44: 11rem;         /* 176px */
  --spacing-48: 12rem;         /* 192px */
  --spacing-52: 13rem;         /* 208px */
  --spacing-56: 14rem;         /* 224px */
  --spacing-60: 15rem;         /* 240px */
  --spacing-64: 16rem;         /* 256px */
  --spacing-72: 18rem;         /* 288px */
  --spacing-80: 20rem;         /* 320px */
  --spacing-96: 24rem;         /* 384px */
  
  /* ========================================
     尺寸系统 (Size System)
   ======================================== */
  
  /* 宽度 */
  --width-xs: 20rem;           /* 320px */
  --width-sm: 24rem;           /* 384px */
  --width-md: 28rem;           /* 448px */
  --width-lg: 32rem;           /* 512px */
  --width-xl: 36rem;           /* 576px */
  --width-2xl: 42rem;          /* 672px */
  --width-3xl: 48rem;          /* 768px */
  --width-4xl: 56rem;          /* 896px */
  --width-5xl: 64rem;          /* 1024px */
  --width-6xl: 72rem;          /* 1152px */
  --width-7xl: 80rem;          /* 1280px */
  --width-full: 100%;
  --width-screen: 100vw;
  
  /* 高度 */
  --height-xs: 20rem;          /* 320px */
  --height-sm: 24rem;          /* 384px */
  --height-md: 28rem;          /* 448px */
  --height-lg: 32rem;          /* 512px */
  --height-xl: 36rem;          /* 576px */
  --height-2xl: 42rem;         /* 672px */
  --height-3xl: 48rem;         /* 768px */
  --height-4xl: 56rem;         /* 896px */
  --height-5xl: 64rem;         /* 1024px */
  --height-6xl: 72rem;         /* 1152px */
  --height-full: 100%;
  --height-screen: 100vh;
  
  /* 组件特定尺寸 */
  --sidebar-width: 16rem;      /* 256px */
  --sidebar-width-collapsed: 4rem; /* 64px */
  --navbar-height: 4rem;       /* 64px */
  --footer-height: 3rem;       /* 48px */
  
  /* ========================================
     边框系统 (Border System)
   ======================================== */
  
  /* 边框宽度 */
  --border-width-0: 0px;
  --border-width-1: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;
  
  /* 边框圆角 */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;    /* 2px */
  --border-radius-base: 0.25rem;   /* 4px */
  --border-radius-md: 0.375rem;    /* 6px */
  --border-radius-lg: 0.5rem;      /* 8px */
  --border-radius-xl: 0.75rem;     /* 12px */
  --border-radius-2xl: 1rem;       /* 16px */
  --border-radius-3xl: 1.5rem;     /* 24px */
  --border-radius-full: 50%;
  
  /* ========================================
     阴影系统 (Shadow System)
   ======================================== */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-none: none;
  
  /* 特殊阴影 */
  --shadow-outline: 0 0 0 3px rgba(59, 130, 246, 0.5);
  --shadow-outline-gray: 0 0 0 3px rgba(156, 163, 175, 0.5);
  
  /* ========================================
     过渡动画 (Transition System)
   ======================================== */
  
  /* 动画时长 */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
  
  /* 动画曲线 */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 常用过渡 */
  --transition-none: none;
  --transition-all: all var(--duration-150) var(--ease-in-out);
  --transition-colors: color var(--duration-150) var(--ease-in-out), 
                       background-color var(--duration-150) var(--ease-in-out), 
                       border-color var(--duration-150) var(--ease-in-out);
  --transition-opacity: opacity var(--duration-150) var(--ease-in-out);
  --transition-shadow: box-shadow var(--duration-150) var(--ease-in-out);
  --transition-transform: transform var(--duration-150) var(--ease-in-out);
  
  /* ========================================
     Z-Index系统 (Z-Index System)
   ======================================== */
  
  --z-index-0: 0;
  --z-index-10: 10;
  --z-index-20: 20;
  --z-index-30: 30;
  --z-index-40: 40;
  --z-index-50: 50;
  --z-index-auto: auto;
  
  /* 语义化层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
  
  /* ========================================
     断点系统 (Breakpoint System)
   ======================================== */
  
  --breakpoint-xs: 0px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
  
  /* 自定义断点 */
  --breakpoint-mobile: 767.98px;
  --breakpoint-tablet: 991.98px;
  --breakpoint-desktop: 1199.98px;
  
  /* ========================================
     表格专用设计令牌
   ======================================== */
  
  /* 表格颜色 */
  --table-bg: var(--color-white);
  --table-hover-bg: rgba(13, 110, 253, 0.04);
  --table-stripe-bg: rgba(0, 0, 0, 0.025);
  --table-border-color: var(--color-gray-200);
  --table-head-bg: var(--color-gray-50);
  --table-head-color: var(--color-gray-700);
  
  /* 表格间距 */
  --table-cell-padding-y: var(--spacing-2);
  --table-cell-padding-x: var(--spacing-3);
  --table-cell-padding-y-sm: var(--spacing-1);
  --table-cell-padding-x-sm: var(--spacing-2);
  
  /* 分页器 */
  --pagination-padding-y: var(--spacing-2);
  --pagination-padding-x: var(--spacing-3);
  --pagination-margin-x: var(--spacing-1);
  --pagination-border-radius: var(--border-radius-base);
  --pagination-hover-bg: var(--color-gray-100);
  --pagination-active-bg: var(--color-primary-500);
  --pagination-active-color: var(--color-white);
  --pagination-disabled-color: var(--color-gray-400);
  
  /* ========================================
     组件特定令牌
   ======================================== */
  
  /* 按钮 */
  --button-border-radius: var(--border-radius-base);
  --button-font-weight: var(--font-weight-medium);
  --button-transition: var(--transition-colors);
  
  /* 表单控件 */
  --input-border-radius: var(--border-radius-base);
  --input-border-color: var(--color-gray-300);
  --input-focus-border-color: var(--color-primary-500);
  --input-focus-ring: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  
  /* 卡片 */
  --card-border-radius: var(--border-radius-lg);
  --card-border-color: var(--color-gray-200);
  --card-shadow: var(--shadow-sm);
  
  /* 导航 */
  --nav-link-padding-y: var(--spacing-2);
  --nav-link-padding-x: var(--spacing-4);
  --nav-link-hover-color: var(--color-primary-500);
  --nav-link-active-color: var(--color-primary-600);
}

/* ========================================
   媒体查询预设 (Media Query Presets)
 ======================================== */

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  :root {
    /* 深色模式下的颜色覆盖 */
    --color-gray-50: #1f2937;
    --color-gray-100: #374151;
    --color-gray-200: #4b5563;
    --color-gray-300: #6b7280;
    --color-gray-400: #9ca3af;
    --color-gray-500: #d1d5db;
    --color-gray-600: #e5e7eb;
    --color-gray-700: #f3f4f6;
    --color-gray-800: #f9fafb;
    --color-gray-900: #ffffff;
    
    --table-bg: var(--color-gray-800);
    --table-head-bg: var(--color-gray-700);
    --table-border-color: var(--color-gray-600);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --table-border-color: var(--color-black);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --duration-75: 0ms;
    --duration-100: 0ms;
    --duration-150: 0ms;
    --duration-200: 0ms;
    --duration-300: 0ms;
    --duration-500: 0ms;
    --duration-700: 0ms;
    --duration-1000: 0ms;
    
    --transition-none: none;
    --transition-all: none;
    --transition-colors: none;
    --transition-opacity: none;
    --transition-shadow: none;
    --transition-transform: none;
  }
}