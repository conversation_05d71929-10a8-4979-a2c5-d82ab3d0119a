{% extends "base.html" %}

{% block title %}回执单生成器{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">回执单生成器</h1>
        </div>

        <!-- 显示提示消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">填写回执单信息</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="product_type" class="form-label">产品类型</label>
                                <select class="form-select" id="product_type" name="product_type" required>
                                    <option value="">请选择产品类型</option>
                                    {% for product_type in product_options %}
                                    <option value="{{ product_type }}">{{ product_type }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    请选择产品类型
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="merchant" class="form-label">出租商户</label>
                                <select class="form-select" id="merchant" name="merchant" required>
                                    <option value="">请选择商户</option>
                                    {% for merchant in merchant_options %}
                                    <option value="{{ merchant }}">{{ merchant }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    请选择商户
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">客户姓名</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                                <div class="invalid-feedback">
                                    请输入客户姓名
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="order_id" class="form-label">订单编号</label>
                                <input type="text" class="form-control" id="order_id" name="order_id" required>
                                <div class="invalid-feedback">
                                    请输入订单编号
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="model" class="form-label">设备型号</label>
                                <input type="text" class="form-control" id="model" name="model" required>
                                <div class="invalid-feedback">
                                    请输入设备型号
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="serial" class="form-label">设备串号</label>
                                <textarea class="form-control" id="serial" name="serial" rows="3" required></textarea>
                                <div class="form-text">多个串号请换行分隔</div>
                                <div class="invalid-feedback">
                                    请输入设备串号
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="device_count" class="form-label">设备数量</label>
                                <input type="number" class="form-control" id="device_count" name="device_count" min="1" value="1" required>
                                <div class="invalid-feedback">
                                    请输入设备数量
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary" id="generateBtn">生成回执单</button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-grid">
                                        <a href="{{ url_for('main.batch_receipt_generator') }}" class="btn btn-outline-primary">批量生成回执单</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载中提示 -->
<div class="modal fade" id="loadingModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <h5>正在生成回执单文件，请稍候...</h5>
                <p class="text-muted small">文件生成完成后将自动下载</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 表单验证
    (function () {
        'use strict'
        
        // 页面加载完成时确保关闭全局加载指示器
        document.addEventListener('DOMContentLoaded', function() {
            // 确保加载指示器被隐藏
            if (typeof hideLoading === 'function') {
                hideLoading();
            }
            
            // 确保模态框加载指示器被隐藏
            var loadingModal = document.getElementById('loadingModal');
            if (loadingModal) {
                var bsModal = bootstrap.Modal.getInstance(loadingModal);
                if (bsModal) {
                    bsModal.hide();
                }
            }
        });
        
        // 获取所有需要验证的表单
        var forms = document.querySelectorAll('.needs-validation')
        
        // 阻止提交并应用验证样式
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    } else {
                        // 当表单验证通过并提交时，显示加载中
                        var loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
                        loadingModal.show();
                        
                        // 处理下载开始事件 - 多种方式尝试关闭加载提示
                        // 1. 设置超时自动关闭
                        setTimeout(function() {
                            loadingModal.hide();
                        }, 5000); // 5秒后自动关闭
                        
                        // 2. 监听窗口焦点变化
                        window.addEventListener('focus', function() {
                            setTimeout(function() {
                                loadingModal.hide();
                            }, 500);
                        }, { once: true });
                        
                        // 3. 监听beforeunload事件
                        window.addEventListener('beforeunload', function() {
                            loadingModal.hide();
                        }, { once: true });
                    }
                    
                    form.classList.add('was-validated')
                }, false)
            })
    })()
    
    // 产品类型改变时处理逻辑
    document.getElementById('product_type').addEventListener('change', function() {
        const merchantField = document.getElementById('merchant');
        const merchantContainer = merchantField.closest('.mb-3');
        
        // 如果选择了"天还"，则禁用商户选择（使用固定商户）
        if (this.value === '天还') {
            merchantField.disabled = true;
            merchantContainer.style.opacity = '0.6';
        } else {
            merchantField.disabled = false;
            merchantContainer.style.opacity = '1';
        }
    });
</script>
{% endblock %} 