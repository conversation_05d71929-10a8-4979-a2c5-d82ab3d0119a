<template>
  <div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            {{ getGreeting() }}，{{ authStore.displayName }}！
          </h1>
          <p class="welcome-subtitle">
            欢迎使用太享查询系统，今天是 {{ currentDate }}
          </p>
        </div>
        <div class="welcome-actions">
          <el-button type="primary" :icon="Search" @click="goToQuery">
            开始查询
          </el-button>
          <el-button :icon="Document" @click="goToSummary">
            数据汇总
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div
        v-for="stat in statsCards"
        :key="stat.key"
        class="stat-card"
        :class="stat.color"
      >
        <div class="stat-icon">
          <el-icon :size="24">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            {{ formatStatValue(stat.value, stat.type) }}
          </div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-change" :class="stat.trend">
            <el-icon :size="12">
              <component :is="stat.trend === 'up' ? 'TrendCharts' : 'Bottom'" />
            </el-icon>
            {{ stat.change }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧内容 -->
      <div class="content-left">
        <!-- 快速操作 -->
        <div class="quick-actions-card">
          <div class="card-header">
            <h3>快速操作</h3>
          </div>
          <div class="quick-actions-grid">
            <div
              v-for="action in quickActions"
              :key="action.key"
              class="quick-action-item"
              @click="handleQuickAction(action)"
            >
              <div class="action-icon" :class="action.color">
                <el-icon :size="20">
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-content">
                <div class="action-title">{{ action.title }}</div>
                <div class="action-desc">{{ action.description }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 最近查询 -->
        <div class="recent-queries-card">
          <div class="card-header">
            <h3>最近查询</h3>
            <el-button type="primary" text @click="clearRecentQueries">
              清空
            </el-button>
          </div>
          <div class="recent-queries-list">
            <div
              v-for="query in recentQueries"
              :key="query.id"
              class="recent-query-item"
              @click="repeatQuery(query)"
            >
              <div class="query-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div class="query-content">
                <div class="query-title">{{ query.title }}</div>
                <div class="query-time">{{ formatRelativeTime(query.time) }}</div>
              </div>
              <div class="query-action">
                <el-icon><Right /></el-icon>
              </div>
            </div>
            
            <div v-if="recentQueries.length === 0" class="empty-queries">
              <el-empty description="暂无查询记录" :image-size="80" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧内容 -->
      <div class="content-right">
        <!-- 数据概览图表 -->
        <div class="chart-card">
          <div class="card-header">
            <h3>数据概览</h3>
            <el-select v-model="chartPeriod" size="small" style="width: 120px">
              <el-option label="最近7天" value="7d" />
              <el-option label="最近30天" value="30d" />
              <el-option label="最近90天" value="90d" />
            </el-select>
          </div>
          <div class="chart-container">
            <ChartContainer
              title=""
              :data="overviewChartData"
              :loading="chartLoading"
              :height="'300px'"
              :exportable="false"
              :allow-fullscreen="false"
              @refresh="loadChartData"
            />
          </div>
        </div>
        
        <!-- 系统状态 -->
        <div class="system-status-card">
          <div class="card-header">
            <h3>系统状态</h3>
            <el-tag :type="systemStatus.type" size="small">
              {{ systemStatus.text }}
            </el-tag>
          </div>
          <div class="status-list">
            <div
              v-for="item in systemStatusItems"
              :key="item.key"
              class="status-item"
            >
              <div class="status-label">{{ item.label }}</div>
              <div class="status-value" :class="item.status">
                <el-icon :size="14">
                  <component :is="item.status === 'normal' ? 'SuccessFilled' : 'WarningFilled'" />
                </el-icon>
                {{ item.value }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Search,
  Document,
  TrendCharts,
  Bottom,
  Right,
  DataAnalysis,
  User,
  Files,
  Tools,
  Calendar,
  Calculator,
  QrCode,
  SuccessFilled,
  WarningFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ChartContainer from '@/components/business/ChartContainer.vue'
import { useAuthStore } from '@/stores/auth'
import { useDataStore } from '@/stores/data'
import { formatCurrency, formatNumber, formatRelativeTime } from '@/utils/format'
import type { ChartData } from '@/types/data'

const router = useRouter()
const authStore = useAuthStore()
const dataStore = useDataStore()

// 响应式数据
const currentDate = ref('')
const chartPeriod = ref('30d')
const chartLoading = ref(false)
const overviewChartData = ref<ChartData | null>(null)
const recentQueries = ref<any[]>([])

// 统计卡片数据
const statsCards = ref([
  {
    key: 'totalOrders',
    label: '总订单数',
    value: 0,
    type: 'number',
    icon: 'Files',
    color: 'bg-blue-500',
    change: '+12.5%',
    trend: 'up'
  },
  {
    key: 'totalAmount',
    label: '总金额',
    value: 0,
    type: 'currency',
    icon: 'TrendCharts',
    color: 'bg-green-500',
    change: '+8.2%',
    trend: 'up'
  },
  {
    key: 'activeCustomers',
    label: '活跃客户',
    value: 0,
    type: 'number',
    icon: 'User',
    color: 'bg-purple-500',
    change: '+5.1%',
    trend: 'up'
  },
  {
    key: 'overdueOrders',
    label: '逾期订单',
    value: 0,
    type: 'number',
    icon: 'WarningFilled',
    color: 'bg-red-500',
    change: '-2.3%',
    trend: 'down'
  }
])

// 快速操作
const quickActions = ref([
  {
    key: 'dataQuery',
    title: '数据查询',
    description: '查询订单和客户数据',
    icon: 'Search',
    color: 'text-blue-500',
    route: '/data/query'
  },
  {
    key: 'customerSummary',
    title: '客户汇总',
    description: '查看客户详细信息',
    icon: 'User',
    color: 'text-green-500',
    route: '/customer'
  },
  {
    key: 'dataAnalysis',
    title: '数据分析',
    description: '生成数据分析报告',
    icon: 'DataAnalysis',
    color: 'text-purple-500',
    route: '/data/summary'
  },
  {
    key: 'tools',
    title: '工具箱',
    description: '实用工具集合',
    icon: 'Tools',
    color: 'text-orange-500',
    route: '/tools'
  }
])

// 系统状态
const systemStatus = ref({
  type: 'success' as const,
  text: '运行正常'
})

const systemStatusItems = ref([
  {
    key: 'api',
    label: 'API服务',
    value: '正常',
    status: 'normal'
  },
  {
    key: 'database',
    label: '数据库',
    value: '正常',
    status: 'normal'
  },
  {
    key: 'cache',
    label: '缓存服务',
    value: '正常',
    status: 'normal'
  },
  {
    key: 'lastUpdate',
    label: '最后更新',
    value: '2分钟前',
    status: 'normal'
  }
])

// 计算属性
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 14) return '中午好'
  if (hour < 17) return '下午好'
  if (hour < 19) return '傍晚好'
  return '晚上好'
}

// 方法
const formatStatValue = (value: number, type: string) => {
  switch (type) {
    case 'currency':
      return formatCurrency(value, { showSymbol: false })
    case 'number':
      return formatNumber(value)
    default:
      return value.toString()
  }
}

const goToQuery = () => {
  router.push('/data/query')
}

const goToSummary = () => {
  router.push('/data/summary')
}

const handleQuickAction = (action: any) => {
  if (action.route) {
    router.push(action.route)
  }
}

const repeatQuery = (query: any) => {
  // 重复执行查询
  router.push({
    path: '/data/query',
    query: query.params
  })
}

const clearRecentQueries = () => {
  recentQueries.value = []
  localStorage.removeItem('recentQueries')
  ElMessage.success('已清空查询记录')
}

const loadChartData = async () => {
  chartLoading.value = true
  
  try {
    await dataStore.fetchChartData('orders', { period: chartPeriod.value })
    overviewChartData.value = dataStore.chartData.orders
  } catch (error) {
    console.error('加载图表数据失败:', error)
  } finally {
    chartLoading.value = false
  }
}

const loadStatsData = async () => {
  try {
    await dataStore.fetchSummaryStats()
    
    if (dataStore.summaryStats) {
      const stats = dataStore.summaryStats
      statsCards.value[0].value = stats.totalOrders
      statsCards.value[1].value = stats.totalFinance
      statsCards.value[2].value = stats.activeCustomers
      statsCards.value[3].value = stats.overdueOrders
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadRecentQueries = () => {
  const saved = localStorage.getItem('recentQueries')
  if (saved) {
    try {
      recentQueries.value = JSON.parse(saved).slice(0, 5)
    } catch (error) {
      console.error('解析查询记录失败:', error)
    }
  }
}

const updateCurrentDate = () => {
  const now = new Date()
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

// 定时器
let dateTimer: NodeJS.Timeout

// 生命周期
onMounted(async () => {
  updateCurrentDate()
  dateTimer = setInterval(updateCurrentDate, 60000) // 每分钟更新一次
  
  // 加载数据
  await Promise.all([
    loadStatsData(),
    loadChartData(),
    loadRecentQueries()
  ])
})

onUnmounted(() => {
  if (dateTimer) {
    clearInterval(dateTimer)
  }
})

// 监听图表周期变化
watch(() => chartPeriod.value, () => {
  loadChartData()
})
</script>

<style scoped>
.dashboard-container {
  @apply space-y-6;
}

/* 欢迎横幅 */
.welcome-banner {
  @apply bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white;
}

.welcome-content {
  @apply flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4;
}

.welcome-title {
  @apply text-2xl font-bold mb-1;
}

.welcome-subtitle {
  @apply text-blue-100;
}

.welcome-actions {
  @apply flex gap-3;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4;
}

.stat-card {
  @apply bg-white rounded-lg p-6 shadow-sm border border-gray-200 flex items-center gap-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center text-white;
}

.stat-content {
  @apply flex-1;
}

.stat-value {
  @apply text-2xl font-bold text-gray-800 mb-1;
}

.stat-label {
  @apply text-gray-600 text-sm mb-2;
}

.stat-change {
  @apply text-xs flex items-center gap-1;
}

.stat-change.up {
  @apply text-green-600;
}

.stat-change.down {
  @apply text-red-600;
}

/* 主要内容 */
.main-content {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-6;
}

.content-left {
  @apply lg:col-span-2 space-y-6;
}

.content-right {
  @apply space-y-6;
}

/* 卡片样式 */
.quick-actions-card,
.recent-queries-card,
.chart-card,
.system-status-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.card-header {
  @apply flex justify-between items-center p-6 border-b border-gray-100;
}

.card-header h3 {
  @apply text-lg font-semibold text-gray-800;
}

/* 快速操作 */
.quick-actions-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-4 p-6;
}

.quick-action-item {
  @apply flex items-center gap-4 p-4 rounded-lg border border-gray-100 hover:border-blue-200 hover:bg-blue-50 cursor-pointer transition-all;
}

.action-icon {
  @apply w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center;
}

.action-title {
  @apply font-medium text-gray-800;
}

.action-desc {
  @apply text-sm text-gray-600;
}

/* 最近查询 */
.recent-queries-list {
  @apply p-6 space-y-3;
}

.recent-query-item {
  @apply flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-all;
}

.query-icon {
  @apply w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center;
}

.query-content {
  @apply flex-1;
}

.query-title {
  @apply font-medium text-gray-800 text-sm;
}

.query-time {
  @apply text-xs text-gray-500;
}

.query-action {
  @apply text-gray-400;
}

.empty-queries {
  @apply py-8;
}

/* 图表卡片 */
.chart-container {
  @apply p-6 pt-0;
}

/* 系统状态 */
.status-list {
  @apply p-6 space-y-4;
}

.status-item {
  @apply flex justify-between items-center;
}

.status-label {
  @apply text-gray-600;
}

.status-value {
  @apply flex items-center gap-2 text-sm;
}

.status-value.normal {
  @apply text-green-600;
}

.status-value.warning {
  @apply text-yellow-600;
}

.status-value.error {
  @apply text-red-600;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .welcome-banner {
    @apply p-4;
  }
  
  .welcome-title {
    @apply text-xl;
  }
  
  .stat-card {
    @apply p-4;
  }
  
  .stat-value {
    @apply text-xl;
  }
  
  .quick-actions-grid {
    @apply grid-cols-1 p-4;
  }
  
  .card-header {
    @apply p-4;
  }
  
  .recent-queries-list,
  .chart-container,
  .status-list {
    @apply p-4;
  }
}
</style>
