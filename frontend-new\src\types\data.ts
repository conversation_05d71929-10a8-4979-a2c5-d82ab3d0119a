// 数据相关类型定义

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  timestamp?: string
}

// 查询参数
export interface QueryParams {
  startDate?: string
  endDate?: string
  customerName?: string
  orderNumber?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 订单数据
export interface OrderData {
  id: string
  orderNumber: string
  orderDate: string
  customerName: string
  productType: string
  totalFinance: number
  currentReceivable: number
  totalPeriods: number
  currentReceivablePeriods: number
  devicesCount: number
  businessType: string
  status: OrderStatus
  overdueStatus?: OverdueStatus
}

export type OrderStatus = 'active' | 'completed' | 'cancelled' | 'overdue'

export interface OverdueStatus {
  isOverdue: boolean
  overdueDays: number
  overdueAmount: number
}

// 客户汇总数据
export interface CustomerSummary {
  customerName: string
  totalOrders: number
  totalFinance: number
  currentReceivable: number
  overdueAmount: number
  deviceCount: number
  receivableByPeriods: ReceivablePeriod[]
  orderDetails: OrderData[]
  financeRecords: FinanceRecord[]
}

export interface ReceivablePeriod {
  period: number
  orderCount: number
  devicesCount: number
  amount: number
  overdueDays: number
  status: 'normal' | 'overdue'
}

export interface FinanceRecord {
  id: string
  transactionDate: string
  transactionId: string
  transactionType: string
  amount: number
  flowDirection: 'in' | 'out'
  orderNumber?: string
  remarks?: string
}

// 图表数据
export interface ChartData {
  labels: string[]
  datasets: ChartDataset[]
}

export interface ChartDataset {
  label: string
  data: number[]
  backgroundColor?: string | string[]
  borderColor?: string | string[]
  borderWidth?: number
}

// 汇总统计
export interface SummaryStats {
  totalOrders: number
  totalFinance: number
  totalReceivable: number
  overdueOrders: number
  overdueAmount: number
  activeCustomers: number
}

// 导出配置
export interface ExportConfig {
  format: 'excel' | 'csv' | 'pdf'
  filename?: string
  includeCharts?: boolean
  dateRange?: {
    start: string
    end: string
  }
  columns?: string[]
}

// 表格配置
export interface TableConfig {
  columns: TableColumn[]
  pagination?: PaginationConfig
  sortable?: boolean
  filterable?: boolean
  exportable?: boolean
}

export interface TableColumn {
  key: string
  label: string
  width?: number | string
  sortable?: boolean
  filterable?: boolean
  formatter?: (value: any, row: any) => string
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right'
}

export interface PaginationConfig {
  page: number
  pageSize: number
  total: number
  pageSizes: number[]
}

// 搜索配置
export interface SearchConfig {
  placeholder: string
  fields: SearchField[]
  quickFilters?: QuickFilter[]
}

export interface SearchField {
  key: string
  label: string
  type: 'text' | 'date' | 'select' | 'number'
  options?: SelectOption[]
}

export interface SelectOption {
  label: string
  value: string | number
}

export interface QuickFilter {
  label: string
  value: any
  field: string
}
