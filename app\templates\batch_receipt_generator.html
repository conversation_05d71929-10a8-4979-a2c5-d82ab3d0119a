{% extends "base.html" %}

{% block title %}批量回执单生成器{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">批量回执单生成器</h1>
        </div>

        <!-- 显示提示消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">批量生成回执单</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="product_type" class="form-label">产品类型</label>
                                <select class="form-select" id="product_type" name="product_type" required>
                                    <option value="">请选择产品类型</option>
                                    {% for product_type in product_options %}
                                    <option value="{{ product_type }}">{{ product_type }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    请选择产品类型
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="merchant" class="form-label">出租商户</label>
                                <select class="form-select" id="merchant" name="merchant" required>
                                    <option value="">请选择商户</option>
                                    {% for merchant in merchant_options %}
                                    <option value="{{ merchant }}">{{ merchant }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    请选择商户
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="excel_file" class="form-label">上传Excel文件</label>
                                <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xls,.xlsx" required>
                                <div class="form-text">
                                    请上传包含订单数据的Excel文件（.xls 或 .xlsx 格式）
                                </div>
                                <div class="invalid-feedback">
                                    请选择Excel文件
                                </div>
                            </div>
                            
                            <div class="alert alert-info mt-3">
                                <h6 class="alert-heading">文件要求：</h6>
                                <p class="mb-0">Excel文件需包含以下字段：下单姓名/客户姓名、订单ID/订单编号、商品名称/型号、设备串号、用户备注</p>
                                <p class="mb-0 mt-2">用户备注字段用于指定设备数量，如无则默认为1</p>
                                <p class="mb-0 mt-2">可包含多个"设备串号"前缀的列，所有列会自动合并</p>
                            </div>
                            
                            <div class="d-grid mt-4">
                                <button type="submit" class="btn btn-primary" id="generateBtn">批量生成回执单</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载中提示 -->
<div class="modal fade" id="loadingModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <h5>正在批量生成回执单文件，请稍候...</h5>
                <p class="text-muted small">文件生成完成后将自动下载</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 页面加载完成时确保关闭全局加载指示器
    document.addEventListener('DOMContentLoaded', function() {
        // 确保加载指示器被隐藏
        if (typeof hideLoading === 'function') {
            hideLoading();
        }
        
        // 确保模态框加载指示器被隐藏
        var loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            var bsModal = bootstrap.Modal.getInstance(loadingModal);
            if (bsModal) {
                bsModal.hide();
            }
        }
    });
    
    // 表单验证
    (function () {
        'use strict'
        
        // 获取所有需要验证的表单
        var forms = document.querySelectorAll('.needs-validation')
        
        // 阻止提交并应用验证样式
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    } else {
                        // 当表单验证通过并提交时，显示加载中
                        var loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
                        loadingModal.show();
                        
                        // 处理下载开始事件 - 多种方式尝试关闭加载提示
                        // 1. 设置超时自动关闭
                        setTimeout(function() {
                            loadingModal.hide();
                        }, 60000); // 60秒后自动关闭，批量处理可能需要更长时间
                        
                        // 2. 监听窗口焦点变化
                        window.addEventListener('focus', function() {
                            setTimeout(function() {
                                loadingModal.hide();
                            }, 500);
                        }, { once: true });
                        
                        // 3. 监听beforeunload事件
                        window.addEventListener('beforeunload', function() {
                            loadingModal.hide();
                        }, { once: true });
                    }
                    
                    form.classList.add('was-validated')
                }, false)
            })
    })()
    
    // 产品类型改变时处理逻辑
    document.getElementById('product_type').addEventListener('change', function() {
        const merchantField = document.getElementById('merchant');
        const merchantContainer = merchantField.closest('.mb-3');
        
        // 如果选择了"天还"，则禁用商户选择（使用固定商户）
        if (this.value === '天还') {
            merchantField.disabled = true;
            merchantContainer.style.opacity = '0.6';
        } else {
            merchantField.disabled = false;
            merchantContainer.style.opacity = '1';
        }
    });
</script>
{% endblock %} 