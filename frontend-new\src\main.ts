import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'

// 导入全局样式
import './assets/styles/main.css'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 注册Element Plus
app.use(ElementPlus, {
  // 全局配置
  size: 'default',
  zIndex: 3000,
})

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册Pinia
app.use(pinia)

// 注册路由
app.use(router)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
  
  // 在生产环境中，可以将错误发送到错误监控服务
  if (import.meta.env.PROD) {
    // 发送错误到监控服务
    // errorReporting.captureException(err, { extra: { info } })
  }
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Global warning:', msg, trace)
}

// 全局属性
app.config.globalProperties.$ELEMENT = {
  size: 'default',
  zIndex: 3000
}

// 挂载应用
app.mount('#app')

// 开发环境下的调试工具
if (import.meta.env.DEV) {
  // 暴露应用实例到全局，方便调试
  window.__VUE_APP__ = app
  
  // 性能监控
  if (typeof window !== 'undefined' && window.performance) {
    window.addEventListener('load', () => {
      const timing = window.performance.timing
      const loadTime = timing.loadEventEnd - timing.navigationStart
      console.log(`页面加载时间: ${loadTime}ms`)
    })
  }
}

// 注册Service Worker（生产环境）
if (import.meta.env.PROD && 'serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration)
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}
