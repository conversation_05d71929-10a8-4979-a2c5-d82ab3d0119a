/**
 * QRCode Component - 二维码生成器组件
 * 独立可复用的二维码生成模块
 */

class QRCodeModule {
    constructor(triggerElement) {
        this.triggerElement = triggerElement;
        this.modal = null;
        this.form = null;
        this.contentInput = null;
        this.sizeSelect = null;
        this.generateBtn = null;
        this.resultDiv = null;
        this.errorDiv = null;
        this.loadingDiv = null;
        this.qrcodeImage = null;
        this.downloadBtn = null;
        this.copyBtn = null;
        
        this.init();
    }

    init() {
        this.bindTrigger();
        this.setupModal();
    }

    bindTrigger() {
        if (this.triggerElement) {
            this.triggerElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.show();
            });
        }
    }

    setupModal() {
        this.modal = document.getElementById('qrcodeModal');
        if (this.modal) {
            this.initializeElements();
            this.bindModalEvents();
            this.bindFormEvents();
        }
    }

    initializeElements() {
        this.form = this.modal.querySelector('#qrcodeForm');
        this.contentInput = this.modal.querySelector('#qrcodeContent');
        this.sizeSelect = this.modal.querySelector('#qrcodeSize');
        this.generateBtn = this.modal.querySelector('#generateQrcodeBtn');
        this.resultDiv = this.modal.querySelector('#qrcodeResult');
        this.errorDiv = this.modal.querySelector('#qrcodeError');
        this.loadingDiv = this.modal.querySelector('#qrcodeLoading');
        this.qrcodeImage = this.modal.querySelector('#qrcodeImage');
        this.downloadBtn = this.modal.querySelector('#downloadQrcodeBtn');
        this.copyBtn = this.modal.querySelector('#copyQrcodeBtn');
    }

    bindModalEvents() {
        // 模态框事件处理
        this.modal.addEventListener('show.bs.modal', () => {
            this.resetForm();
        });

        this.modal.addEventListener('shown.bs.modal', () => {
            if (this.contentInput) {
                this.contentInput.focus();
            }
        });

        this.modal.addEventListener('hide.bs.modal', () => {
            this.clearFocus();
        });

        this.modal.addEventListener('hidden.bs.modal', () => {
            this.resetForm();
            this.restoreFocus();
        });

        // 关闭按钮特殊处理
        const closeButtons = this.modal.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.clearFocus();
            });
        });
    }

    bindFormEvents() {
        if (this.generateBtn) {
            this.generateBtn.addEventListener('click', () => {
                this.generateQRCode();
            });
        }

        if (this.contentInput) {
            this.contentInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.generateQRCode();
                }
            });
        }

        if (this.downloadBtn) {
            this.downloadBtn.addEventListener('click', () => {
                this.downloadQRCode();
            });
        }

        if (this.copyBtn) {
            this.copyBtn.addEventListener('click', () => {
                this.copyQRCode();
            });
        }
    }

    async generateQRCode() {
        const content = this.contentInput.value.trim();
        const size = parseInt(this.sizeSelect.value);
        
        if (!content) {
            this.showError('请输入要生成二维码的内容');
            return;
        }

        if (content.length > 2000) {
            this.showError('内容长度不能超过2000个字符');
            return;
        }

        this.showLoading();

        try {
            const response = await this.requestQRCode(content, size);
            
            if (response.success) {
                this.showResult(response.image);
                this.currentQRCode = response.image;
            } else {
                this.showError(response.error || '生成二维码失败');
            }
        } catch (error) {
            console.error('生成二维码错误:', error);
            this.showError('网络错误，请重试');
        }
    }

    async requestQRCode(content, size) {
        const response = await fetch('/generate_qrcode', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                content: content,
                size: size
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    downloadQRCode() {
        if (this.currentQRCode) {
            const filename = `qrcode_${Date.now()}.png`;
            this.downloadImage(this.currentQRCode, filename);
            this.showSuccessMessage('二维码已下载');
        }
    }

    async copyQRCode() {
        if (!this.currentQRCode) return;

        try {
            // 将base64转换为blob
            const response = await fetch(this.currentQRCode);
            const blob = await response.blob();
            
            // 复制到剪贴板
            await navigator.clipboard.write([
                new ClipboardItem({ [blob.type]: blob })
            ]);
            
            this.showSuccessMessage('二维码已复制到剪贴板');
        } catch (error) {
            console.error('复制失败:', error);
            this.showErrorMessage('复制失败，请尝试手动保存');
        }
    }

    downloadImage(dataUrl, filename) {
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 状态显示方法
    showLoading() {
        this.hideAllStates();
        if (this.loadingDiv) {
            this.loadingDiv.style.display = 'block';
        }
    }

    showResult(imageData) {
        this.hideAllStates();
        if (this.resultDiv && this.qrcodeImage) {
            this.qrcodeImage.src = imageData;
            this.resultDiv.style.display = 'block';
        }
    }

    showError(message) {
        this.hideAllStates();
        if (this.errorDiv) {
            this.errorDiv.textContent = message;
            this.errorDiv.style.display = 'block';
        }
    }

    hideAllStates() {
        if (this.loadingDiv) this.loadingDiv.style.display = 'none';
        if (this.resultDiv) this.resultDiv.style.display = 'none';
        if (this.errorDiv) this.errorDiv.style.display = 'none';
    }

    resetForm() {
        if (this.contentInput) this.contentInput.value = '';
        if (this.sizeSelect) this.sizeSelect.value = '256';
        this.hideAllStates();
        this.currentQRCode = null;
    }

    // 模态框控制方法
    show() {
        const qrcodeModal = new bootstrap.Modal(this.modal);
        qrcodeModal.show();
    }

    hide() {
        const qrcodeModal = bootstrap.Modal.getInstance(this.modal);
        if (qrcodeModal) {
            qrcodeModal.hide();
        }
    }

    clearFocus() {
        const focusedElement = document.activeElement;
        if (focusedElement && this.modal.contains(focusedElement)) {
            focusedElement.blur();
        }
    }

    restoreFocus() {
        if (this.triggerElement && document.activeElement === document.body) {
            setTimeout(() => {
                this.triggerElement.focus();
            }, 100);
        }
    }

    // 消息提示方法
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showErrorMessage(message) {
        this.showMessage(message, 'danger');
    }

    showMessage(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // 自动淡出效果
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.3s';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 3000);
    }

    // 验证方法
    validateContent(content) {
        if (!content || content.trim().length === 0) {
            return { valid: false, message: '请输入要生成二维码的内容' };
        }
        
        if (content.length > 2000) {
            return { valid: false, message: '内容长度不能超过2000个字符' };
        }
        
        return { valid: true };
    }

    // 获取内容类型
    getContentType(content) {
        const urlPattern = /^https?:\/\/.+/i;
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const phonePattern = /^[\d\s\-\+\(\)]+$/;
        
        if (urlPattern.test(content)) {
            return 'url';
        } else if (emailPattern.test(content)) {
            return 'email';
        } else if (phonePattern.test(content) && content.replace(/\D/g, '').length >= 10) {
            return 'phone';
        } else {
            return 'text';
        }
    }

    // 销毁方法
    destroy() {
        // 清理事件监听器
        if (this.triggerElement) {
            this.triggerElement.removeEventListener('click', this.show);
        }
        
        if (this.generateBtn) {
            this.generateBtn.removeEventListener('click', this.generateQRCode);
        }
        
        if (this.contentInput) {
            this.contentInput.removeEventListener('keypress', this.handleKeyPress);
        }
        
        if (this.downloadBtn) {
            this.downloadBtn.removeEventListener('click', this.downloadQRCode);
        }
        
        if (this.copyBtn) {
            this.copyBtn.removeEventListener('click', this.copyQRCode);
        }
        
        // 清理模态框事件
        if (this.modal) {
            const events = ['show.bs.modal', 'shown.bs.modal', 'hide.bs.modal', 'hidden.bs.modal'];
            events.forEach(event => {
                this.modal.removeEventListener(event, this[event]);
            });
        }
    }
}

// 导出模块
window.QRCodeModule = QRCodeModule;