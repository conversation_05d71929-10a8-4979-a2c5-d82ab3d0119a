@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS变量定义 */
:root {
  /* 主色调 */
  --color-primary: #409eff;
  --color-primary-light: #79bbff;
  --color-primary-dark: #337ecc;
  
  /* 功能色 */
  --color-success: #67c23a;
  --color-warning: #e6a23c;
  --color-danger: #f56c6c;
  --color-info: #909399;
  
  /* 中性色 */
  --color-text-primary: #303133;
  --color-text-regular: #606266;
  --color-text-secondary: #909399;
  --color-text-placeholder: #c0c4cc;
  
  /* 边框色 */
  --color-border-base: #dcdfe6;
  --color-border-light: #e4e7ed;
  --color-border-lighter: #ebeef5;
  --color-border-extra-light: #f2f6fc;
  
  /* 背景色 */
  --color-bg-base: #f5f7fa;
  --color-bg-light: #fafafa;
  --color-bg-white: #ffffff;
  
  /* 阴影 */
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4px 20px 0 rgba(0, 0, 0, 0.2);
  
  /* 圆角 */
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 字体 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  
  /* 行高 */
  --line-height-base: 1.5;
  --line-height-tight: 1.25;
  --line-height-loose: 1.75;
  
  /* 过渡 */
  --transition-base: all 0.3s ease;
  --transition-fast: all 0.15s ease;
  --transition-slow: all 0.5s ease;
}

/* 暗色主题变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-text-primary: #e5eaf3;
    --color-text-regular: #cfd3dc;
    --color-text-secondary: #a3a6ad;
    --color-text-placeholder: #8d9095;
    
    --color-border-base: #4c4d4f;
    --color-border-light: #414243;
    --color-border-lighter: #363637;
    --color-border-extra-light: #2b2b2c;
    
    --color-bg-base: #1d1e1f;
    --color-bg-light: #141414;
    --color-bg-white: #262727;
  }
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: var(--line-height-base);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  background-color: var(--color-bg-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--color-primary-light);
}

/* 按钮基础样式 */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  cursor: pointer;
  border: none;
  background: none;
  outline: none;
}

/* 表单元素样式 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

/* 列表样式 */
ul, ol {
  list-style: none;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 工具类 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-wrap: break-word;
  word-break: break-all;
}

.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

/* 响应式工具类 */
.hidden-xs {
  @media (max-width: 639px) {
    display: none !important;
  }
}

.hidden-sm {
  @media (min-width: 640px) and (max-width: 767px) {
    display: none !important;
  }
}

.hidden-md {
  @media (min-width: 768px) and (max-width: 1023px) {
    display: none !important;
  }
}

.hidden-lg {
  @media (min-width: 1024px) {
    display: none !important;
  }
}

.visible-xs {
  @media (min-width: 640px) {
    display: none !important;
  }
}

.visible-sm {
  @media (max-width: 639px), (min-width: 768px) {
    display: none !important;
  }
}

.visible-md {
  @media (max-width: 767px), (min-width: 1024px) {
    display: none !important;
  }
}

.visible-lg {
  @media (max-width: 1023px) {
    display: none !important;
  }
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-base);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--transition-base);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.scale-enter-active,
.scale-leave-active {
  transition: all var(--transition-fast);
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 自定义滚动条 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-base) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--color-border-base);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-border-light);
}

/* 加载状态 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
  
  .print-avoid-break {
    page-break-inside: avoid;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --color-border-base: #000000;
    --color-text-primary: #000000;
    --color-bg-white: #ffffff;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
