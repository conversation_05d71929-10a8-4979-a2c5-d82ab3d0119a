# 功能测试清单

## 🔐 认证功能测试

### 登录页面
- [ ] 页面正常加载
- [ ] 验证码正常显示
- [ ] 验证码点击刷新
- [ ] 用户名输入验证
- [ ] 密码输入验证
- [ ] 验证码输入验证
- [ ] 记住我功能
- [ ] 登录成功跳转
- [ ] 登录失败提示
- [ ] 移动端适配

### 权限控制
- [ ] 有限权限用户功能限制
- [ ] 标准权限用户功能
- [ ] 完全权限用户功能
- [ ] 未授权页面访问拦截
- [ ] 登录过期处理

## 📊 数据查询功能

### 搜索表单
- [ ] 日期范围选择
- [ ] 客户名称搜索
- [ ] 订单编号搜索
- [ ] 状态筛选
- [ ] 快速筛选标签
- [ ] 表单重置功能
- [ ] 表单验证

### 数据表格
- [ ] 数据正常加载
- [ ] 分页功能
- [ ] 排序功能
- [ ] 列显示/隐藏
- [ ] 行选择功能
- [ ] 搜索功能
- [ ] 导出功能
- [ ] 刷新功能

### 响应式适配
- [ ] 桌面端表格显示
- [ ] 平板端表格适配
- [ ] 移动端卡片显示
- [ ] 触摸滑动操作

## 🏠 工作台功能

### 首页展示
- [ ] 欢迎信息显示
- [ ] 统计卡片数据
- [ ] 快速操作按钮
- [ ] 最近查询记录
- [ ] 数据概览图表
- [ ] 系统状态显示

### 交互功能
- [ ] 快速操作跳转
- [ ] 查询记录重复执行
- [ ] 图表类型切换
- [ ] 图表导出功能
- [ ] 通知中心

## 📈 图表功能

### 图表显示
- [ ] 折线图显示
- [ ] 柱状图显示
- [ ] 饼图显示
- [ ] 图表数据正确
- [ ] 图表交互功能

### 图表操作
- [ ] 图表类型切换
- [ ] 图表刷新
- [ ] 图表导出
- [ ] 全屏显示
- [ ] 移动端适配

## 👤 客户管理

### 客户列表
- [ ] 客户列表加载
- [ ] 客户搜索
- [ ] 客户详情查看
- [ ] 分页功能

### 客户详情
- [ ] 基本信息显示
- [ ] 订单汇总
- [ ] 待收明细
- [ ] 财务流水
- [ ] 标签页切换

## 🛠️ 工具功能

### 二维码生成
- [ ] 二维码生成
- [ ] 二维码下载
- [ ] 参数配置

### 计算器
- [ ] 基本计算功能
- [ ] 历史记录
- [ ] 清除功能

## 📱 移动端测试

### 基本功能
- [ ] 页面加载速度
- [ ] 触摸操作响应
- [ ] 滑动操作
- [ ] 缩放操作
- [ ] 横竖屏切换

### 导航功能
- [ ] 侧边栏展开/收起
- [ ] 底部导航
- [ ] 面包屑导航
- [ ] 返回按钮

## 🔧 系统功能

### 性能测试
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 2秒
- [ ] 大数据表格性能
- [ ] 内存使用情况
- [ ] 网络请求优化

### 兼容性测试
- [ ] Chrome 浏览器
- [ ] Firefox 浏览器
- [ ] Safari 浏览器
- [ ] Edge 浏览器
- [ ] 移动端浏览器

### 错误处理
- [ ] 网络错误提示
- [ ] API错误处理
- [ ] 404页面
- [ ] 500错误页面
- [ ] 权限错误提示

## 🚀 部署测试

### 开发环境
- [ ] 本地开发服务器启动
- [ ] 热重载功能
- [ ] 代码检查通过
- [ ] 类型检查通过

### 生产环境
- [ ] 构建成功
- [ ] Docker镜像构建
- [ ] 容器启动正常
- [ ] Nginx配置正确
- [ ] 静态资源加载

### 集成测试
- [ ] 前后端连接正常
- [ ] API代理配置
- [ ] CORS配置
- [ ] 认证流程
- [ ] 数据流转

## 📋 测试执行

### 自动化测试
```bash
# 前端测试
cd frontend-new
npm run lint
npm run type-check
npm run test

# 后端测试
python test_coze_network.py
python test_enterprise_credit_fix.py

# 集成测试
python test_integration.py
```

### 手动测试
1. 启动后端服务：`python run.py`
2. 启动前端服务：`cd frontend-new && npm run dev`
3. 访问 http://localhost:3000
4. 按照清单逐项测试

### 测试数据
- 测试用户账号：
  - 有限权限：`TT2024`
  - 标准权限：`881017`
  - 完全权限：`Doolin`
- 测试客户数据：使用现有数据库数据
- 测试订单数据：使用现有API数据

### 测试报告
记录测试结果，包括：
- 通过的功能点
- 发现的问题
- 性能数据
- 兼容性情况
- 改进建议
