import { http } from '@/utils/request'
import type { 
  LoginRequest, 
  LoginResponse, 
  CaptchaResponse, 
  User,
  Permission
} from '@/types/auth'

export const authApi = {
  // 获取验证码
  getCaptcha(): Promise<CaptchaResponse> {
    return http.get('/auth/captcha')
  },

  // 用户登录
  login(credentials: LoginRequest): Promise<LoginResponse> {
    return http.post('/auth/login', credentials)
  },

  // 用户登出
  logout(): Promise<void> {
    return http.post('/auth/logout')
  },

  // 获取当前用户信息
  getCurrentUser(): Promise<{ success: boolean; data: User }> {
    return http.get('/auth/user')
  },

  // 获取用户权限
  getUserPermissions(): Promise<{ success: boolean; data: Permission[] }> {
    return http.get('/auth/permissions')
  },

  // 刷新token
  refreshToken(): Promise<{ success: boolean; token: string }> {
    return http.post('/auth/refresh')
  },

  // 修改密码
  changePassword(data: {
    oldPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<{ success: boolean; message: string }> {
    return http.post('/auth/change-password', data)
  },

  // 检查用户名是否存在
  checkUsername(username: string): Promise<{ exists: boolean }> {
    return http.get('/auth/check-username', { username })
  }
}
