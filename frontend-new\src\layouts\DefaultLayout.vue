<template>
  <div class="app-layout">
    <!-- 移动端头部 -->
    <div v-if="isMobile" class="mobile-header">
      <div class="mobile-header-content">
        <el-button
          type="primary"
          text
          :icon="Menu"
          @click="toggleMobileSidebar"
          class="mobile-menu-btn"
        />
        <div class="mobile-title">{{ currentPageTitle }}</div>
        <div class="mobile-actions">
          <el-dropdown @command="handleUserAction">
            <el-avatar :size="32" :src="userAvatar">
              {{ userInitial }}
            </el-avatar>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div 
      class="app-sidebar"
      :class="{ 
        'sidebar-collapsed': !sidebarExpanded,
        'sidebar-mobile': isMobile,
        'sidebar-mobile-open': isMobile && mobileSidebarOpen
      }"
    >
      <AppSidebar
        :collapsed="!sidebarExpanded"
        :mobile="isMobile"
        @menu-select="handleMenuSelect"
      />
    </div>
    
    <!-- 主内容区域 -->
    <div 
      class="app-main"
      :class="{ 
        'main-expanded': !sidebarExpanded,
        'main-mobile': isMobile
      }"
    >
      <!-- 桌面端头部 -->
      <div v-if="!isMobile" class="desktop-header">
        <AppHeader
          :sidebar-collapsed="!sidebarExpanded"
          @toggle-sidebar="toggleSidebar"
        />
      </div>
      
      <!-- 页面内容 -->
      <div class="app-content">
        <!-- 面包屑导航 -->
        <div v-if="showBreadcrumb" class="breadcrumb-container">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="item in breadcrumbItems"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <!-- 路由视图 -->
        <div class="page-container">
          <router-view v-slot="{ Component, route }">
            <transition
              name="page-transition"
              mode="out-in"
              appear
            >
              <keep-alive :include="cachedViews">
                <component :is="Component" :key="route.path" />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </div>
      
      <!-- 页脚 -->
      <div v-if="showFooter" class="app-footer">
        <AppFooter />
      </div>
    </div>
    
    <!-- 移动端侧边栏遮罩 -->
    <div
      v-if="isMobile && mobileSidebarOpen"
      class="mobile-sidebar-overlay"
      @click="closeMobileSidebar"
    />
    
    <!-- 回到顶部 -->
    <el-backtop
      :right="isMobile ? 20 : 40"
      :bottom="isMobile ? 80 : 40"
      :visibility-height="300"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Menu } from '@element-plus/icons-vue'
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'
import AppFooter from '@/components/common/AppFooter.vue'
import { useAuthStore } from '@/stores/auth'
import { useResponsive } from '@/composables/useResponsive'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const { isMobile } = useResponsive()

// 响应式数据
const sidebarExpanded = ref(!isMobile.value)
const mobileSidebarOpen = ref(false)
const cachedViews = ref<string[]>([])

// 计算属性
const currentPageTitle = computed(() => {
  return route.meta.title as string || '太享查询系统'
})

const userAvatar = computed(() => {
  return authStore.user?.avatar || ''
})

const userInitial = computed(() => {
  return authStore.user?.username?.charAt(0).toUpperCase() || 'U'
})

const showBreadcrumb = computed(() => {
  return !isMobile.value && route.meta.showBreadcrumb !== false
})

const showFooter = computed(() => {
  return route.meta.showFooter !== false
})

const breadcrumbItems = computed(() => {
  const items = []
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  
  // 添加首页
  if (route.path !== '/dashboard') {
    items.push({
      title: '工作台',
      path: '/dashboard'
    })
  }
  
  // 添加匹配的路由
  matched.forEach(match => {
    if (match.meta.title && match.path !== '/') {
      items.push({
        title: match.meta.title as string,
        path: match.path
      })
    }
  })
  
  return items
})

// 方法
const toggleSidebar = () => {
  sidebarExpanded.value = !sidebarExpanded.value
  localStorage.setItem('sidebarExpanded', String(sidebarExpanded.value))
}

const toggleMobileSidebar = () => {
  mobileSidebarOpen.value = !mobileSidebarOpen.value
}

const closeMobileSidebar = () => {
  mobileSidebarOpen.value = false
}

const handleMenuSelect = (path: string) => {
  if (isMobile.value) {
    closeMobileSidebar()
  }
  
  if (path !== route.path) {
    router.push(path)
  }
}

const handleUserAction = async (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      router.push('/profile')
      break
    case 'settings':
      // 跳转到设置页面
      router.push('/settings')
      break
    case 'logout':
      try {
        await authStore.logout()
        router.push('/auth/login')
      } catch (error) {
        console.error('Logout failed:', error)
      }
      break
  }
}

const handleResize = () => {
  if (isMobile.value) {
    sidebarExpanded.value = false
    mobileSidebarOpen.value = false
  } else {
    mobileSidebarOpen.value = false
    // 恢复桌面端侧边栏状态
    const saved = localStorage.getItem('sidebarExpanded')
    sidebarExpanded.value = saved !== null ? saved === 'true' : true
  }
}

// 监听路由变化
watch(route, (newRoute) => {
  // 关闭移动端侧边栏
  if (isMobile.value) {
    closeMobileSidebar()
  }
  
  // 管理缓存视图
  if (newRoute.meta.keepAlive) {
    const componentName = newRoute.name as string
    if (componentName && !cachedViews.value.includes(componentName)) {
      cachedViews.value.push(componentName)
    }
  }
})

// 监听屏幕尺寸变化
watch(isMobile, handleResize)

// 生命周期
onMounted(() => {
  // 恢复侧边栏状态
  const saved = localStorage.getItem('sidebarExpanded')
  if (saved !== null && !isMobile.value) {
    sidebarExpanded.value = saved === 'true'
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.app-layout {
  @apply min-h-screen bg-gray-50;
  display: flex;
  flex-direction: column;
}

/* 移动端头部 */
.mobile-header {
  @apply bg-white border-b border-gray-200 sticky top-0 z-40;
  height: 56px;
}

.mobile-header-content {
  @apply flex items-center justify-between px-4 h-full;
}

.mobile-title {
  @apply text-lg font-semibold text-gray-800 flex-1 text-center;
}

.mobile-menu-btn {
  @apply text-xl;
}

.mobile-actions {
  @apply flex items-center;
}

/* 侧边栏 */
.app-sidebar {
  @apply fixed left-0 top-0 h-full bg-white border-r border-gray-200 z-50;
  width: 240px;
  transition: transform 0.3s ease;
}

.sidebar-collapsed {
  width: 64px;
}

.sidebar-mobile {
  @apply top-14;
  height: calc(100vh - 56px);
  transform: translateX(-100%);
}

.sidebar-mobile-open {
  transform: translateX(0);
}

/* 主内容区域 */
.app-main {
  @apply flex-1 flex flex-col;
  margin-left: 240px;
  transition: margin-left 0.3s ease;
}

.main-expanded {
  margin-left: 64px;
}

.main-mobile {
  margin-left: 0;
  margin-top: 56px;
}

/* 桌面端头部 */
.desktop-header {
  @apply bg-white border-b border-gray-200 sticky top-0 z-30;
  height: 64px;
}

/* 页面内容 */
.app-content {
  @apply flex-1 p-4;
  min-height: calc(100vh - 64px);
}

.main-mobile .app-content {
  min-height: calc(100vh - 56px);
}

.breadcrumb-container {
  @apply mb-4 px-1;
}

.page-container {
  @apply min-h-full;
}

/* 页脚 */
.app-footer {
  @apply bg-white border-t border-gray-200 mt-auto;
}

/* 移动端遮罩 */
.mobile-sidebar-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  top: 56px;
}

/* 页面切换动画 */
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s ease;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-sidebar {
    width: 280px;
  }
  
  .app-content {
    padding: 1rem;
  }
}

@media (max-width: 640px) {
  .app-content {
    padding: 0.5rem;
  }
}
</style>
