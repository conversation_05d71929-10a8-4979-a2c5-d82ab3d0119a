# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

**Development Server:**
```bash
# Start development server
python run.py

# Create virtual environment and install dependencies
python -m venv venv
source venv/bin/activate  # Linux/macOS
pip install -r requirements.txt
```

**Production Deployment:**
```bash
# Deploy with provided script
chmod +x deploy.sh
sudo ./deploy.sh

# Manual gunicorn deployment
gunicorn -c gunicorn_config.py run:app

# Service management (after deployment)
sudo systemctl start hdsc-query
sudo systemctl stop hdsc-query
sudo systemctl restart hdsc-query
sudo systemctl status hdsc-query
```

**Docker Deployment:**
```bash
# Build and run with Docker Compose
docker-compose up -d

# Check deployment status
bash check-deployment.sh
```

## High-Level Architecture

### Flask Application Structure
- **Entry Point**: `run.py` creates app instance with scheduled task initialization
- **App Factory**: `app/__init__.py` implements application factory pattern with blueprint registration
- **Configuration**: `config.py` provides multi-environment configuration (dev/prod/test)

### Blueprint Organization
```
app/routes/
├── auth.py              # Authentication (login/logout/captcha)
├── main_refactored.py   # Core pages and navigation
├── api.py               # REST API endpoints
├── query_routes.py      # Data querying and filtering
├── export_routes.py     # Excel/CSV export functionality
├── order_cleaner.py     # Data cleaning tools (/tools prefix)
└── async_routes.py      # Async task handling
```

### Data Access Pattern
- **Proxy API Architecture**: No direct database access
- **External API Integration**: Connects to `http://*************:5000` with fallback URLs
- **Service Layer**: `app/services/data_service.py` provides high-level data operations
- **Multi-level Caching**: Flask-Caching + smart cache system with different timeouts per data type

### Authentication System  
- **File-based Auth**: No database, uses configuration-based user management
- **Three-tier Permissions**: limited (`TT2024`), standard (`881017`), full (`Doolin`)
- **Session Management**: 8-hour timeout with Flask-Login
- **CAPTCHA Protection**: Image-based verification in `app/services/captcha_service.py`

### Service Architecture
```
app/services/
├── data_service.py        # Core data ops with caching
├── smart_cache.py         # Intelligent multi-level caching  
├── scheduled_tasks.py     # Background tasks (2 AM cache preload)
├── chart_service.py       # Data visualization
├── contract_generator.py  # Document generation
├── receipt_generator.py   # Receipt creation
├── captcha_service.py     # Security verification
└── async_task_handler.py  # Async operations
```

### Frontend Architecture
- **Modular JavaScript**: Organized in `core/`, `modules/`, `controllers/`, `pages/`
- **API Client**: `modules/api-data-manager.js` handles all frontend-backend communication
- **Component System**: BEM CSS methodology with design token system
- **Enterprise Tables**: Responsive DataTables with export functionality in `modules/table-manager.js`

## Key Technical Details

### API Integration
- **Primary API**: External service at `*************:5000`
- **Authentication**: Uses hardcoded API key (`lxw8025031`)
- **Retry Logic**: Multiple fallback URLs and robust error handling
- **Key Endpoints**: `filter_data_db`, `filter_overdue_orders_db`, `summary_data_db`

### Caching Strategy
- **Cache Types**: SimpleCache (memory) + filesystem caching
- **Timeouts**: 5min (filters), 10min (orders), 30min (summaries)
- **Prewarming**: Scheduled daily cache preload via APScheduler
- **Cache Directory**: `cache/` with automatic cleanup

### Background Tasks
- **Scheduler**: APScheduler with 2 AM daily cache prewarming
- **Initialization**: `app/services/scheduled_tasks.py` - called from both `run.py` and `gunicorn_config.py`
- **Task Types**: Data preloading, cache cleanup, system maintenance

### Production Configuration
- **WSGI Server**: Gunicorn with worker process management
- **Logging**: Rotating file handlers in `logs/` directory
- **Error Pages**: Custom templates in `app/templates/errors/`
- **Health Checks**: Built-in service monitoring

## Business Logic Context

This is a financial data query and analysis system ("太享查询系统") that:
- Provides three-tier user access to financial order data
- Supports date-based filtering, customer queries, and overdue order analysis
- Generates Excel/CSV exports and PDF contracts/receipts
- Includes data visualization with Chart.js integration
- Features utility tools (QR code generation, calculator, document templates)

The system acts as a frontend proxy to an external financial data API, providing caching, user management, and enhanced UI/UX for data analysis workflows.