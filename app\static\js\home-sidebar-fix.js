/**
 * 首页侧边栏移动端修复器
 * 专门解决首页侧边栏在移动端无法弹出的问题
 */

(function() {
    'use strict';
    
    // 确保只在首页运行
    if (window.location.pathname !== '/') {
        return;
    }
    
    console.log('首页侧边栏修复器：开始初始化');
    
    let initialized = false;
    
    function initHomeSidebarFix() {
        if (initialized) {
            console.log('首页侧边栏修复器：已初始化，跳过');
            return;
        }
        
        // 查找关键元素
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        if (!sidebarToggle || !sidebar) {
            console.log('首页侧边栏修复器：未找到必要元素，延迟重试');
            setTimeout(initHomeSidebarFix, 500);
            return;
        }
        
        console.log('首页侧边栏修复器：找到必要元素，开始修复');
        
        // 移除可能存在的旧事件监听器
        const newToggle = sidebarToggle.cloneNode(true);
        sidebarToggle.parentNode.replaceChild(newToggle, sidebarToggle);
        
        // 添加新的事件监听器
        newToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('首页侧边栏修复器：切换按钮被点击');
            
            // 检查当前是否为移动端
            const isMobile = window.innerWidth <= 768;
            
            if (isMobile) {
                // 切换侧边栏显示状态
                const isActive = sidebar.classList.contains('sidebar-active');
                
                if (isActive) {
                    // 关闭侧边栏
                    sidebar.classList.remove('sidebar-active');
                    if (overlay) {
                        overlay.classList.remove('overlay-active');
                    }
                    console.log('首页侧边栏修复器：侧边栏已关闭');
                } else {
                    // 打开侧边栏
                    sidebar.classList.add('sidebar-active');
                    if (overlay) {
                        overlay.classList.add('overlay-active');
                    }
                    console.log('首页侧边栏修复器：侧边栏已打开');
                }
            }
        });
        
        // 处理遮罩层点击事件
        if (overlay) {
            // 移除旧的事件监听器
            const newOverlay = overlay.cloneNode(true);
            overlay.parentNode.replaceChild(newOverlay, overlay);
            
            newOverlay.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('首页侧边栏修复器：遮罩层被点击，关闭侧边栏');
                
                sidebar.classList.remove('sidebar-active');
                newOverlay.classList.remove('overlay-active');
            });
        }
        
        // 处理窗口大小变化
        let resizeTimer;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                const isMobile = window.innerWidth <= 768;
                
                if (!isMobile) {
                    // 桌面端时关闭移动端侧边栏
                    sidebar.classList.remove('sidebar-active');
                    if (overlay) {
                        overlay.classList.remove('overlay-active');
                    }
                }
            }, 250);
        });
        
        // 确保移动端侧边栏按钮可见
        function ensureMobileToggleVisible() {
            const isMobile = window.innerWidth <= 768;
            
            if (isMobile) {
                newToggle.style.display = 'flex';
                newToggle.style.position = 'fixed';
                newToggle.style.top = '10px';
                newToggle.style.left = '10px';
                newToggle.style.zIndex = '1040';
                newToggle.style.width = '40px';
                newToggle.style.height = '40px';
                newToggle.style.backgroundColor = 'white';
                newToggle.style.borderRadius = '50%';
                newToggle.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                newToggle.style.alignItems = 'center';
                newToggle.style.justifyContent = 'center';
                newToggle.style.cursor = 'pointer';
                
                // 确保图标正确显示
                const icon = newToggle.querySelector('i');
                if (icon) {
                    icon.style.fontSize = '24px';
                    icon.style.color = '#333';
                }
            } else {
                newToggle.style.display = 'none';
            }
        }
        
        // 初始化时设置按钮样式
        ensureMobileToggleVisible();
        
        // 窗口大小变化时重新设置按钮样式
        window.addEventListener('resize', ensureMobileToggleVisible);
        
        initialized = true;
        console.log('首页侧边栏修复器：初始化完成');
    }
    
    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initHomeSidebarFix);
    } else {
        initHomeSidebarFix();
    }
    
    // 页面完全加载后再次检查
    window.addEventListener('load', function() {
        setTimeout(initHomeSidebarFix, 100);
    });
    
})(); 