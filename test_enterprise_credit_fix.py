#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业信用查询修复效果测试脚本
测试修复后的长超时和错误处理是否正常工作
"""

import requests
import json
import time
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_enterprise_credit_endpoint():
    """测试企业信用查询端点"""
    log_message("=== 测试企业信用查询端点修复效果 ===")
    
    # 测试URL - 根据实际情况调整
    base_url = "http://localhost:5000"  # 或者使用实际的服务器地址
    endpoint = f"{base_url}/api/enterprise-credit/stream"
    
    payload = {
        "input": "测试企业查询",
        "streaming": True,
        "speed": 0.001
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        log_message(f"发送请求到: {endpoint}")
        log_message(f"请求负载: {json.dumps(payload, ensure_ascii=False)}")
        
        start_time = time.time()
        
        # 发送请求，设置较长的超时时间进行测试
        response = requests.post(
            endpoint,
            headers=headers,
            json=payload,
            stream=True,
            timeout=60  # 客户端测试超时设置为1分钟
        )
        
        elapsed_time = time.time() - start_time
        log_message(f"请求响应时间: {elapsed_time:.2f}秒")
        log_message(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            log_message("✅ 企业信用查询端点响应正常")
            
            # 尝试读取一些流式数据
            content_received = 0
            try:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        content_received += len(chunk)
                        log_message(f"收到数据块: {len(chunk)} 字节，总计: {content_received} 字节")
                        
                        # 读取足够的数据后停止（避免长时间等待）
                        if content_received > 1024:
                            log_message("✅ 流式数据接收正常，测试完成")
                            break
                        
                        # 如果30秒内没有数据，停止等待
                        if time.time() - start_time > 30:
                            log_message("⏱️ 30秒测试时间到，停止接收数据")
                            break
                            
            except Exception as stream_error:
                log_message(f"❌ 流式数据处理错误: {stream_error}")
                
        elif response.status_code == 504:
            log_message("⏱️ 收到504超时响应，这表明超时处理机制正常工作")
            try:
                error_data = response.json()
                log_message(f"错误信息: {error_data}")
            except:
                log_message("无法解析错误信息")
                
        elif response.status_code == 502:
            log_message("🔌 收到502连接错误响应，这表明错误处理机制正常工作")
            try:
                error_data = response.json()
                log_message(f"错误信息: {error_data}")
            except:
                log_message("无法解析错误信息")
                
        else:
            log_message(f"❌ 收到意外的响应状态码: {response.status_code}")
            try:
                log_message(f"响应内容: {response.text[:500]}")
            except:
                log_message("无法读取响应内容")
        
        return True
        
    except requests.exceptions.Timeout:
        log_message("⏱️ 客户端超时（60秒），这说明服务器端的长超时设置正在工作")
        log_message("✅ 修复效果：服务器没有因为长时间处理而提前断开连接")
        return True
        
    except requests.exceptions.ConnectionError as e:
        log_message(f"❌ 连接错误: {e}")
        log_message("可能的原因：服务未启动或端口不正确")
        return False
        
    except Exception as e:
        log_message(f"❌ 其他错误: {e}")
        return False

def test_basic_connectivity():
    """测试基本连接"""
    log_message("=== 测试基本连接 ===")
    
    try:
        response = requests.get("http://localhost:5000/", timeout=10)
        if response.status_code == 200:
            log_message("✅ 应用基本连接正常")
            return True
        else:
            log_message(f"⚠️ 应用响应异常，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        log_message("❌ 无法连接到应用，请检查服务是否启动")
        return False
    except Exception as e:
        log_message(f"❌ 连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    log_message("开始测试企业信用查询修复效果...")
    log_message("=" * 60)
    
    # 基本连接测试
    if not test_basic_connectivity():
        log_message("基本连接失败，跳过企业信用查询测试")
        return 1
    
    # 企业信用查询测试
    success = test_enterprise_credit_endpoint()
    
    log_message("=" * 60)
    if success:
        log_message("🎉 测试完成！修复效果验证成功")
        log_message("主要改进:")
        log_message("- 超时时间增加到10分钟")
        log_message("- 添加了重试机制") 
        log_message("- 改善了错误处理")
        log_message("- gunicorn超时设置已调整")
    else:
        log_message("⚠️ 测试发现问题，请检查日志")
    
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())