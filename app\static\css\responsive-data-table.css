/**
 * 响应式数据表格样式
 * 支持桌面端表格和移动端卡片视图
 */

/* ==================== 数据容器 ==================== */
.data-container {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

/* ==================== 结果统计 ==================== */
.result-summary {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-bottom: 1px solid var(--medium-gray);
}

.summary-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-color);
}

.summary-info i {
    color: var(--primary-color);
}

/* ==================== 桌面端表格视图 ==================== */
.desktop-table-view {
    display: block;
}

.table-container {
    overflow-x: auto;
    overflow-y: hidden;
}

.modern-data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.table-header {
    background: var(--light-gray);
    padding: var(--spacing-lg) var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 2px solid var(--medium-gray);
    cursor: pointer;
    transition: background var(--transition-fast);
    position: relative;
    white-space: nowrap;
}

.table-header:hover {
    background: var(--medium-gray);
}

.header-text {
    margin-right: var(--spacing-sm);
}

.sort-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.sort-icon.active {
    color: var(--primary-color);
}

.table-row {
    transition: background var(--transition-fast);
}

.table-row:hover {
    background: rgba(0, 123, 255, 0.05);
}

.table-row:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

.table-row:nth-child(even):hover {
    background: rgba(0, 123, 255, 0.05);
}

.table-cell {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--medium-gray);
    vertical-align: middle;
}

/* ==================== 表格内容样式 ==================== */
.amount-value {
    font-weight: 600;
    color: var(--success-color);
}

.status-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.status-badge.danger {
    background: var(--danger-color);
    color: var(--white);
}

.status-badge.primary {
    background: var(--primary-color);
    color: var(--white);
}

.status-badge.info {
    background: var(--info-color);
    color: var(--white);
}

.date-value {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.text-value {
    color: var(--text-color);
}

.empty-value {
    color: var(--text-muted);
    font-style: italic;
}

/* ==================== 增强型响应式列管理 ==================== */
.table-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-lg);
}

.table-wrapper {
    position: relative;
    overflow-x: auto;
    overflow-y: visible;
    scrollbar-width: thin;
    scrollbar-color: var(--medium-gray) transparent;
}

.table-wrapper::-webkit-scrollbar {
    height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: var(--light-gray);
    border-radius: var(--border-radius-sm);
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: var(--border-radius-sm);
    transition: background var(--transition-fast);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
}

/* 现代化悬浮列控制按钮 */
.column-controls {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-sm);
    border: 1px solid rgba(0, 123, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-normal);
}

.column-controls:hover {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-50%) translateX(-2px);
}

.column-controls.enhanced {
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 123, 255, 0.3);
}

/* 吸附指示器 */
.snap-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #28a745, #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10;
    animation: snap-indicator-pulse 2s ease-in-out infinite;
}

.snap-indicator i {
    font-size: 8px;
}

@keyframes snap-indicator-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* 悬浮按钮内的列信息 */
.column-info {
    font-size: 0.7rem;
    color: var(--text-muted);
    font-weight: 600;
    text-align: center;
    line-height: 1.2;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid rgba(233, 236, 239, 0.6);
    margin-bottom: var(--spacing-xs);
    min-width: 50px;
}

.column-info .visible-count {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 0.8rem;
}

/* 悬浮切换按钮 */
.column-toggle-btn {
    background: transparent;
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.7rem;
    color: var(--text-color);
    transition: all var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 50px;
    text-align: center;
}

.column-toggle-btn:hover {
    background: rgba(0, 123, 255, 0.08);
    color: var(--primary-color);
    transform: scale(1.05);
}

.column-toggle-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius-md);
}

.column-toggle-btn.active:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.column-toggle-btn i {
    font-size: 1rem;
    margin-bottom: 2px;
}

.column-toggle-btn .btn-text {
    font-size: 0.65rem;
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
}

.column-toggle-btn .hidden-count {
    font-size: 0.6rem;
    opacity: 0.8;
    line-height: 1;
}

/* 悬浮按钮进入动画 */
@keyframes floatIn {
    from {
        opacity: 0;
        transform: translateY(-50%) translateX(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) translateX(0) scale(1);
    }
}

.column-controls {
    animation: floatIn 0.4s ease-out;
}

/* 按钮脉冲效果（当有隐藏列时） */
@keyframes pulse {
    0% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    }
    50% {
        box-shadow: 0 8px 32px rgba(0, 123, 255, 0.15), 0 2px 8px rgba(0, 123, 255, 0.1);
    }
    100% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    }
}

.column-controls:not(.active) {
    animation: floatIn 0.4s ease-out, pulse 2s ease-in-out infinite;
    animation-delay: 0s, 1s;
}

/* 激活状态的特殊样式 */
.column-controls .column-toggle-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    box-shadow: inset 0 1px 3px rgba(255, 255, 255, 0.2);
}

/* 微妙的边缘发光效果 */
.column-controls::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(0, 123, 255, 0.1), transparent);
    border-radius: var(--border-radius-xl);
    z-index: -1;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.column-controls:hover::before {
    opacity: 1;
}

/* 拖拽相关样式 */
.column-controls {
    cursor: grab;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* 确保拖拽时位置更新流畅 */
    will-change: transform, left, top;
    /* 硬件加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.column-controls:active {
    cursor: grabbing;
}

/* 拖拽时禁用页面滚动和选择 */
body.dragging-column-controls {
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 拖拽状态样式 */
.column-controls.dragging {
    cursor: grabbing !important;
    z-index: 1001 !important;
    transform: scale(1.08) !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25), 0 8px 20px rgba(0, 0, 0, 0.18) !important;
    animation: none; /* 停止脉冲动画 */
    transition: none !important; /* 拖拽时禁用过渡动画 */
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
}

.column-controls.dragging::before {
    opacity: 1;
    background: linear-gradient(45deg,
        rgba(0, 123, 255, 0.3),
        rgba(0, 123, 255, 0.15),
        rgba(0, 123, 255, 0.3)
    );
}

/* 拖拽时的按钮内容样式 */
.column-controls.dragging .column-toggle-btn {
    pointer-events: none; /* 拖拽时禁用按钮点击 */
    opacity: 0.9;
}

/* 拖拽时的光晕效果 */
.column-controls.dragging::after {
    opacity: 0.8;
    border-color: rgba(0, 123, 255, 0.5);
    animation: pulse-glow 1s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
    from {
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
    }
    to {
        box-shadow: 0 0 15px rgba(0, 123, 255, 0.6);
    }
}

/* 边缘吸附动画样式 */
.column-controls.snapping {
    transition: none !important; /* 禁用CSS过渡，使用JS动画 */
    z-index: 1001 !important;
    transform: scale(1.02) !important;
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.2), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.column-controls.snapping::before {
    opacity: 1;
    background: linear-gradient(45deg,
        rgba(0, 123, 255, 0.25),
        rgba(0, 123, 255, 0.1),
        rgba(0, 123, 255, 0.25)
    );
}

/* 吸附指示器动画 */
.column-controls.snapping::after {
    opacity: 1;
    border-color: rgba(0, 123, 255, 0.4);
    animation: snap-pulse 0.3s ease-out;
}

@keyframes snap-pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.6;
    }
}

/* 拖拽提示样式 */
.column-controls::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px dashed rgba(0, 123, 255, 0.3);
    border-radius: var(--border-radius-xl);
    opacity: 0;
    transition: opacity var(--transition-fast);
    pointer-events: none;
}

.column-controls:hover::after {
    opacity: 0.5;
}

.column-controls.dragging::after {
    opacity: 1;
    border-style: solid;
    border-color: rgba(0, 123, 255, 0.6);
}

/* 防止拖拽时选中文本 */
.column-controls * {
    pointer-events: none;
}

.column-controls .column-toggle-btn {
    pointer-events: auto;
}

/* 表格响应式样式 */
.modern-data-table.responsive-table {
    transition: all var(--transition-normal);
}

.modern-data-table.column-managed th,
.modern-data-table.column-managed td {
    transition: all var(--transition-fast);
    white-space: nowrap;
    min-width: 120px;
}

/* 隐藏的列 */
.modern-data-table th.column-hidden,
.modern-data-table td.column-hidden {
    display: none;
}

/* 列展开状态 */
.modern-data-table.columns-expanded {
    min-width: 100%;
}

.modern-data-table.columns-expanded th,
.modern-data-table.columns-expanded td {
    min-width: 150px;
}

/* 列切换动画 */
.modern-data-table.column-transitioning {
    transition: all var(--transition-normal);
}

.modern-data-table.column-transitioning th,
.modern-data-table.column-transitioning td {
    transition: all var(--transition-normal);
}

/* 列渐入渐出动画 */
.column-fade-in {
    animation: columnFadeIn var(--transition-normal) ease;
}

.column-fade-out {
    animation: columnFadeOut var(--transition-normal) ease;
}

@keyframes columnFadeIn {
    from {
        opacity: 0;
        transform: scaleX(0);
    }
    to {
        opacity: 1;
        transform: scaleX(1);
    }
}

@keyframes columnFadeOut {
    from {
        opacity: 1;
        transform: scaleX(1);
    }
    to {
        opacity: 0;
        transform: scaleX(0);
    }
}

/* 增强型滚动指示器 */
.table-wrapper::before,
.table-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 30px;
    pointer-events: none;
    z-index: 10;
    transition: opacity var(--transition-fast);
    opacity: 0;
}

/* 左侧滚动阴影 */
.table-wrapper::before {
    left: 0;
    background: linear-gradient(to right, 
        rgba(255, 255, 255, 0.9), 
        rgba(255, 255, 255, 0.7),
        transparent
    );
}

/* 右侧滚动阴影 */
.table-wrapper::after {
    right: 0;
    background: linear-gradient(to left, 
        rgba(255, 255, 255, 0.9), 
        rgba(255, 255, 255, 0.7),
        transparent
    );
}

/* 滚动状态指示 */
.table-wrapper.has-scroll:not(.scroll-at-start)::before {
    opacity: 1;
}

.table-wrapper.has-scroll:not(.scroll-at-end)::after {
    opacity: 1;
}

/* 滚动进度指示器 */
.table-wrapper.has-scroll {
    position: relative;
}

.table-wrapper.has-scroll::after {
    background: linear-gradient(to left, 
        rgba(0, 123, 255, 0.1), 
        rgba(0, 123, 255, 0.05),
        transparent
    );
}

/* 列折叠提示 */
.columns-indicator {
    position: absolute;
    top: 50%;
    right: 50px;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.7rem;
    color: var(--text-muted);
    pointer-events: none;
    border: 1px solid rgba(233, 236, 239, 0.6);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.table-container:hover .columns-indicator {
    opacity: 1;
}

/* ==================== 响应式断点调整 ==================== */

/* 移动端样式 (≤768px) */
@media (max-width: 768px) {
    /* 移动端完全隐藏悬浮列控制按钮 */
    .column-controls {
        display: none !important;
    }
    
    .desktop-table-view {
        display: none;
    }
    
    .mobile-card-view {
        display: block;
    }
    
    /* 确保移动端表格正常显示（如果意外显示） */
    .modern-data-table th,
    .modern-data-table td {
        display: table-cell !important;
    }
    
    .modern-data-table th.column-hidden,
    .modern-data-table td.column-hidden {
        display: table-cell !important;
    }
}

/* 平板端样式 (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .column-controls {
        scale: 0.9;
    }
    
    .modern-data-table.column-managed th,
    .modern-data-table.column-managed td {
        min-width: 100px;
        font-size: 0.85rem;
    }
}

/* 小桌面端样式 (1025px - 1200px) */
@media (min-width: 1025px) and (max-width: 1200px) {
    .column-controls {
        scale: 0.95;
    }
}

/* 大屏幕样式 (>1200px) */
@media (min-width: 1201px) {
    .modern-data-table.columns-expanded th,
    .modern-data-table.columns-expanded td {
        min-width: 180px;
    }
    
    .column-controls {
        right: 30px;
    }
}

/* ==================== 移动端卡片视图 ==================== */
.mobile-card-view {
    display: none;
    padding: var(--spacing-lg);
}

.cards-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.data-card {
    background: var(--white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.data-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--light-gray);
    border-bottom: 1px solid var(--medium-gray);
}

.card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.order-number {
    font-weight: 600;
    color: var(--text-color);
    font-size: 1rem;
}

.overdue-badge {
    background: var(--danger-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.card-actions {
    display: flex;
    align-items: center;
}

.card-expand-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.card-expand-btn:hover {
    background: var(--medium-gray);
    color: var(--text-color);
}

.card-expand-btn i {
    transition: transform var(--transition-normal);
}

.card-summary {
    padding: var(--spacing-lg);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.item-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.item-value {
    font-weight: 500;
    color: var(--text-color);
}

.item-value.amount {
    color: var(--success-color);
    font-weight: 600;
}

.card-details {
    display: none;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--medium-gray);
    background: rgba(248, 249, 250, 0.5);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(233, 236, 239, 0.5);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.detail-value {
    font-size: 0.875rem;
    color: var(--text-color);
    text-align: right;
    max-width: 60%;
    word-break: break-word;
}

/* ==================== 分页样式 ==================== */
.pagination-wrapper {
    padding: var(--spacing-xl);
    background: var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.pagination-info {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.pagination-nav {
    display: flex;
    align-items: center;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--white);
    color: var(--text-color);
    text-decoration: none;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.pagination-btn:hover:not(.disabled) {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.page-numbers {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.page-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--white);
    color: var(--text-color);
    text-decoration: none;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.page-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.page-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.page-ellipsis {
    padding: var(--spacing-md);
    color: var(--text-muted);
}

/* ==================== 错误和空状态 ==================== */
.error-state,
.empty-state {
    padding: var(--spacing-xxl);
    text-align: center;
    color: var(--text-muted);
}

.error-icon,
.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.error-icon {
    color: var(--danger-color);
}

.empty-icon {
    color: var(--text-muted);
}

.error-title,
.empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

.error-message,
.empty-message {
    font-size: 1rem;
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.retry-btn,
.load-data-btn,
.clear-search-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.retry-btn:hover,
.load-data-btn:hover,
.clear-search-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}
