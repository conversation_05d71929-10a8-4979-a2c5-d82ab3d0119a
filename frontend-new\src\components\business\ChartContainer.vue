<template>
  <div class="chart-container">
    <!-- 图表头部 -->
    <div class="chart-header mb-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div class="chart-title">
        <h3 class="text-lg font-semibold text-gray-800">{{ title }}</h3>
        <p v-if="subtitle" class="text-sm text-gray-600 mt-1">{{ subtitle }}</p>
      </div>
      
      <div class="chart-actions flex flex-wrap gap-2">
        <!-- 图表类型切换 -->
        <el-select
          v-if="allowTypeChange && chartTypes.length > 1"
          v-model="currentChartType"
          size="small"
          style="width: 120px"
          @change="handleTypeChange"
        >
          <el-option
            v-for="type in chartTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
        
        <!-- 刷新按钮 -->
        <el-button
          size="small"
          :icon="Refresh"
          @click="handleRefresh"
          :loading="loading"
        >
          刷新
        </el-button>
        
        <!-- 导出按钮 -->
        <el-dropdown v-if="exportable" @command="handleExport">
          <el-button size="small" :icon="Download">
            导出<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="png">PNG图片</el-dropdown-item>
              <el-dropdown-item command="jpg">JPG图片</el-dropdown-item>
              <el-dropdown-item command="pdf">PDF文档</el-dropdown-item>
              <el-dropdown-item command="svg">SVG矢量图</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        
        <!-- 全屏按钮 -->
        <el-button
          v-if="allowFullscreen"
          size="small"
          :icon="FullScreen"
          @click="toggleFullscreen"
        >
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
      </div>
    </div>
    
    <!-- 图表内容 -->
    <div 
      ref="chartContainer"
      class="chart-content"
      :class="{ 'fullscreen': isFullscreen }"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="chart-loading">
        <el-skeleton animated>
          <template #template>
            <div class="h-64 bg-gray-100 rounded"></div>
          </template>
        </el-skeleton>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="chart-error">
        <el-result
          icon="error"
          title="图表加载失败"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="handleRefresh">
              重新加载
            </el-button>
          </template>
        </el-result>
      </div>
      
      <!-- 空数据状态 -->
      <div v-else-if="!hasData" class="chart-empty">
        <el-empty description="暂无图表数据" />
      </div>
      
      <!-- ECharts图表 -->
      <v-chart
        v-else
        ref="chartRef"
        :option="chartOption"
        :loading="loading"
        :theme="theme"
        autoresize
        class="chart"
        :style="{ height: chartHeight }"
        @click="handleChartClick"
      />
    </div>
    
    <!-- 图表说明 -->
    <div v-if="description" class="chart-description mt-4">
      <el-alert
        :title="description"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import {
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { Refresh, Download, FullScreen, ArrowDown } from '@element-plus/icons-vue'
import type { ChartData } from '@/types/data'
import { useResponsive } from '@/composables/useResponsive'

// 注册ECharts组件
use([
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent
])

interface ChartType {
  label: string
  value: string
}

interface Props {
  title: string
  subtitle?: string
  data: ChartData | null
  chartType?: string
  chartTypes?: ChartType[]
  allowTypeChange?: boolean
  loading?: boolean
  error?: string
  height?: string
  theme?: string
  exportable?: boolean
  allowFullscreen?: boolean
  description?: string
  options?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  chartType: 'line',
  chartTypes: () => [
    { label: '折线图', value: 'line' },
    { label: '柱状图', value: 'bar' },
    { label: '饼图', value: 'pie' }
  ],
  allowTypeChange: true,
  loading: false,
  height: '400px',
  theme: 'light',
  exportable: true,
  allowFullscreen: true
})

const emit = defineEmits<{
  refresh: []
  export: [format: string]
  typeChange: [type: string]
  chartClick: [params: any]
}>()

const { isMobile } = useResponsive()

// 响应式数据
const chartRef = ref()
const chartContainer = ref()
const currentChartType = ref(props.chartType)
const isFullscreen = ref(false)

// 计算属性
const hasData = computed(() => {
  return props.data && props.data.datasets && props.data.datasets.length > 0
})

const chartHeight = computed(() => {
  if (isFullscreen.value) return '80vh'
  if (isMobile.value) return '300px'
  return props.height
})

const chartOption = computed(() => {
  if (!props.data) return {}
  
  const baseOption = {
    responsive: true,
    maintainAspectRatio: false,
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      show: true,
      bottom: 0,
      textStyle: {
        fontSize: isMobile.value ? 12 : 14
      }
    },
    grid: {
      left: isMobile.value ? '10%' : '3%',
      right: isMobile.value ? '10%' : '4%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    }
  }
  
  // 根据图表类型生成配置
  switch (currentChartType.value) {
    case 'bar':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: props.data.labels,
          axisLabel: {
            fontSize: isMobile.value ? 10 : 12,
            rotate: isMobile.value ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: isMobile.value ? 10 : 12
          }
        },
        series: props.data.datasets.map(dataset => ({
          name: dataset.label,
          type: 'bar',
          data: dataset.data,
          itemStyle: {
            color: dataset.backgroundColor
          }
        }))
      }
      
    case 'pie':
      return {
        ...baseOption,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: props.data.datasets[0]?.label || '数据',
          type: 'pie',
          radius: isMobile.value ? '60%' : '70%',
          center: ['50%', '45%'],
          data: props.data.labels.map((label, index) => ({
            name: label,
            value: props.data!.datasets[0]?.data[index] || 0
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      
    default: // line
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: props.data.labels,
          axisLabel: {
            fontSize: isMobile.value ? 10 : 12,
            rotate: isMobile.value ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: isMobile.value ? 10 : 12
          }
        },
        series: props.data.datasets.map(dataset => ({
          name: dataset.label,
          type: 'line',
          data: dataset.data,
          smooth: true,
          lineStyle: {
            color: dataset.borderColor,
            width: 2
          },
          itemStyle: {
            color: dataset.borderColor
          },
          areaStyle: dataset.backgroundColor ? {
            color: dataset.backgroundColor
          } : undefined
        }))
      }
  }
})

// 方法
const handleRefresh = () => {
  emit('refresh')
}

const handleExport = (format: string) => {
  if (chartRef.value) {
    const chart = chartRef.value
    let dataURL = ''
    
    switch (format) {
      case 'png':
        dataURL = chart.getDataURL({ type: 'png', pixelRatio: 2 })
        downloadImage(dataURL, `${props.title}.png`)
        break
      case 'jpg':
        dataURL = chart.getDataURL({ type: 'jpeg', pixelRatio: 2 })
        downloadImage(dataURL, `${props.title}.jpg`)
        break
      case 'svg':
        dataURL = chart.getDataURL({ type: 'svg' })
        downloadImage(dataURL, `${props.title}.svg`)
        break
      default:
        emit('export', format)
    }
  }
}

const downloadImage = (dataURL: string, filename: string) => {
  const link = document.createElement('a')
  link.download = filename
  link.href = dataURL
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const handleTypeChange = (type: string) => {
  currentChartType.value = type
  emit('typeChange', type)
}

const handleChartClick = (params: any) => {
  emit('chartClick', params)
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  if (isFullscreen.value) {
    chartContainer.value?.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
  
  // 延迟调整图表大小
  nextTick(() => {
    setTimeout(() => {
      chartRef.value?.resize()
    }, 100)
  })
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 生命周期
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})

// 监听数据变化，重新渲染图表
watch(() => props.data, () => {
  nextTick(() => {
    chartRef.value?.resize()
  })
}, { deep: true })

// 暴露方法
defineExpose({
  getChart: () => chartRef.value,
  resize: () => chartRef.value?.resize()
})
</script>

<style scoped>
.chart-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
}

.chart-content {
  position: relative;
}

.chart-content.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  padding: 20px;
}

.chart {
  width: 100%;
}

.chart-loading,
.chart-error,
.chart-empty {
  @apply flex items-center justify-center;
  min-height: 300px;
}

@media (max-width: 768px) {
  .chart-container {
    margin: 0 -1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .chart-actions {
    justify-content: center;
  }
}
</style>
