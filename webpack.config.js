const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = {
    mode: 'production',
    entry: {
        // 核心应用
        'app': './app/static/js/main-app.js',
        
        // 企业级功能
        'enterprise': [
            './app/static/js/enterprise/customer-summary-enterprise.js',
            './app/static/js/enterprise/desktop-data-grid.js',
            './app/static/js/enterprise/mobile-adaptive.js'
        ],
        
        // 数据表格
        'datatable': [
            './app/static/js/data-table-enhanced.js',
            './app/static/js/enterprise-table-manager.js'
        ],
        
        // 按需加载的大型库 - 使用实际路径
        'excel': './app/static/vendor/xlsx/xlsx.full.min.js',
        'charts': './app/static/vendor/chart.js/chart.min.js',
        
        // 页面特定脚本
        'home': './app/static/js/pages/home.js',
        
        // 工具组件
        'components': [
            './app/static/js/components/calculator.js',
            './app/static/js/components/qrcode.js',
            './app/static/js/components/todo.js',
            './app/static/js/export-manager.js'
        ]
    },
    
    output: {
        path: path.resolve(__dirname, 'app/static/dist'),
        filename: 'js/[name].min.js',
        publicPath: '/static/dist/',
        clean: true
    },
    
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env']
                    }
                }
            },
            {
                test: /\.css$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    'css-loader'
                ]
            }
        ]
    },
    
    plugins: [
        new MiniCssExtractPlugin({
            filename: 'css/[name].min.css'
        })
    ],
    
    optimization: {
        minimizer: [
            new TerserPlugin({
                terserOptions: {
                    compress: {
                        drop_console: true,
                        drop_debugger: true,
                        pure_funcs: ['console.log']
                    },
                    mangle: true,
                    format: {
                        comments: false
                    }
                },
                extractComments: false
            }),
            new CssMinimizerPlugin()
        ],
        
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                    priority: 10
                },
                common: {
                    name: 'common',
                    minChunks: 2,
                    chunks: 'all',
                    priority: 5
                }
            }
        }
    },
    
    resolve: {
        extensions: ['.js', '.json']
    },
    
    devtool: false, // 生产环境不生成 source map
    
    performance: {
        maxEntrypointSize: 300000,
        maxAssetSize: 300000,
        hints: 'warning'
    }
};