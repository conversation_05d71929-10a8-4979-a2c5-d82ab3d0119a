/**
 * API客户端模块
 * 负责处理与后端API的通信，包括状态检测、错误处理和重试机制
 */

class ApiClient {
    constructor() {
        this.baseUrl = '';
        this.timeout = 10000; // 10秒超时
        this.retryCount = 3;
        this.retryDelay = 1000; // 1秒重试延迟
        this.statusCheckInterval = null;
        
        // 绑定方法上下文
        this.checkApiStatus = this.checkApiStatus.bind(this);
        this.handleApiError = this.handleApiError.bind(this);
    }

    /**
     * 初始化API客户端
     * @param {Object} config - 配置选项
     */
    initialize(config = {}) {
        this.baseUrl = config.baseUrl || '';
        this.timeout = config.timeout || 10000;
        this.retryCount = config.retryCount || 3;
        this.retryDelay = config.retryDelay || 1000;
        
        console.log('ApiClient: API客户端已初始化');
        
        // 开始API状态检测
        this.startStatusCheck();
    }

    /**
     * 发起HTTP请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求Promise
     */
    async request(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        
        const requestOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            signal: controller.signal,
            ...options
        };

        try {
            const response = await fetch(url, requestOptions);
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new ApiError(response.status, response.statusText, url);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new ApiError(408, 'Request Timeout', url);
            }
            
            throw error;
        }
    }

    /**
     * 带重试的请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @param {number} attempts - 当前尝试次数
     * @returns {Promise} 请求Promise
     */
    async requestWithRetry(url, options = {}, attempts = 0) {
        try {
            return await this.request(url, options);
        } catch (error) {
            if (attempts < this.retryCount && this.shouldRetry(error)) {
                console.warn(`ApiClient: 请求失败，正在重试 (${attempts + 1}/${this.retryCount}):`, error.message);
                
                // 指数退避延迟
                const delay = this.retryDelay * Math.pow(2, attempts);
                await this.sleep(delay);
                
                return this.requestWithRetry(url, options, attempts + 1);
            }
            
            throw error;
        }
    }

    /**
     * 判断是否应该重试
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否应该重试
     */
    shouldRetry(error) {
        if (error instanceof ApiError) {
            // 对于5xx错误和网络错误进行重试
            return error.status >= 500 || error.status === 408 || error.status === 0;
        }
        
        // 对于网络错误进行重试
        return error.name === 'TypeError' || error.name === 'NetworkError';
    }

    /**
     * 开始API状态检测
     */
    startStatusCheck() {
        // 立即检测一次
        this.checkApiStatus();
        
        // 设置定期检测
        this.statusCheckInterval = setInterval(this.checkApiStatus, 30000); // 30秒检测一次
    }

    /**
     * 停止API状态检测
     */
    stopStatusCheck() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
            this.statusCheckInterval = null;
        }
    }

    /**
     * 检测API状态
     */
    async checkApiStatus() {
        const apiStatusElement = document.getElementById('apiStatus');
        if (!apiStatusElement) return;
        
        try {
            // 创建超时控制器
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);
            
            const response = await fetch('/api/ping', {
                signal: controller.signal,
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new ApiError(response.status, response.statusText, '/api/ping');
            }
            
            const data = await response.json();
            
            if (data.status === 'ok') {
                this.updateApiStatus('success', '正常');
            } else {
                this.updateApiStatus('warning', '异常');
            }
        } catch (error) {
            console.error('ApiClient: API状态检查失败:', error);
            
            if (error.name === 'AbortError') {
                this.updateApiStatus('danger', '超时');
            } else if (error instanceof ApiError) {
                this.updateApiStatus('danger', `错误 ${error.status}`);
            } else {
                this.updateApiStatus('danger', '连接失败');
            }
            
            // 如果状态检查失败，5秒后重试
            setTimeout(() => this.checkApiStatus(), 5000);
        }
    }

    /**
     * 更新API状态显示
     * @param {string} type - 状态类型 (success, warning, danger)
     * @param {string} text - 状态文本
     */
    updateApiStatus(type, text) {
        const apiStatusElement = document.getElementById('apiStatus');
        if (apiStatusElement) {
            apiStatusElement.innerHTML = `<span class="badge bg-${type}">${text}</span>`;
        }
    }

    /**
     * 处理API错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleApiError(error, context = '') {
        let message = '网络请求失败';
        
        if (error instanceof ApiError) {
            switch (error.status) {
                case 400:
                    message = '请求参数错误';
                    break;
                case 401:
                    message = '未授权访问';
                    break;
                case 403:
                    message = '访问被禁止';
                    break;
                case 404:
                    message = '请求的资源不存在';
                    break;
                case 408:
                    message = '请求超时';
                    break;
                case 500:
                    message = '服务器内部错误';
                    break;
                case 502:
                    message = '网关错误';
                    break;
                case 503:
                    message = '服务不可用';
                    break;
                default:
                    message = `请求失败 (${error.status})`;
            }
        } else if (error.name === 'TypeError') {
            message = '网络连接失败';
        }
        
        console.error(`ApiClient: ${context} - ${message}`, error);
        
        // 显示错误消息给用户
        this.showErrorMessage(`${context ? context + ': ' : ''}${message}`);
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showErrorMessage(message) {
        // 创建错误提示元素
        const alertElement = document.createElement('div');
        alertElement.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alertElement.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertElement.innerHTML = `
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertElement);
        
        // 自动移除提示
        setTimeout(() => {
            if (alertElement.parentNode) {
                alertElement.remove();
            }
        }, 5000);
    }

    /**
     * 获取客户数据
     * @param {string} customerName - 客户名称
     * @returns {Promise} 客户数据
     */
    async getCustomerData(customerName) {
        try {
            const url = `/?customerName=${encodeURIComponent(customerName)}&tab=customer`;
            return await this.requestWithRetry(url);
        } catch (error) {
            this.handleApiError(error, '获取客户数据');
            throw error;
        }
    }

    /**
     * 获取汇总数据
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     * @returns {Promise} 汇总数据
     */
    async getSummaryData(startDate, endDate) {
        try {
            const params = new URLSearchParams();
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            
            const url = `/summary${params.toString() ? '?' + params.toString() : ''}`;
            return await this.requestWithRetry(url);
        } catch (error) {
            this.handleApiError(error, '获取汇总数据');
            throw error;
        }
    }

    /**
     * Sleep函数
     * @param {number} ms - 睡眠毫秒数
     * @returns {Promise} 睡眠Promise
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 销毁API客户端
     */
    destroy() {
        this.stopStatusCheck();
        console.log('ApiClient: API客户端已销毁');
    }
}

/**
 * API错误类
 */
class ApiError extends Error {
    constructor(status, statusText, url) {
        super(`API Error ${status}: ${statusText}`);
        this.name = 'ApiError';
        this.status = status;
        this.statusText = statusText;
        this.url = url;
    }
}

// 导出模块
window.ApiClient = ApiClient;
window.ApiError = ApiError;