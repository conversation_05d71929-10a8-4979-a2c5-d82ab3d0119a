<template>
  <div class="app-header">
    <div class="header-left">
      <!-- 折叠按钮 -->
      <el-button
        type="text"
        :icon="sidebarCollapsed ? Expand : Fold"
        class="collapse-btn"
        @click="toggleSidebar"
      />
      
      <!-- 面包屑导航 -->
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item
            v-for="item in breadcrumbItems"
            :key="item.path"
            :to="item.path"
          >
            <el-icon v-if="item.icon" class="mr-1">
              <component :is="item.icon" />
            </el-icon>
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 搜索框 -->
      <div class="global-search">
        <el-input
          v-model="searchKeyword"
          placeholder="全局搜索..."
          :prefix-icon="Search"
          clearable
          class="search-input"
          @keyup.enter="handleGlobalSearch"
          @focus="showSearchSuggestions = true"
          @blur="hideSearchSuggestions"
        />
        
        <!-- 搜索建议 -->
        <div v-if="showSearchSuggestions && searchSuggestions.length > 0" class="search-suggestions">
          <div
            v-for="suggestion in searchSuggestions"
            :key="suggestion.id"
            class="suggestion-item"
            @click="selectSuggestion(suggestion)"
          >
            <el-icon class="suggestion-icon">
              <component :is="suggestion.icon" />
            </el-icon>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-desc">{{ suggestion.description }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 通知中心 -->
      <el-dropdown @command="handleNotificationAction">
        <div class="notification-btn">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-icon :size="18"><Bell /></el-icon>
          </el-badge>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="notification-menu">
            <div class="notification-header">
              <span>通知中心</span>
              <el-button type="primary" text size="small" @click="markAllAsRead">
                全部已读
              </el-button>
            </div>
            
            <div class="notification-list">
              <div
                v-for="notification in notifications"
                :key="notification.id"
                class="notification-item"
                :class="{ 'unread': !notification.read }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-icon" :class="notification.type">
                  <el-icon>
                    <component :is="getNotificationIcon(notification.type)" />
                  </el-icon>
                </div>
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-desc">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatRelativeTime(notification.time) }}</div>
                </div>
              </div>
              
              <div v-if="notifications.length === 0" class="empty-notifications">
                <el-empty description="暂无通知" :image-size="60" />
              </div>
            </div>
            
            <div class="notification-footer">
              <el-button type="primary" text @click="viewAllNotifications">
                查看全部
              </el-button>
            </div>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 帮助中心 -->
      <el-dropdown @command="handleHelpAction">
        <div class="help-btn">
          <el-icon :size="18"><QuestionFilled /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="docs">使用文档</el-dropdown-item>
            <el-dropdown-item command="shortcuts">快捷键</el-dropdown-item>
            <el-dropdown-item command="feedback">意见反馈</el-dropdown-item>
            <el-dropdown-item divided command="about">关于系统</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 设置 -->
      <el-dropdown @command="handleSettingAction">
        <div class="setting-btn">
          <el-icon :size="18"><Setting /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="theme">主题设置</el-dropdown-item>
            <el-dropdown-item command="language">语言设置</el-dropdown-item>
            <el-dropdown-item command="layout">布局设置</el-dropdown-item>
            <el-dropdown-item divided command="preferences">个人偏好</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 用户菜单 -->
      <el-dropdown @command="handleUserAction">
        <div class="user-menu">
          <el-avatar :size="32" :src="userAvatar">
            {{ userInitial }}
          </el-avatar>
          <div class="user-info">
            <div class="user-name">{{ authStore.displayName }}</div>
            <div class="user-role">{{ getRoleText(authStore.userRole) }}</div>
          </div>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人资料
            </el-dropdown-item>
            <el-dropdown-item command="security">
              <el-icon><Lock /></el-icon>
              安全设置
            </el-dropdown-item>
            <el-dropdown-item command="preferences">
              <el-icon><Setting /></el-icon>
              偏好设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Expand,
  Fold,
  Search,
  Bell,
  QuestionFilled,
  Setting,
  User,
  Lock,
  SwitchButton,
  ArrowDown,
  House,
  DataAnalysis,
  Tools,
  WarningFilled,
  InfoFilled,
  SuccessFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { formatRelativeTime } from '@/utils/format'
import type { UserRole } from '@/types/auth'

interface Props {
  sidebarCollapsed: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'toggle-sidebar': []
}>()

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const searchKeyword = ref('')
const showSearchSuggestions = ref(false)
const notifications = ref([
  {
    id: '1',
    type: 'info',
    title: '系统更新',
    message: '系统已更新到v2.0.0版本',
    time: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
    read: false
  },
  {
    id: '2',
    type: 'warning',
    title: '数据同步',
    message: '数据同步可能延迟，请稍后重试',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
    read: false
  },
  {
    id: '3',
    type: 'success',
    title: '导出完成',
    message: '订单数据导出已完成',
    time: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
    read: true
  }
])

// 计算属性
const breadcrumbItems = computed(() => {
  const items = []
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  
  // 添加首页
  if (route.path !== '/dashboard') {
    items.push({
      title: '工作台',
      path: '/dashboard',
      icon: 'House'
    })
  }
  
  // 添加匹配的路由
  matched.forEach(match => {
    if (match.meta.title && match.path !== '/') {
      items.push({
        title: match.meta.title as string,
        path: match.path,
        icon: match.meta.icon as string
      })
    }
  })
  
  return items
})

const searchSuggestions = computed(() => {
  if (!searchKeyword.value) return []
  
  const suggestions = [
    {
      id: '1',
      title: '数据查询',
      description: '查询订单和客户数据',
      icon: 'Search',
      path: '/data/query'
    },
    {
      id: '2',
      title: '客户汇总',
      description: '查看客户详细信息',
      icon: 'User',
      path: '/customer'
    },
    {
      id: '3',
      title: '数据分析',
      description: '生成数据分析报告',
      icon: 'DataAnalysis',
      path: '/data/summary'
    }
  ]
  
  return suggestions.filter(item =>
    item.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    item.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

const userAvatar = computed(() => {
  return authStore.user?.avatar || ''
})

const userInitial = computed(() => {
  return authStore.user?.username?.charAt(0).toUpperCase() || 'U'
})

// 方法
const toggleSidebar = () => {
  emit('toggle-sidebar')
}

const getRoleText = (role: UserRole | null) => {
  const roleMap: Record<string, string> = {
    limited: '有限权限',
    standard: '标准权限',
    full: '完全权限'
  }
  return role ? roleMap[role] : '未知角色'
}

const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    info: 'InfoFilled',
    warning: 'WarningFilled',
    success: 'SuccessFilled',
    error: 'WarningFilled'
  }
  return iconMap[type] || 'InfoFilled'
}

const handleGlobalSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/data/query',
      query: { keyword: searchKeyword.value }
    })
    searchKeyword.value = ''
    showSearchSuggestions.value = false
  }
}

const selectSuggestion = (suggestion: any) => {
  router.push(suggestion.path)
  searchKeyword.value = ''
  showSearchSuggestions.value = false
}

const hideSearchSuggestions = () => {
  setTimeout(() => {
    showSearchSuggestions.value = false
  }, 200)
}

const handleNotificationClick = (notification: any) => {
  // 标记为已读
  notification.read = true
  
  // 根据通知类型跳转
  if (notification.type === 'warning') {
    router.push('/system/status')
  }
}

const markAllAsRead = () => {
  notifications.value.forEach(n => n.read = true)
  ElMessage.success('已标记全部通知为已读')
}

const viewAllNotifications = () => {
  router.push('/notifications')
}

const handleNotificationAction = (command: string) => {
  console.log('Notification action:', command)
}

const handleHelpAction = (command: string) => {
  switch (command) {
    case 'docs':
      window.open('/docs', '_blank')
      break
    case 'shortcuts':
      // 显示快捷键帮助
      break
    case 'feedback':
      router.push('/feedback')
      break
    case 'about':
      // 显示关于对话框
      break
  }
}

const handleSettingAction = (command: string) => {
  switch (command) {
    case 'theme':
      // 打开主题设置
      break
    case 'language':
      // 打开语言设置
      break
    case 'layout':
      // 打开布局设置
      break
    case 'preferences':
      router.push('/settings/preferences')
      break
  }
}

const handleUserAction = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'security':
      router.push('/security')
      break
    case 'preferences':
      router.push('/preferences')
      break
    case 'logout':
      try {
        await authStore.logout()
        router.push('/auth/login')
      } catch (error) {
        console.error('Logout failed:', error)
      }
      break
  }
}

// 生命周期
onMounted(() => {
  // 可以在这里加载通知数据
})
</script>

<style scoped>
.app-header {
  @apply h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6;
}

.header-left {
  @apply flex items-center gap-4;
}

.collapse-btn {
  @apply text-gray-600 hover:text-gray-800;
}

.breadcrumb-container {
  @apply hidden md:block;
}

.header-right {
  @apply flex items-center gap-4;
}

/* 全局搜索 */
.global-search {
  @apply relative;
}

.search-input {
  width: 300px;
}

.search-suggestions {
  @apply absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-80 overflow-y-auto;
}

.suggestion-item {
  @apply flex items-center gap-3 p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0;
}

.suggestion-icon {
  @apply w-8 h-8 bg-blue-100 text-blue-600 rounded flex items-center justify-center;
}

.suggestion-content {
  @apply flex-1;
}

.suggestion-title {
  @apply font-medium text-gray-800 text-sm;
}

.suggestion-desc {
  @apply text-gray-600 text-xs;
}

/* 通知中心 */
.notification-btn,
.help-btn,
.setting-btn {
  @apply w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer transition-colors;
}

.notification-menu {
  width: 320px;
  max-height: 400px;
}

.notification-header {
  @apply flex justify-between items-center p-4 border-b border-gray-100;
  font-weight: 600;
}

.notification-list {
  @apply max-h-64 overflow-y-auto;
}

.notification-item {
  @apply flex items-start gap-3 p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0;
}

.notification-item.unread {
  @apply bg-blue-50;
}

.notification-icon {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white;
}

.notification-icon.info {
  @apply bg-blue-500;
}

.notification-icon.warning {
  @apply bg-yellow-500;
}

.notification-icon.success {
  @apply bg-green-500;
}

.notification-icon.error {
  @apply bg-red-500;
}

.notification-content {
  @apply flex-1;
}

.notification-title {
  @apply font-medium text-gray-800 text-sm mb-1;
}

.notification-desc {
  @apply text-gray-600 text-xs mb-1;
}

.notification-time {
  @apply text-gray-500 text-xs;
}

.notification-footer {
  @apply p-4 border-t border-gray-100 text-center;
}

.empty-notifications {
  @apply py-8;
}

/* 用户菜单 */
.user-menu {
  @apply flex items-center gap-3 cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors;
}

.user-info {
  @apply hidden lg:block;
}

.user-name {
  @apply text-sm font-medium text-gray-800;
}

.user-role {
  @apply text-xs text-gray-500;
}

.dropdown-icon {
  @apply text-gray-400;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-header {
    @apply px-4;
  }
  
  .search-input {
    width: 200px;
  }
  
  .header-right {
    @apply gap-2;
  }
  
  .notification-btn,
  .help-btn,
  .setting-btn {
    @apply w-8 h-8;
  }
}

@media (max-width: 640px) {
  .global-search {
    @apply hidden;
  }
  
  .search-input {
    width: 150px;
  }
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  .app-header {
    @apply bg-gray-800 border-gray-700;
  }
  
  .collapse-btn {
    @apply text-gray-300 hover:text-white;
  }
  
  .notification-btn,
  .help-btn,
  .setting-btn {
    @apply bg-gray-700 hover:bg-gray-600;
  }
  
  .user-menu:hover {
    @apply bg-gray-700;
  }
  
  .user-name {
    @apply text-gray-200;
  }
  
  .user-role {
    @apply text-gray-400;
  }
}
</style>
