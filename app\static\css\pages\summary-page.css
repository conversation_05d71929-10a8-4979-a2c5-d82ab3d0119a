/**
 * 汇总页面核心样式模块
 * 提取自summary.html内联样式，保持完全一致的视觉效果
 */

/* ==========================================================================
   动画效果模块
   ========================================================================== */

/* 自动消失提示样式 */
.auto-dismiss-alert {
    animation: fadeOut 0.5s ease 3s forwards;
}

@keyframes fadeOut {
    from { 
        opacity: 1; 
    }
    to { 
        opacity: 0; 
        height: 0; 
        margin: 0; 
        padding: 0; 
    }
}

/* ==========================================================================
   表格样式增强模块
   ========================================================================== */

/* 表格基础样式 */
.data-table {
    border-collapse: collapse;
    width: 100% !important;
    margin-bottom: 1rem;
    table-layout: auto; /* 允许自动计算列宽 */
    min-width: 0; /* 防止表格溢出容器 */
}

.data-table th {
    background-color: #f2f2f2;
    font-weight: bold;
    text-align: left;
    padding: 10px;
    white-space: nowrap;
}

.data-table td {
    padding: 8px;
    vertical-align: middle;
}

/* 表格交替行颜色 */
.data-table tbody tr:nth-child(odd) {
    background-color: #f9f9f9;
}

/* 鼠标悬停高亮效果 */
.data-table tbody tr:hover {
    background-color: #e9f2f9;
}

/* ==========================================================================
   图表样式模块
   ========================================================================== */

/* 图表容器样式 */
.chart-container {
    height: 300px;
    position: relative;
}

/* ==========================================================================
   响应式表格模块
   ========================================================================== */

/* 响应式表格样式 */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* 汇总页面专用表格修复 */
.col-md-6 .table-responsive {
    max-width: 100%;
    width: 100%;
}

.col-md-6 .data-table {
    width: 100% !important;
    max-width: none;
}

/* 强制DataTables正确计算宽度 */
.dataTables_wrapper {
    width: 100%;
    max-width: 100%;
    overflow: visible;
}

.dataTables_wrapper .dataTables_scroll {
    width: 100%;
}

.dataTables_wrapper .dataTables_scrollBody {
    width: 100%;
    max-width: 100%;
}

/* ==========================================================================
   分页样式优化模块 - 参考详细汇总数据样式
   ========================================================================== */

/* 基础分页样式 */
.dataTables_paginate {
    margin-top: 1rem;
}

.dataTables_paginate .paginate_button {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    margin: 0 0.125rem;
    line-height: 1.25;
    color: #007bff;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.dataTables_paginate .paginate_button:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.dataTables_paginate .paginate_button.current {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.dataTables_paginate .paginate_button.disabled {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* 分页信息样式 */
.dataTables_info {
    padding-top: 0.75rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: #6c757d;
}

/* ==========================================================================
   平板端分页优化 (768px - 1024px)
   ========================================================================== */
@media (min-width: 769px) and (max-width: 1024px) {
    .dataTables_paginate .paginate_button {
        padding: 0.375rem 0.625rem;
        margin: 0 0.1rem;
        font-size: 0.875rem;
        min-width: 40px;
    }
    
    .dataTables_info {
        font-size: 0.875rem;
        padding-top: 0.5rem;
    }
}

/* ==========================================================================
   桌面端分页优化 (>1024px)
   ========================================================================== */
@media (min-width: 1025px) {
    .dataTables_paginate {
        text-align: right;
    }
    
    .dataTables_paginate .paginate_button {
        padding: 0.375rem 0.75rem;
        margin: 0 0.125rem;
        font-size: 0.875rem;
        min-width: auto;
    }
    
    .dataTables_info {
        font-size: 0.875rem;
        padding-top: 0.75rem;
        text-align: left;
    }
    
    /* 桌面端表格工具栏保持标准布局 */
    .dataTables_wrapper .row [class*="col-"] {
        padding: 0.75rem;
    }
}

/* ==========================================================================
   DataTables响应式样式增强模块
   ========================================================================== */

/* DataTables响应式折叠控制按钮 - 标准样式 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    top: 50%;
    left: 50%;
    height: 16px;
    width: 16px;
    margin-top: -8px;
    margin-left: -8px;
    display: block;
    position: absolute;
    color: white;
    border: 2px solid white;
    border-radius: 16px;
    box-shadow: 0 0 3px #444;
    background: #31b131;
    text-align: center;
    font-size: 11px;
    line-height: 12px;
    content: '+';
}

/* 展开状态的控制按钮 */
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
    content: '-';
    background: #d33333;
}

/* 控制列基础样式 */
td.dtr-control, th.dtr-control {
    position: relative;
    cursor: pointer;
    text-align: center;
    padding: 8px !important;
}

/* 确保控制列在任何情况下都可见 */
table.dataTable > thead > tr > th.dtr-control,
table.dataTable > tbody > tr > td.dtr-control {
    display: table-cell !important;
    visibility: visible !important;
}

/* 没有隐藏列时，不显示控制按钮 */
table.dataTable.dtr-inline:not(.collapsed) > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline:not(.collapsed) > tbody > tr > th.dtr-control:before {
    display: none;
}

/* 响应式详情数据样式 */
.dtr-data {
    word-break: break-word;
}

/* 详情行样式 */
.dtr-details {
    width: 100%;
}

.dtr-details td {
    padding: 5px 10px;
}

/* ==========================================================================
   数据单元格折叠样式模块
   ========================================================================== */

/* 数据单元格折叠样式 */
.cell-content {
    position: relative;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    display: block;
    transition: all 0.3s ease;
    color: #333;
}

/* 渐变遮罩效果 */
.cell-content:after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 30px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
    pointer-events: none;
}

/* 展开状态样式 */
.cell-content.expanded {
    white-space: normal;
    word-break: break-word;
    max-width: none;
    overflow: visible;
}

.cell-content.expanded:after {
    display: none;
}

/* 鼠标悬停状态 */
.cell-content:hover {
    color: #007bff;
}

/* ==========================================================================
   日期选择器样式模块
   ========================================================================== */

/* 日期选择器基础样式 */
input[type="date"] {
    position: relative;
}

/* 自定义日期选择器图标 */
input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 1;
    cursor: pointer;
}

/* 日期选择器位置调整 */
input[type="date"]::-webkit-datetime-edit {
    padding-right: 28px; /* 为图标留出空间 */
}

/* 调整弹出日历的位置 */
::-webkit-calendar-picker-indicator {
    position: relative; 
}

/* 兼容性处理 */
input[type="date"]::-webkit-inner-spin-button {
    display: none;
}

input[type="date"]::-webkit-clear-button {
    display: none;
}

/* ==========================================================================
   移动端响应式样式模块
   ========================================================================== */

/* 移动端样式优化 */
@media (max-width: 768px) {
    /* 单元格内容移动端调整 */
    .cell-content {
        max-width: 120px;
    }
    
    /* 移动端水平滚动表格优化 */
    .table-responsive {
        max-width: 100%;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }
    
    /* 移动端表格水平滚动样式 */
    .dataTables_scrollBody {
        border: none !important;
    }
    
    .dataTables_scroll {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* 移动端表格单元格样式 */
    .data-table th, .data-table td {
        padding: 8px 12px;
        font-size: 0.875rem;
        white-space: nowrap; /* 防止文字换行 */
        min-width: 80px; /* 设置最小列宽 */
    }
    
    /* 移动端表格头部样式 */
    .data-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }
    
    /* 移动端第一列（月份）固定样式 */
    .data-table th:nth-child(1),
    .data-table td:nth-child(1) {
        min-width: 100px;
        font-weight: 500;
    }
    
    /* 移动端数字列右对齐 */
    .data-table td:not(:first-child) {
        text-align: right;
    }
    
    /* 移动端分页控件优化 */
    .dataTables_paginate {
        text-align: center;
        margin-top: 1rem;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem;
        margin: 0;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        min-width: 44px; /* 增加触摸目标大小 */
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* 移动端分页按钮悬停效果 */
    .dataTables_paginate .paginate_button:hover {
        background-color: #f8f9fa;
        border-color: #007bff;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,123,255,0.15);
    }
    
    /* 移动端当前页按钮 */
    .dataTables_paginate .paginate_button.current {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
        font-weight: 600;
    }
    
    /* 移动端禁用按钮 */
    .dataTables_paginate .paginate_button.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #f8f9fa;
    }
    
    /* 移动端信息显示优化 */
    .dataTables_info {
        text-align: center;
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.75rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
    }
    
    /* 移动端表格工具栏布局 */
    .dataTables_wrapper .row {
        margin: 0;
    }
    
    .dataTables_wrapper .row [class*="col-"] {
        padding: 0.25rem;
    }
    
    /* 移动端搜索框优化 */
    .dataTables_filter {
        margin-bottom: 0.75rem;
    }
    
    .dataTables_filter input {
        width: 100% !important;
        padding: 0.5rem 0.75rem;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        font-size: 0.875rem;
    }
    
    .dataTables_filter label {
        width: 100%;
        margin-bottom: 0;
        font-weight: 500;
        color: #495057;
    }
    
    /* DataTables移动端响应式增强（备用） */
    table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
    table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
        top: 50%;
        transform: translateY(-50%);
        background-color: #007bff;
        box-shadow: 0 0 0.2rem rgba(0, 123, 255, 0.5);
    }
    
    /* 响应式数据单元格（备用） */
    .dtr-data {
        word-break: break-word;
        max-width: calc(100vw - 100px);
    }
    
    /* 移动端图表容器 */
    .chart-container {
        height: 250px;
    }
    
    /* 移动端卡片内容 */
    .card-body {
        padding: 0.75rem;
    }
    
    /* 移动端汇总表格容器特殊处理 */
    .col-md-6 {
        padding-left: 8px;
        padding-right: 8px;
    }
    
    /* 移动端表格标题 */
    .card-header h5 {
        font-size: 1rem;
        margin-bottom: 0;
    }
}