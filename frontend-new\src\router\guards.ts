import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 太享查询系统`
    }
    
    // 检查是否需要认证
    if (to.meta.requiresAuth !== false) {
      // 如果没有token，跳转到登录页
      if (!authStore.token) {
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
        return
      }
      
      // 如果有token但未认证，验证token
      if (!authStore.isAuthenticated) {
        try {
          const isValid = await authStore.checkAuth()
          if (!isValid) {
            next({
              name: 'Login',
              query: { redirect: to.fullPath }
            })
            return
          }
        } catch (error) {
          console.error('Auth check failed:', error)
          next({
            name: 'Login',
            query: { redirect: to.fullPath }
          })
          return
        }
      }
      
      // 检查权限
      if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
        const hasPermission = to.meta.permissions.some((permission: string) => {
          const [resource, action] = permission.split(':')
          return authStore.hasPermission(resource, action)
        })
        
        if (!hasPermission) {
          ElMessage.error('您没有权限访问该页面')
          next({ name: 'Dashboard' })
          return
        }
      }
      
      // 检查角色
      if (to.meta.roles && Array.isArray(to.meta.roles)) {
        const hasRole = authStore.hasAnyRole(to.meta.roles)
        
        if (!hasRole) {
          ElMessage.error('您的角色无权访问该页面')
          next({ name: 'Dashboard' })
          return
        }
      }
    } else {
      // 如果已经登录，访问登录页时重定向到首页
      if (to.name === 'Login' && authStore.isAuthenticated) {
        next({ name: 'Dashboard' })
        return
      }
    }
    
    next()
  })
  
  // 全局后置守卫
  router.afterEach((to, from) => {
    // 页面加载完成后的处理
    // 可以在这里添加页面访问统计等
    
    // 移除加载状态
    const loadingElement = document.querySelector('.page-loading')
    if (loadingElement) {
      loadingElement.remove()
    }
  })
  
  // 路由错误处理
  router.onError((error) => {
    console.error('Router error:', error)
    ElMessage.error('页面加载失败，请刷新重试')
  })
}
