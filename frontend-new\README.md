# 太享查询系统 - 现代化前端架构

基于 Vue 3 + TypeScript 的企业级金融数据查询展示系统，采用现代化前端架构，支持桌面端和移动端响应式设计。

## ✨ 特性

- 🚀 **现代化技术栈**: Vue 3 + TypeScript + Vite
- 📱 **响应式设计**: 完美适配桌面端、平板端和移动端
- 🎨 **优雅UI**: Element Plus + Tailwind CSS 组合
- 📊 **数据可视化**: ECharts 图表库
- 🔐 **权限管理**: 基于角色的访问控制
- ⚡ **性能优化**: 代码分割、懒加载、缓存策略
- 🛠️ **开发体验**: TypeScript、ESLint、Prettier
- 📦 **容器化部署**: Docker + Nginx

## 技术栈

- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus + Tailwind CSS
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **构建工具**: Vite
- **图表**: ECharts
- **HTTP客户端**: Axios

## 项目结构

```
frontend-new/
├── public/                     # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── api/                    # API接口层
│   │   ├── auth.ts            # 认证相关API
│   │   ├── data.ts            # 数据查询API
│   │   ├── export.ts          # 导出功能API
│   │   └── index.ts           # API统一导出
│   ├── assets/                 # 资源文件
│   │   ├── images/            # 图片资源
│   │   ├── icons/             # 图标资源
│   │   └── styles/            # 全局样式
│   │       ├── main.css       # 主样式文件
│   │       ├── variables.css  # CSS变量
│   │       └── responsive.css # 响应式样式
│   ├── components/             # 通用组件
│   │   ├── common/            # 基础组件
│   │   │   ├── AppHeader.vue  # 应用头部
│   │   │   ├── AppSidebar.vue # 侧边栏
│   │   │   ├── AppFooter.vue  # 页脚
│   │   │   └── LoadingSpinner.vue # 加载动画
│   │   ├── business/          # 业务组件
│   │   │   ├── DataTable.vue  # 数据表格
│   │   │   ├── ChartContainer.vue # 图表容器
│   │   │   ├── SearchForm.vue # 搜索表单
│   │   │   └── ExportButton.vue # 导出按钮
│   │   └── ui/                # UI组件
│   │       ├── Button.vue     # 按钮组件
│   │       ├── Modal.vue      # 模态框
│   │       └── Card.vue       # 卡片组件
│   ├── composables/           # 组合式函数
│   │   ├── useAuth.ts         # 认证逻辑
│   │   ├── useData.ts         # 数据处理
│   │   ├── useChart.ts        # 图表逻辑
│   │   └── useResponsive.ts   # 响应式逻辑
│   ├── layouts/               # 布局组件
│   │   ├── DefaultLayout.vue  # 默认布局
│   │   ├── AuthLayout.vue     # 认证布局
│   │   └── MobileLayout.vue   # 移动端布局
│   ├── pages/                 # 页面组件
│   │   ├── auth/              # 认证页面
│   │   │   └── Login.vue      # 登录页
│   │   ├── dashboard/         # 工作台
│   │   │   └── Home.vue       # 首页
│   │   ├── data/              # 数据查询
│   │   │   ├── Query.vue      # 数据查询
│   │   │   └── Summary.vue    # 数据汇总
│   │   ├── customer/          # 客户管理
│   │   │   ├── List.vue       # 客户列表
│   │   │   └── Detail.vue     # 客户详情
│   │   └── tools/             # 工具页面
│   │       ├── QRCode.vue     # 二维码生成
│   │       └── Calculator.vue # 计算器
│   ├── router/                # 路由配置
│   │   ├── index.ts           # 路由主文件
│   │   └── guards.ts          # 路由守卫
│   ├── stores/                # 状态管理
│   │   ├── auth.ts            # 认证状态
│   │   ├── data.ts            # 数据状态
│   │   └── ui.ts              # UI状态
│   ├── types/                 # TypeScript类型
│   │   ├── api.ts             # API类型
│   │   ├── auth.ts            # 认证类型
│   │   └── data.ts            # 数据类型
│   ├── utils/                 # 工具函数
│   │   ├── request.ts         # HTTP请求封装
│   │   ├── format.ts          # 格式化工具
│   │   ├── date.ts            # 日期工具
│   │   └── export.ts          # 导出工具
│   ├── App.vue                # 根组件
│   └── main.ts                # 应用入口
├── package.json               # 依赖配置
├── vite.config.ts            # Vite配置
├── tailwind.config.js        # Tailwind配置
├── tsconfig.json             # TypeScript配置
└── .env                      # 环境变量
```

## 核心设计原则

### 1. 组件化设计
- **原子化组件**: 最小可复用单元
- **业务组件**: 封装特定业务逻辑
- **页面组件**: 组合业务组件形成完整页面

### 2. 响应式优先
- **移动端优先**: 从小屏幕开始设计
- **断点系统**: sm(640px) md(768px) lg(1024px) xl(1280px)
- **自适应布局**: 使用Flexbox和Grid

### 3. 状态管理
- **Pinia Store**: 模块化状态管理
- **组合式API**: 逻辑复用和组织
- **响应式数据**: 自动更新UI

### 4. 类型安全
- **TypeScript**: 全面的类型检查
- **API类型**: 接口数据类型定义
- **组件Props**: 严格的属性类型

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装依赖

```bash
cd frontend-new
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
npm run type-check
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t hdsc-query-frontend .

# 运行容器
docker run -d -p 3000:80 hdsc-query-frontend
```

### 自动化部署

```bash
# 开发环境
./deploy.sh development

# 生产环境
./deploy.sh production v2.0.0
```

## 开发规范

### 1. 命名规范
- **组件**: PascalCase (DataTable.vue)
- **文件**: kebab-case (data-table.vue)
- **变量**: camelCase (userData)
- **常量**: UPPER_SNAKE_CASE (API_BASE_URL)

### 2. 代码组织
- **单一职责**: 每个组件只负责一个功能
- **组合优于继承**: 使用组合式API
- **依赖注入**: 使用provide/inject传递数据

### 3. 性能优化
- **懒加载**: 路由和组件按需加载
- **虚拟滚动**: 大数据列表优化
- **缓存策略**: 合理使用缓存
- **代码分割**: 按功能模块分割代码
