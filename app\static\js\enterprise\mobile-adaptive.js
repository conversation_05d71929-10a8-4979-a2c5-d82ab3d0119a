/*
 * 太享查询系统 - 移动端适配专用脚本
 * Mobile Adaptive Scripts for Enterprise Customer Summary
 * Version: 3.0
 * Last Updated: 2024-12-28
 */

/**
 * 移动端适配管理器
 * 专门处理移动端的响应式逻辑和交互优化
 */
class MobileAdaptiveManager {
    constructor() {
        this.isMobile = false;
        this.isTablet = false;
        this.currentBreakpoint = 'desktop';
        this.currentTab = 'receivable';
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.isTabDropdownOpen = false;
        
        // 设备检测
        this.detectDevice();
        
        // 绑定上下文
        this.handleResize = this.handleResize.bind(this);
        this.handleTabChange = this.handleTabChange.bind(this);
        this.handleTouchStart = this.handleTouchStart.bind(this);
        this.handleTouchMove = this.handleTouchMove.bind(this);
        this.handleTouchEnd = this.handleTouchEnd.bind(this);
        
        console.log('移动端适配管理器已创建');
    }

    /**
     * 初始化移动端适配系统
     */
    async init() {
        try {
            console.log('初始化移动端适配系统...');
            
            // 设置响应式监听
            this.setupResponsiveListeners();
            
            // 初始化移动端标签页
            this.initMobileTabSystem();
            
            // 初始化触摸手势
            this.initTouchGestures();
            
            // 初始化移动端卡片功能
            this.initMobileCardSystem();
            
            // 执行初始布局调整
            this.handleResize();
            
            console.log('移动端适配系统初始化完成');
            
        } catch (error) {
            console.error('移动端适配系统初始化失败:', error);
        }
    }

    /**
     * 设备检测
     */
    detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const screenWidth = window.innerWidth;
        
        // 移动设备检测
        this.isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent) || screenWidth < 768;
        this.isTablet = /ipad|tablet/i.test(userAgent) || (screenWidth >= 768 && screenWidth < 1200);
        
        // 断点检测
        if (screenWidth < 576) {
            this.currentBreakpoint = 'xs';
        } else if (screenWidth < 768) {
            this.currentBreakpoint = 'sm';
        } else if (screenWidth < 992) {
            this.currentBreakpoint = 'md';
        } else if (screenWidth < 1200) {
            this.currentBreakpoint = 'lg';
        } else {
            this.currentBreakpoint = 'xl';
        }
        
        console.log(`设备检测: 移动端=${this.isMobile}, 平板=${this.isTablet}, 断点=${this.currentBreakpoint}`);
    }

    /**
     * 设置响应式监听器
     */
    setupResponsiveListeners() {
        // 窗口大小变化监听
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(this.handleResize, 250);
        });
        
        // 方向变化监听
        window.addEventListener('orientationchange', () => {
            setTimeout(this.handleResize, 500);
        });
        
        // 可见性变化监听
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                setTimeout(this.handleResize, 100);
            }
        });
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        const oldBreakpoint = this.currentBreakpoint;
        this.detectDevice();
        
        // 断点变化时重新布局
        if (oldBreakpoint !== this.currentBreakpoint) {
            console.log(`断点变化: ${oldBreakpoint} → ${this.currentBreakpoint}`);
            this.adjustLayoutForBreakpoint();
        }
        
        // 调整卡片布局
        this.adjustCardLayout();
        
        // 调整图表尺寸
        this.adjustChartSizes();
    }

    /**
     * 根据断点调整布局
     */
    adjustLayoutForBreakpoint() {
        const body = document.body;
        
        // 移除所有断点类
        body.classList.remove('breakpoint-xs', 'breakpoint-sm', 'breakpoint-md', 'breakpoint-lg', 'breakpoint-xl');
        
        // 添加当前断点类
        body.classList.add(`breakpoint-${this.currentBreakpoint}`);
        
        // 根据断点调整组件
        if (this.currentBreakpoint === 'xs' || this.currentBreakpoint === 'sm') {
            this.enableMobileMode();
        } else if (this.currentBreakpoint === 'md' || this.currentBreakpoint === 'lg') {
            this.enableTabletMode();
        } else {
            this.enableDesktopMode();
        }
    }

    /**
     * 启用移动端模式
     */
    enableMobileMode() {
        console.log('启用移动端模式');
        
        // 调整页面头部
        this.adjustPageHeader(true);
        
        // 调整统计卡片
        this.adjustStatsCards('mobile');
        
        // 调整图表容器
        this.adjustChartContainers('mobile');
        
        // 确保移动端标签页可见
        this.ensureMobileTabsVisible();
    }

    /**
     * 启用平板模式
     */
    enableTabletMode() {
        console.log('启用平板模式');
        
        // 调整页面头部
        this.adjustPageHeader(false);
        
        // 调整统计卡片
        this.adjustStatsCards('tablet');
        
        // 调整图表容器
        this.adjustChartContainers('tablet');
        
        // 显示移动端卡片视图
        this.ensureMobileTabsVisible();
    }

    /**
     * 启用桌面端模式
     */
    enableDesktopMode() {
        console.log('启用桌面端模式');
        
        // 调整页面头部
        this.adjustPageHeader(false);
        
        // 调整统计卡片
        this.adjustStatsCards('desktop');
        
        // 调整图表容器
        this.adjustChartContainers('desktop');
        
        // 隐藏移动端标签页
        this.hideMobileTabs();
    }

    /**
     * 调整页面头部
     */
    adjustPageHeader(isMobile) {
        const pageHeader = document.querySelector('.enterprise-page-header');
        const pageActions = document.querySelector('.enterprise-page-actions');
        
        if (!pageHeader || !pageActions) return;
        
        if (isMobile) {
            pageHeader.style.textAlign = 'center';
            pageActions.style.flexDirection = 'column';
            pageActions.style.gap = '0.5rem';
        } else {
            pageHeader.style.textAlign = '';
            pageActions.style.flexDirection = '';
            pageActions.style.gap = '';
        }
    }

    /**
     * 调整统计卡片
     */
    adjustStatsCards(mode) {
        const statsGrid = document.querySelector('.enterprise-stats-grid');
        if (!statsGrid) return;
        
        switch (mode) {
            case 'mobile':
                statsGrid.style.gridTemplateColumns = '1fr';
                break;
            case 'tablet':
                statsGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                break;
            case 'desktop':
                statsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(250px, 1fr))';
                break;
        }
    }

    /**
     * 调整图表容器
     */
    adjustChartContainers(mode) {
        const chartWrappers = document.querySelectorAll('.enterprise-chart-wrapper');
        
        chartWrappers.forEach(wrapper => {
            switch (mode) {
                case 'mobile':
                    wrapper.style.height = '200px';
                    break;
                case 'tablet':
                    wrapper.style.height = '220px';
                    break;
                case 'desktop':
                    wrapper.style.height = '250px';
                    break;
            }
        });
    }

    /**
     * 初始化移动端标签页系统
     */
    initMobileTabSystem() {
        const mobileTabIndicator = document.querySelector('.mobile-tab-indicator');
        if (!mobileTabIndicator) return;
        
        // 创建下拉菜单
        this.createMobileTabDropdown();
        
        // 绑定点击事件
        mobileTabIndicator.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleMobileTabDropdown();
        });
        
        // 初始化标签页内容
        this.updateMobileTabContent();
    }

    /**
     * 创建移动端标签页下拉菜单
     */
    createMobileTabDropdown() {
        const tabSelector = document.querySelector('.mobile-tab-selector');
        if (!tabSelector) return;
        
        const dropdown = document.createElement('div');
        dropdown.className = 'mobile-tab-dropdown';
        dropdown.innerHTML = `
            <div class="mobile-tab-option" data-tab="receivable">
                <div class="option-icon">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="option-content">
                    <div class="option-title">待收明细</div>
                    <div class="option-subtitle">期数分组的待收款信息</div>
                </div>
                <div class="option-badge">5项</div>
            </div>
            <div class="mobile-tab-option" data-tab="orders">
                <div class="option-icon">
                    <i class="bi bi-list-ul"></i>
                </div>
                <div class="option-content">
                    <div class="option-title">订单详情</div>
                    <div class="option-subtitle">完整的订单信息记录</div>
                </div>
                <div class="option-badge">12项</div>
            </div>
            <div class="mobile-tab-option" data-tab="finance">
                <div class="option-icon">
                    <i class="bi bi-receipt"></i>
                </div>
                <div class="option-content">
                    <div class="option-title">财务流水</div>
                    <div class="option-subtitle">资金进出明细记录</div>
                </div>
                <div class="option-badge">8项</div>
            </div>
        `;
        
        tabSelector.appendChild(dropdown);
        
        // 绑定选项点击事件
        dropdown.addEventListener('click', (e) => {
            const option = e.target.closest('.mobile-tab-option');
            if (option) {
                const tabId = option.dataset.tab;
                this.switchToTab(tabId);
                this.closeMobileTabDropdown();
            }
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!tabSelector.contains(e.target)) {
                this.closeMobileTabDropdown();
            }
        });
    }

    /**
     * 切换标签页
     */
    switchToTab(tabId) {
        console.log(`切换到标签页: ${tabId}`);
        
        // 更新当前标签
        this.currentTab = tabId;
        
        // 更新移动端指示器
        this.updateMobileTabIndicator(tabId);
        
        // 更新下拉菜单选中状态
        this.updateMobileTabOptions(tabId);
        
        // 显示对应的标签页内容
        this.showTabContent(tabId);
        
        // 触发标签页变化事件
        this.handleTabChange(tabId);
    }

    /**
     * 更新移动端标签页指示器
     */
    updateMobileTabIndicator(tabId) {
        const indicatorIcon = document.querySelector('.mobile-tab-indicator .indicator-icon i');
        const indicatorText = document.querySelector('.mobile-tab-indicator .indicator-text');
        const indicatorBadge = document.querySelector('.mobile-tab-indicator .indicator-badge');
        
        if (!indicatorText || !indicatorIcon) return;
        
        const tabInfo = {
            'receivable': { 
                name: '待收明细', 
                icon: 'bi-currency-dollar', 
                count: '5项' 
            },
            'orders': { 
                name: '订单详情', 
                icon: 'bi-list-ul', 
                count: '12项' 
            },
            'finance': { 
                name: '财务流水', 
                icon: 'bi-receipt', 
                count: '8项' 
            }
        };
        
        const info = tabInfo[tabId];
        if (info) {
            indicatorText.textContent = info.name;
            indicatorIcon.className = `bi ${info.icon}`;
            if (indicatorBadge) {
                indicatorBadge.textContent = info.count;
            }
        }
    }

    /**
     * 更新移动端标签页选项状态
     */
    updateMobileTabOptions(tabId) {
        const options = document.querySelectorAll('.mobile-tab-option');
        options.forEach(option => {
            if (option.dataset.tab === tabId) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });
    }

    /**
     * 显示标签页内容
     */
    showTabContent(tabId) {
        // 隐藏所有标签页内容
        const tabPanes = document.querySelectorAll('.tab-pane');
        tabPanes.forEach(pane => {
            pane.classList.remove('show', 'active');
        });
        
        // 显示目标标签页内容
        const targetPane = document.getElementById(tabId);
        if (targetPane) {
            targetPane.classList.add('show', 'active');
            
            // 触发标签页显示完成事件
            setTimeout(() => {
                this.onTabShown(tabId);
            }, 150);
        }
    }

    /**
     * 标签页显示完成回调
     */
    onTabShown(tabId) {
        // 调整卡片布局
        this.adjustCardLayout();
        
        // 通知主管理器
        if (window.enterpriseCustomerSummaryManager) {
            const event = new CustomEvent('tabShown', { detail: { tabId } });
            document.dispatchEvent(event);
        }
    }

    /**
     * 切换移动端标签页下拉菜单
     */
    toggleMobileTabDropdown() {
        const dropdown = document.querySelector('.mobile-tab-dropdown');
        const indicator = document.querySelector('.mobile-tab-indicator');
        
        if (!dropdown || !indicator) return;
        
        if (this.isTabDropdownOpen) {
            this.closeMobileTabDropdown();
        } else {
            this.openMobileTabDropdown();
        }
    }

    /**
     * 打开移动端标签页下拉菜单
     */
    openMobileTabDropdown() {
        const dropdown = document.querySelector('.mobile-tab-dropdown');
        const indicator = document.querySelector('.mobile-tab-indicator');
        
        if (!dropdown || !indicator) return;
        
        dropdown.classList.add('show');
        indicator.classList.add('expanded');
        this.isTabDropdownOpen = true;
    }

    /**
     * 关闭移动端标签页下拉菜单
     */
    closeMobileTabDropdown() {
        const dropdown = document.querySelector('.mobile-tab-dropdown');
        const indicator = document.querySelector('.mobile-tab-indicator');
        
        if (!dropdown || !indicator) return;
        
        dropdown.classList.remove('show');
        indicator.classList.remove('expanded');
        this.isTabDropdownOpen = false;
    }

    /**
     * 确保移动端标签页可见
     */
    ensureMobileTabsVisible() {
        const mobileSelector = document.querySelector('.mobile-tab-selector');
        const desktopTabs = document.querySelector('.enterprise-tab-nav');
        
        if (mobileSelector) {
            mobileSelector.style.display = 'block';
        }
        
        if (desktopTabs) {
            desktopTabs.style.display = 'none';
        }
    }

    /**
     * 隐藏移动端标签页
     */
    hideMobileTabs() {
        const mobileSelector = document.querySelector('.mobile-tab-selector');
        const desktopTabs = document.querySelector('.enterprise-tab-nav');
        
        if (mobileSelector) {
            mobileSelector.style.display = 'none';
        }
        
        if (desktopTabs) {
            desktopTabs.style.display = 'flex';
        }
    }

    /**
     * 初始化触摸手势
     */
    initTouchGestures() {
        const tabContent = document.querySelector('.enterprise-tab-content');
        if (!tabContent) return;
        
        // 绑定触摸事件
        tabContent.addEventListener('touchstart', this.handleTouchStart, { passive: true });
        tabContent.addEventListener('touchmove', this.handleTouchMove, { passive: true });
        tabContent.addEventListener('touchend', this.handleTouchEnd, { passive: true });
        
        console.log('触摸手势初始化完成');
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(e) {
        if (e.touches.length !== 1) return;
        
        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
        this.touchStartTime = Date.now();
    }

    /**
     * 处理触摸移动
     */
    handleTouchMove(e) {
        // 防止过度滚动时的意外手势
        if (e.touches.length !== 1) return;
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(e) {
        if (e.changedTouches.length !== 1) return;
        
        const touchEndX = e.changedTouches[0].clientX;
        const touchEndY = e.changedTouches[0].clientY;
        const touchEndTime = Date.now();
        
        const deltaX = touchEndX - this.touchStartX;
        const deltaY = touchEndY - this.touchStartY;
        const deltaTime = touchEndTime - this.touchStartTime;
        
        // 检测滑动手势
        const minSwipeDistance = 50;
        const maxSwipeTime = 300;
        const maxVerticalDistance = 100;
        
        if (Math.abs(deltaY) < maxVerticalDistance && deltaTime < maxSwipeTime && Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                // 向右滑动 - 上一个标签页
                this.switchToPreviousTab();
            } else {
                // 向左滑动 - 下一个标签页
                this.switchToNextTab();
            }
        }
    }

    /**
     * 切换到上一个标签页
     */
    switchToPreviousTab() {
        const tabs = ['receivable', 'orders', 'finance'];
        const currentIndex = tabs.indexOf(this.currentTab);
        const previousIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
        
        this.switchToTab(tabs[previousIndex]);
        this.showSwipeNotification('上一页');
    }

    /**
     * 切换到下一个标签页
     */
    switchToNextTab() {
        const tabs = ['receivable', 'orders', 'finance'];
        const currentIndex = tabs.indexOf(this.currentTab);
        const nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
        
        this.switchToTab(tabs[nextIndex]);
        this.showSwipeNotification('下一页');
    }

    /**
     * 显示滑动通知
     */
    showSwipeNotification(text) {
        const notification = document.createElement('div');
        notification.className = 'swipe-notification';
        notification.textContent = text;
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            z-index: 9999;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        `;
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);
        
        // 自动移除
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 200);
        }, 1000);
    }

    /**
     * 初始化移动端卡片系统
     */
    initMobileCardSystem() {
        console.log('初始化移动端卡片系统...');
        
        // 初始化卡片交互
        this.initCardInteractions();
        
        // 初始化卡片操作按钮
        this.initCardActions();
        
        // 初始化卡片动画
        this.initCardAnimations();
    }

    /**
     * 初始化卡片交互
     */
    initCardInteractions() {
        // 卡片点击展开功能
        document.addEventListener('click', (e) => {
            const card = e.target.closest('.mobile-data-card');
            if (card && !e.target.closest('.card-actions')) {
                this.toggleCardExpansion(card);
            }
        });
        
        // 长按显示更多选项
        let longPressTimer;
        document.addEventListener('touchstart', (e) => {
            const card = e.target.closest('.mobile-data-card');
            if (card) {
                longPressTimer = setTimeout(() => {
                    this.showCardContextMenu(card, e.touches[0]);
                }, 500);
            }
        });
        
        document.addEventListener('touchend', () => {
            clearTimeout(longPressTimer);
        });
        
        document.addEventListener('touchmove', () => {
            clearTimeout(longPressTimer);
        });
    }

    /**
     * 切换卡片展开状态
     */
    toggleCardExpansion(card) {
        const isExpanded = card.classList.contains('expanded');
        
        // 关闭其他展开的卡片
        document.querySelectorAll('.mobile-data-card.expanded').forEach(expandedCard => {
            if (expandedCard !== card) {
                expandedCard.classList.remove('expanded');
            }
        });
        
        // 切换当前卡片状态
        card.classList.toggle('expanded', !isExpanded);
        
        if (!isExpanded) {
            // 展开时滚动到可见位置
            setTimeout(() => {
                card.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }, 150);
        }
    }

    /**
     * 显示卡片上下文菜单
     */
    showCardContextMenu(card, touch) {
        const menu = document.createElement('div');
        menu.className = 'card-context-menu';
        menu.style.cssText = `
            position: fixed;
            top: ${touch.clientY - 50}px;
            left: ${touch.clientX - 75}px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            min-width: 150px;
            overflow: hidden;
        `;
        
        const category = card.dataset.category;
        const menuItems = this.getContextMenuItems(category);
        
        menu.innerHTML = menuItems.map(item => `
            <div class="context-menu-item" data-action="${item.action}">
                <i class="bi ${item.icon}"></i>
                <span>${item.text}</span>
            </div>
        `).join('');
        
        document.body.appendChild(menu);
        
        // 绑定菜单项点击事件
        menu.addEventListener('click', (e) => {
            const item = e.target.closest('.context-menu-item');
            if (item) {
                this.handleContextMenuAction(card, item.dataset.action);
                document.body.removeChild(menu);
            }
        });
        
        // 点击外部关闭菜单
        setTimeout(() => {
            const closeMenu = (e) => {
                if (!menu.contains(e.target)) {
                    document.body.removeChild(menu);
                    document.removeEventListener('click', closeMenu);
                }
            };
            document.addEventListener('click', closeMenu);
        }, 100);
    }

    /**
     * 获取上下文菜单项
     */
    getContextMenuItems(category) {
        const commonItems = [
            { action: 'expand', icon: 'bi-arrows-expand', text: '展开详情' },
            { action: 'copy', icon: 'bi-clipboard', text: '复制信息' },
            { action: 'share', icon: 'bi-share', text: '分享' }
        ];
        
        switch (category) {
            case 'receivable':
                return [
                    ...commonItems,
                    { action: 'remind', icon: 'bi-bell', text: '催收提醒' }
                ];
            case 'orders':
                return [
                    ...commonItems,
                    { action: 'track', icon: 'bi-geo-alt', text: '订单追踪' },
                    { action: 'invoice', icon: 'bi-receipt', text: '查看发票' }
                ];
            case 'finance':
                return [
                    ...commonItems,
                    { action: 'voucher', icon: 'bi-file-earmark', text: '查看凭证' },
                    { action: 'export', icon: 'bi-download', text: '导出记录' }
                ];
            default:
                return commonItems;
        }
    }

    /**
     * 处理上下文菜单操作
     */
    handleContextMenuAction(card, action) {
        console.log(`执行卡片操作: ${action}`);
        
        switch (action) {
            case 'expand':
                this.toggleCardExpansion(card);
                break;
            case 'copy':
                this.copyCardInfo(card);
                break;
            case 'share':
                this.shareCardInfo(card);
                break;
            default:
                this.showActionNotification(`功能 "${action}" 正在开发中`);
        }
    }

    /**
     * 复制卡片信息
     */
    async copyCardInfo(card) {
        try {
            const cardText = this.extractCardText(card);
            await navigator.clipboard.writeText(cardText);
            this.showActionNotification('信息已复制到剪贴板');
        } catch (error) {
            console.error('复制失败:', error);
            this.showActionNotification('复制失败，请手动复制');
        }
    }

    /**
     * 提取卡片文本信息
     */
    extractCardText(card) {
        const title = card.querySelector('.card-title')?.textContent || '';
        const amount = card.querySelector('.amount-value')?.textContent || '';
        const status = card.querySelector('.status-badge')?.textContent || '';
        
        return `${title}\n金额: ${amount}\n状态: ${status}`;
    }

    /**
     * 分享卡片信息
     */
    shareCardInfo(card) {
        if (navigator.share) {
            const cardText = this.extractCardText(card);
            navigator.share({
                title: '订单信息',
                text: cardText
            }).catch(error => {
                console.error('分享失败:', error);
                this.showActionNotification('分享功能不可用');
            });
        } else {
            this.copyCardInfo(card);
            this.showActionNotification('已复制信息，可手动分享');
        }
    }

    /**
     * 显示操作通知
     */
    showActionNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'action-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transition: all 0.3s ease-in-out;
        `;
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(-50%) translateY(-10px)';
        }, 10);
        
        // 自动移除
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(10px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    /**
     * 初始化卡片操作按钮
     */
    initCardActions() {
        // 全局卡片操作函数
        window.showDetails = (button) => {
            const card = button.closest('.mobile-data-card');
            this.showCardDetails(card);
        };
        
        window.expandCard = (button) => {
            const card = button.closest('.mobile-data-card');
            this.toggleCardExpansion(card);
        };
        
        window.showOrderDetails = (button) => {
            const card = button.closest('.mobile-data-card');
            this.showOrderDetails(card);
        };
        
        window.showPaymentHistory = (button) => {
            const card = button.closest('.mobile-data-card');
            this.showPaymentHistory(card);
        };
        
        window.showTransactionDetails = (button) => {
            const card = button.closest('.mobile-data-card');
            this.showTransactionDetails(card);
        };
        
        window.copyTransactionId = (button) => {
            const card = button.closest('.mobile-data-card');
            this.copyTransactionId(card);
        };
    }

    /**
     * 显示卡片详情
     */
    showCardDetails(card) {
        this.showActionNotification('详情功能正在开发中');
    }

    /**
     * 显示订单详情
     */
    showOrderDetails(card) {
        this.showActionNotification('订单详情功能正在开发中');
    }

    /**
     * 显示还款记录
     */
    showPaymentHistory(card) {
        // 调用主管理器的还款记录功能
        if (window.enterpriseCustomerSummaryManager) {
            const button = card.querySelector('.btn[onclick*="showPaymentHistory"]');
            if (button) {
                window.enterpriseCustomerSummaryManager.showPaymentHistory(button);
            } else {
                this.showActionNotification('无法获取订单信息');
            }
        } else {
            this.showActionNotification('系统未准备就绪，请稍后重试');
        }
    }

    /**
     * 显示交易详情
     */
    showTransactionDetails(card) {
        this.showActionNotification('交易详情功能正在开发中');
    }

    /**
     * 复制交易流水号
     */
    async copyTransactionId(card) {
        try {
            const transactionId = card.querySelector('.transaction-id .detail-value')?.textContent;
            if (transactionId) {
                await navigator.clipboard.writeText(transactionId);
                this.showActionNotification('流水号已复制');
            } else {
                this.showActionNotification('未找到流水号');
            }
        } catch (error) {
            this.showActionNotification('复制失败');
        }
    }

    /**
     * 初始化卡片动画
     */
    initCardAnimations() {
        // 观察器配置
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        // 创建观察器
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);
        
        // 观察所有卡片
        document.querySelectorAll('.mobile-data-card').forEach(card => {
            observer.observe(card);
        });
    }

    /**
     * 调整卡片布局
     */
    adjustCardLayout() {
        const cardViews = document.querySelectorAll('.mobile-card-view');
        
        cardViews.forEach(view => {
            if (this.currentBreakpoint === 'md' || this.currentBreakpoint === 'lg') {
                // 平板模式：2列网格
                view.style.display = 'grid';
                view.style.gridTemplateColumns = 'repeat(auto-fit, minmax(400px, 1fr))';
                view.style.gap = '1rem';
            } else {
                // 移动端模式：单列
                view.style.display = 'block';
                view.style.gridTemplateColumns = '';
                view.style.gap = '';
            }
        });
    }

    /**
     * 调整图表尺寸
     */
    adjustChartSizes() {
        // 通知图表管理器调整尺寸
        if (window.enterpriseCustomerSummaryManager) {
            setTimeout(() => {
                window.enterpriseCustomerSummaryManager.resizeCharts();
            }, 100);
        }
    }

    /**
     * 更新移动端标签页内容
     */
    updateMobileTabContent() {
        // 确保当前标签页处于活动状态
        this.showTabContent(this.currentTab);
    }

    /**
     * 处理标签页变化
     */
    handleTabChange(tabId) {
        console.log(`标签页变化: ${tabId}`);
        
        // 调整布局
        setTimeout(() => {
            this.adjustCardLayout();
            this.adjustChartSizes();
        }, 150);
    }

    /**
     * 销毁移动端适配管理器
     */
    destroy() {
        console.log('销毁移动端适配管理器...');
        
        // 移除事件监听器
        window.removeEventListener('resize', this.handleResize);
        window.removeEventListener('orientationchange', this.handleResize);
        
        // 清理全局函数
        const globalFunctions = [
            'showDetails', 'expandCard', 'showOrderDetails', 
            'showPaymentHistory', 'showTransactionDetails', 'copyTransactionId'
        ];
        
        globalFunctions.forEach(func => {
            if (window[func]) {
                delete window[func];
            }
        });
        
        console.log('移动端适配管理器已销毁');
    }
}

// 全局实例
window.MobileAdaptiveManager = MobileAdaptiveManager;

// 自动初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('移动端适配系统DOM加载完成');
    
    // 延迟初始化，确保其他系统已准备就绪
    setTimeout(async () => {
        try {
            window.mobileAdaptiveManager = new MobileAdaptiveManager();
            await window.mobileAdaptiveManager.init();
        } catch (error) {
            console.error('移动端适配系统启动失败:', error);
        }
    }, 800);
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.mobileAdaptiveManager) {
        window.mobileAdaptiveManager.destroy();
    }
});