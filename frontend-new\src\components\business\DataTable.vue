<template>
  <div class="data-table-container">
    <!-- 表格工具栏 -->
    <div class="table-toolbar mb-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <!-- 搜索区域 -->
      <div class="search-area flex-1 w-full sm:w-auto">
        <el-input
          v-model="searchKeyword"
          :placeholder="searchConfig.placeholder"
          clearable
          class="w-full sm:w-80"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <!-- 操作按钮区域 -->
      <div class="action-area flex flex-wrap gap-2">
        <el-button
          v-if="exportable"
          type="primary"
          :icon="Download"
          @click="handleExport"
          :loading="exportLoading"
        >
          导出
        </el-button>
        
        <el-button
          :icon="Refresh"
          @click="handleRefresh"
          :loading="loading"
        >
          刷新
        </el-button>
        
        <!-- 自定义操作按钮 -->
        <slot name="actions"></slot>
      </div>
    </div>
    
    <!-- 快速筛选 -->
    <div v-if="quickFilters.length > 0" class="quick-filters mb-4">
      <div class="flex flex-wrap gap-2">
        <el-tag
          v-for="filter in quickFilters"
          :key="filter.value"
          :type="activeFilters.includes(filter.value) ? 'primary' : 'info'"
          :effect="activeFilters.includes(filter.value) ? 'dark' : 'plain'"
          class="cursor-pointer"
          @click="toggleFilter(filter)"
        >
          {{ filter.label }}
        </el-tag>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        v-bind="tableProps"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        class="w-full"
        :class="{ 'mobile-table': isMobile }"
      >
        <!-- 选择列 -->
        <el-table-column
          v-if="selectable"
          type="selection"
          width="55"
          :selectable="selectableFunction"
        />
        
        <!-- 展开列 -->
        <el-table-column
          v-if="expandable"
          type="expand"
          width="55"
        >
          <template #default="{ row }">
            <slot name="expand" :row="row"></slot>
          </template>
        </el-table-column>
        
        <!-- 数据列 -->
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.key"
          :prop="column.key"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :sortable="column.sortable"
          :align="column.align || 'left'"
          :fixed="column.fixed"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template #default="{ row, column: col, $index }">
            <!-- 自定义列内容 -->
            <slot
              :name="`column-${column.key}`"
              :row="row"
              :column="col"
              :index="$index"
              :value="row[column.key]"
            >
              <!-- 默认格式化 -->
              <span v-if="column.formatter">
                {{ column.formatter(row[column.key], row) }}
              </span>
              <span v-else>
                {{ row[column.key] }}
              </span>
            </slot>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column
          v-if="hasActions"
          label="操作"
          :width="actionColumnWidth"
          fixed="right"
        >
          <template #default="{ row, $index }">
            <slot name="actions" :row="row" :index="$index">
              <el-button type="primary" link size="small">
                查看
              </el-button>
            </slot>
          </template>
        </el-table-column>
        
        <!-- 空数据 -->
        <template #empty>
          <div class="empty-data py-8">
            <el-empty :description="emptyText" />
          </div>
        </template>
      </el-table>
    </div>
    
    <!-- 分页 -->
    <div v-if="pagination && pagination.total > 0" class="pagination-wrapper mt-4">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="currentPageSize"
        :total="pagination.total"
        :page-sizes="pagination.pageSizes"
        :layout="paginationLayout"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="flex justify-center sm:justify-end"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Search, Download, Refresh } from '@element-plus/icons-vue'
import type { 
  TableColumn, 
  PaginationConfig, 
  QuickFilter,
  SearchConfig 
} from '@/types/data'
import { useResponsive } from '@/composables/useResponsive'

interface Props {
  data: any[]
  columns: TableColumn[]
  loading?: boolean
  pagination?: PaginationConfig
  searchConfig?: SearchConfig
  quickFilters?: QuickFilter[]
  selectable?: boolean
  expandable?: boolean
  exportable?: boolean
  emptyText?: string
  actionColumnWidth?: number | string
  selectableFunction?: (row: any, index: number) => boolean
  tableProps?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchConfig: () => ({ placeholder: '请输入搜索关键词', fields: [] }),
  quickFilters: () => [],
  selectable: false,
  expandable: false,
  exportable: false,
  emptyText: '暂无数据',
  actionColumnWidth: 120,
  tableProps: () => ({})
})

const emit = defineEmits<{
  search: [keyword: string]
  refresh: []
  export: []
  selectionChange: [selection: any[]]
  sortChange: [sort: { prop: string; order: string }]
  pageChange: [page: number, pageSize: number]
}>()

const { isMobile } = useResponsive()

// 响应式数据
const tableRef = ref()
const searchKeyword = ref('')
const activeFilters = ref<any[]>([])
const exportLoading = ref(false)
const selectedRows = ref<any[]>([])

// 分页数据
const currentPage = ref(props.pagination?.page || 1)
const currentPageSize = ref(props.pagination?.pageSize || 20)

// 计算属性
const tableData = computed(() => props.data)

const visibleColumns = computed(() => {
  return props.columns.filter(col => !col.hidden)
})

const hasActions = computed(() => {
  return !!$slots.actions
})

const paginationLayout = computed(() => {
  return isMobile.value 
    ? 'prev, pager, next' 
    : 'total, sizes, prev, pager, next, jumper'
})

// 方法
const handleSearch = debounce((keyword: string) => {
  emit('search', keyword)
}, 300)

const handleRefresh = () => {
  emit('refresh')
}

const handleExport = async () => {
  exportLoading.value = true
  try {
    emit('export')
  } finally {
    exportLoading.value = false
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
  emit('selectionChange', selection)
}

const handleSortChange = (sort: { prop: string; order: string }) => {
  emit('sortChange', sort)
}

const handleSizeChange = (size: number) => {
  currentPageSize.value = size
  emit('pageChange', currentPage.value, size)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  emit('pageChange', page, currentPageSize.value)
}

const toggleFilter = (filter: QuickFilter) => {
  const index = activeFilters.value.indexOf(filter.value)
  if (index > -1) {
    activeFilters.value.splice(index, 1)
  } else {
    activeFilters.value.push(filter.value)
  }
  // 触发筛选事件
  emit('search', searchKeyword.value)
}

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 监听分页变化
watch(() => props.pagination, (newPagination) => {
  if (newPagination) {
    currentPage.value = newPagination.page
    currentPageSize.value = newPagination.pageSize
  }
}, { deep: true })

// 暴露方法
defineExpose({
  getSelectedRows: () => selectedRows.value,
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: any, selected?: boolean) => {
    tableRef.value?.toggleRowSelection(row, selected)
  }
})
</script>

<style scoped>
.data-table-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
  padding: 1rem;
}

.table-toolbar {
  @apply border-b border-gray-100 pb-4;
}

.quick-filters {
  @apply border-b border-gray-100 pb-4;
}

.table-wrapper {
  @apply overflow-x-auto;
}

.mobile-table {
  font-size: 14px;
}

.mobile-table :deep(.el-table__cell) {
  padding: 8px 4px;
}

.pagination-wrapper {
  @apply border-t border-gray-100 pt-4;
}

.empty-data {
  @apply text-center text-gray-500;
}

@media (max-width: 768px) {
  .data-table-container {
    padding: 0.5rem;
    margin: 0 -0.5rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .table-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-area {
    justify-content: center;
  }
}
</style>
