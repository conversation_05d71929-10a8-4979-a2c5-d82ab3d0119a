/**
 * 统一分页管理器
 * 提供智能的响应式分页功能，解决不同屏幕尺寸下的自适应问题
 */

class UnifiedPaginationManager {
    constructor() {
        this.breakpoints = {
            mobile: 768,
            small: 480,
            extraSmall: 375
        };
        
        this.initialized = false;
        this.resizeTimer = null;
        this.managedTables = new Set();
        
        this.init();
    }
    
    init() {
        if (this.initialized) return;
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
        
        this.initialized = true;
    }
    
    setup() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
        
        // 监听DataTables初始化事件
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $(document).on('init.dt', (e, settings) => {
                this.enhanceTable(settings.nTable);
            });
            
            $(document).on('draw.dt', (e, settings) => {
                this.optimizePagination(settings.nTable);
            });
        }
        
        console.log('统一分页管理器初始化完成');
    }
    
    /**
     * 增强表格分页功能
     */
    enhanceTable(tableElement) {
        if (!tableElement || this.managedTables.has(tableElement)) return;
        
        try {
            const dataTable = $(tableElement).DataTable();
            this.managedTables.add(tableElement);
            
            // 立即优化分页
            this.optimizePagination(tableElement);
            
            // 添加触摸支持
            this.addTouchSupport(tableElement);
            
            console.log('表格分页增强完成:', tableElement.id || 'unnamed');
        } catch (error) {
            console.warn('增强表格分页失败:', error);
        }
    }
    
    /**
     * 优化分页显示
     */
    optimizePagination(tableElement) {
        if (!tableElement) return;
        
        try {
            const dataTable = $(tableElement).DataTable();
            const api = dataTable.api();
            const info = api.page.info();
            
            // 如果只有一页，不需要优化
            if (info.pages <= 1) return;
            
            const container = $(tableElement).closest('.dataTables_wrapper');
            const paginateContainer = container.find('.dataTables_paginate');
            
            if (paginateContainer.length === 0) return;
            
            const screenWidth = window.innerWidth;
            
            // 移除之前的优化类
            paginateContainer.removeClass('mobile-smart-hide ultra-compact');
            paginateContainer.find('.current-page-indicator, .ellipsis-indicator').remove();
            paginateContainer.find('.paginate_button').removeClass('mobile-hidden').show();
            
            if (screenWidth <= this.breakpoints.extraSmall) {
                // 超小屏幕：只显示前后页和当前页指示器
                this.applyUltraCompactMode(paginateContainer, info);
            } else if (screenWidth <= this.breakpoints.small) {
                // 小屏幕：智能隐藏页码
                this.applySmartHideMode(paginateContainer, info);
            } else if (screenWidth <= this.breakpoints.mobile) {
                // 移动端：紧凑模式
                this.applyMobileMode(paginateContainer, info);
            } else {
                // 桌面端：标准模式
                this.applyDesktopMode(paginateContainer);
            }
            
        } catch (error) {
            console.warn('优化分页显示失败:', error);
        }
    }
    
    /**
     * 超小屏幕模式：只显示前后页和页码指示器
     */
    applyUltraCompactMode(container, info) {
        container.addClass('ultra-compact');
        
        // 隐藏所有页码按钮，只保留前后页
        container.find('.paginate_button:not(.previous):not(.next)').addClass('mobile-hidden');
        
        // 添加当前页指示器
        const currentPageIndicator = $(`
            <span class="current-page-indicator">
                ${info.page + 1} / ${info.pages}
            </span>
        `);
        
        const nextButton = container.find('.paginate_button.next');
        if (nextButton.length > 0) {
            currentPageIndicator.insertBefore(nextButton);
        } else {
            container.append(currentPageIndicator);
        }
    }
    
    /**
     * 智能隐藏模式：显示关键页码
     */
    applySmartHideMode(container, info) {
        container.addClass('mobile-smart-hide');
        
        const currentPage = info.page + 1;
        const totalPages = info.pages;
        
        container.find('.paginate_button').each(function() {
            const $btn = $(this);
            const btnText = $btn.text().trim();
            
            // 保留前后页按钮
            if ($btn.hasClass('previous') || $btn.hasClass('next')) {
                return;
            }
            
            // 保留当前页
            if ($btn.hasClass('current')) {
                return;
            }
            
            // 保留首页和末页
            if (btnText === '1' || btnText === totalPages.toString()) {
                return;
            }
            
            // 保留当前页附近的页码
            const pageNum = parseInt(btnText);
            if (!isNaN(pageNum) && Math.abs(pageNum - currentPage) <= 1) {
                return;
            }
            
            // 隐藏其他页码
            $btn.addClass('mobile-hidden');
        });
    }
    
    /**
     * 移动端模式：紧凑布局
     */
    applyMobileMode(container, info) {
        // 限制显示的页码数量
        const maxVisiblePages = 5;
        const currentPage = info.page + 1;
        const totalPages = info.pages;
        
        if (totalPages > maxVisiblePages) {
            container.find('.paginate_button').each(function() {
                const $btn = $(this);
                const btnText = $btn.text().trim();
                
                if ($btn.hasClass('previous') || $btn.hasClass('next') || $btn.hasClass('current')) {
                    return;
                }
                
                const pageNum = parseInt(btnText);
                if (!isNaN(pageNum)) {
                    const distance = Math.abs(pageNum - currentPage);
                    if (distance > 2) {
                        $btn.addClass('mobile-hidden');
                    }
                }
            });
        }
    }
    
    /**
     * 桌面端模式：标准显示
     */
    applyDesktopMode(container) {
        // 移除所有移动端优化类
        container.removeClass('mobile-smart-hide ultra-compact');
        container.find('.paginate_button').removeClass('mobile-hidden').show();
        container.find('.current-page-indicator, .ellipsis-indicator').remove();
    }
    
    /**
     * 添加触摸支持
     */
    addTouchSupport(tableElement) {
        const container = $(tableElement).closest('.dataTables_wrapper');
        const paginateContainer = container.find('.dataTables_paginate');
        
        if (paginateContainer.length === 0 || paginateContainer.data('touch-enabled')) return;
        
        let touchStartX = 0;
        let touchEndX = 0;
        
        paginateContainer[0].addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });
        
        paginateContainer[0].addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            this.handleSwipe(tableElement, touchStartX, touchEndX);
        }, { passive: true });
        
        paginateContainer.data('touch-enabled', true);
    }
    
    /**
     * 处理滑动手势
     */
    handleSwipe(tableElement, startX, endX) {
        const minSwipeDistance = 80;
        const swipeDistance = endX - startX;
        
        if (Math.abs(swipeDistance) < minSwipeDistance) return;
        
        try {
            const dataTable = $(tableElement).DataTable();
            const api = dataTable.api();
            const info = api.page.info();
            
            if (swipeDistance > 0 && info.page > 0) {
                // 向右滑动：上一页
                api.page('previous').draw('page');
            } else if (swipeDistance < 0 && info.page < info.pages - 1) {
                // 向左滑动：下一页
                api.page('next').draw('page');
            }
        } catch (error) {
            console.warn('处理滑动手势失败:', error);
        }
    }
    
    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 使用防抖避免频繁触发
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            this.managedTables.forEach(table => {
                this.optimizePagination(table);
            });
        }, 150);
    }
    
    /**
     * 手动优化指定表格
     */
    optimizeTable(tableSelector) {
        const tableElement = typeof tableSelector === 'string' 
            ? document.querySelector(tableSelector) 
            : tableSelector;
            
        if (tableElement) {
            this.enhanceTable(tableElement);
        }
    }
    
    /**
     * 获取当前屏幕类型
     */
    getScreenType() {
        const width = window.innerWidth;
        if (width <= this.breakpoints.extraSmall) return 'extraSmall';
        if (width <= this.breakpoints.small) return 'small';
        if (width <= this.breakpoints.mobile) return 'mobile';
        return 'desktop';
    }
    
    /**
     * 销毁管理器
     */
    destroy() {
        this.managedTables.clear();
        clearTimeout(this.resizeTimer);
        window.removeEventListener('resize', this.handleResize);
        
        if (typeof $ !== 'undefined') {
            $(document).off('init.dt draw.dt');
        }
        
        this.initialized = false;
    }
}

// 创建全局实例
window.UnifiedPaginationManager = UnifiedPaginationManager;

// 自动初始化
if (typeof window !== 'undefined') {
    window.paginationManager = new UnifiedPaginationManager();
}

// 提供便捷的全局函数
window.optimizeTablePagination = function(tableSelector) {
    if (window.paginationManager) {
        window.paginationManager.optimizeTable(tableSelector);
    }
};

/**
 * 专门为汇总页面优化的分页配置
 * 针对窄容器（col-md-6）优化布局，让分页和信息文本分别独占一行
 */
window.getSummaryPagePaginationConfig = function(isMobile = false, isNarrowContainer = true) {
    const baseConfig = {
        responsive: {
            details: {
                type: 'column',
                target: 0
            }
        },
        columnDefs: [
            {
                className: 'dtr-control',
                orderable: false,
                targets: 0,
                width: "40px"
            }
        ],
        autoWidth: false,
        paging: true,
        order: [],
        language: {
            search: "搜索:",
            searchPlaceholder: "输入关键词筛选",
            lengthMenu: "显示 _MENU_ 条记录",
            zeroRecords: "无匹配数据",
            info: "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",
            infoEmpty: "显示第 0 至 0 条记录，共 0 条",
            infoFiltered: "(从 _MAX_ 条记录中筛选)",
            emptyTable: "表中数据为空",
            paginate: {
                first: "首页",
                previous: "上页",
                next: "下页",
                last: "末页"
            }
        }
    };

    // 根据容器类型和屏幕尺寸选择最佳DOM布局
    let domLayout;

    if (isMobile) {
        // 移动端：所有元素都独占一行
        domLayout = '<"row"<"col-12"f>>rt<"row"<"col-12 text-center"p>><"row"<"col-12 text-center"i>>';
    } else if (isNarrowContainer) {
        // 桌面端窄容器（如汇总页面的col-md-6）：分页和信息独占一行，长度选择器和搜索框共享一行
        domLayout = '<"row"<"col-sm-6"l><"col-sm-6"f>>rt<"row"<"col-12 text-center"p>><"row"<"col-12 text-center"i>>';
    } else {
        // 桌面端宽容器：标准布局
        domLayout = '<"row"<"col-sm-6"l><"col-sm-6"f>>rt<"row"<"col-sm-6"i><"col-sm-6"p>>';
    }

    return {
        ...baseConfig,
        pageLength: isMobile ? 5 : 10,
        lengthMenu: [[5, 10, 25, -1], [5, 10, 25, "全部"]],
        dom: domLayout,
        drawCallback: function() {
            // 绘制完成后优化分页
            if (window.paginationManager) {
                setTimeout(() => {
                    window.paginationManager.optimizePagination(this.api().table().node());

                    // 如果是窄容器，自动启用紧凑模式
                    if (isNarrowContainer) {
                        window.enableCompactPagination(this.api().table().node());
                    }
                }, 50);
            }
        }
    };
};

/**
 * 启用紧凑模式 - 极致空间利用
 */
window.enableCompactPagination = function(tableSelector) {
    const tableElement = typeof tableSelector === 'string'
        ? document.querySelector(tableSelector)
        : tableSelector;

    if (tableElement) {
        const wrapper = tableElement.closest('.dataTables_wrapper');
        if (wrapper) {
            wrapper.classList.add('compact-mode');
            console.log('已启用紧凑分页模式');
        }
    }
};

/**
 * 禁用紧凑模式
 */
window.disableCompactPagination = function(tableSelector) {
    const tableElement = typeof tableSelector === 'string'
        ? document.querySelector(tableSelector)
        : tableSelector;

    if (tableElement) {
        const wrapper = tableElement.closest('.dataTables_wrapper');
        if (wrapper) {
            wrapper.classList.remove('compact-mode');
            console.log('已禁用紧凑分页模式');
        }
    }
};

/**
 * 兼容性函数：支持旧的分页优化调用
 */
window.optimizePagination = function(dataTable, isSmallScreen) {
    if (window.paginationManager && dataTable) {
        const tableElement = dataTable.table ? dataTable.table().node() : dataTable;
        window.paginationManager.optimizePagination(tableElement);
    }
};

console.log('统一分页管理器加载完成');
