#!/bin/bash

# HDSC查询系统快速管理命令
# 使用方法: ./hdsc [命令]

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

show_help() {
    echo "HDSC查询系统 Docker管理命令"
    echo ""
    echo "使用方法: ./hdsc [命令]"
    echo ""
    echo "可用命令:"
    echo "  status    - 查看容器状态"
    echo "  start     - 启动服务"
    echo "  stop      - 停止服务"
    echo "  restart   - 重启服务"
    echo "  logs      - 查看实时日志"
    echo "  shell     - 进入容器"
    echo "  check     - 健康检查"
    echo "  stats     - 查看资源使用"
    echo "  backup    - 备份数据"
    echo "  manage    - 打开管理界面"
    echo "  help      - 显示此帮助"
    echo ""
    echo "示例:"
    echo "  ./hdsc status    # 查看状态"
    echo "  ./hdsc logs      # 查看日志"
    echo "  ./hdsc restart   # 重启服务"
}

case "${1:-help}" in
    "status")
        log_info "查看容器状态..."
        sudo docker-compose ps
        echo ""
        if sudo docker ps | grep -q "hdsc-query-app"; then
            log_info "服务地址: http://localhost:5000"
        fi
        ;;
    
    "start")
        log_info "启动服务..."
        sudo docker-compose up -d
        if [ $? -eq 0 ]; then
            log_info "服务启动成功，访问地址: http://localhost:5000"
        fi
        ;;
    
    "stop")
        log_info "停止服务..."
        sudo docker-compose down
        ;;
    
    "restart")
        log_info "重启服务..."
        sudo docker-compose restart
        if [ $? -eq 0 ]; then
            log_info "服务重启成功，访问地址: http://localhost:5000"
        fi
        ;;
    
    "logs")
        log_info "显示实时日志 (按Ctrl+C退出)..."
        sudo docker-compose logs -f hdsc-query-app
        ;;
    
    "shell")
        log_info "进入容器 (输入exit退出)..."
        sudo docker exec -it hdsc-query-app bash
        ;;
    
    "check")
        log_info "执行健康检查..."
        if sudo docker ps | grep -q "hdsc-query-app"; then
            container_status=$(sudo docker inspect --format='{{.State.Status}}' hdsc-query-app 2>/dev/null)
            container_health=$(sudo docker inspect --format='{{.State.Health.Status}}' hdsc-query-app 2>/dev/null || echo "none")
            log_info "容器状态: $container_status"
            log_info "健康状态: $container_health"
            
            # 检查端口
            if curl -f http://localhost:5000/ >/dev/null 2>&1; then
                log_info "应用响应正常: http://localhost:5000"
            else
                log_warn "应用无响应，请检查日志"
            fi
        else
            log_error "容器未运行"
        fi
        ;;
    
    "stats")
        log_info "资源使用情况:"
        sudo docker stats --no-stream hdsc-query-app 2>/dev/null || log_warn "容器未运行"
        ;;
    
    "backup")
        backup_name="hdsc-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
        log_info "创建备份: $backup_name"
        tar -czf "$backup_name" logs cache config.py docker-compose.yml 2>/dev/null || true
        if [ -f "$backup_name" ]; then
            log_info "备份完成: $backup_name"
        fi
        ;;
    
    "manage")
        log_info "Docker管理功能..."
        log_info "可用操作:"
        echo "  - 查看状态: ./hdsc status"
        echo "  - 查看日志: ./hdsc logs"
        echo "  - 重启服务: ./hdsc restart"
        echo "  - 进入容器: ./hdsc shell"
        echo "  - 健康检查: ./hdsc check"
        ;;
    
    "help"|*)
        show_help
        ;;
esac
