import { http, downloadFile } from '@/utils/request'
import type { 
  QueryParams, 
  OrderData, 
  CustomerSummary, 
  SummaryStats,
  ChartData,
  ExportConfig
} from '@/types/data'

export const dataApi = {
  // 获取订单数据
  getOrders(params?: QueryParams) {
    return http.get('/data/orders', params)
  },

  // 获取逾期订单
  getOverdueOrders(params?: QueryParams) {
    return http.get('/data/overdue-orders', params)
  },

  // 获取客户汇总
  getCustomerSummary(customerName: string) {
    return http.get(`/data/customer-summary/${encodeURIComponent(customerName)}`)
  },

  // 获取汇总统计
  getSummaryStats(params?: QueryParams) {
    return http.get('/data/summary-stats', params)
  },

  // 获取图表数据
  getChartData(type: 'orders' | 'overdue' | 'finance', params?: any) {
    return http.get(`/data/chart/${type}`, params)
  },

  // 搜索客户
  searchCustomers(keyword: string) {
    return http.get('/data/search/customers', { keyword })
  },

  // 搜索订单
  searchOrders(keyword: string) {
    return http.get('/data/search/orders', { keyword })
  },

  // 获取客户列表
  getCustomers(params?: QueryParams) {
    return http.get('/data/customers', params)
  },

  // 获取订单详情
  getOrderDetail(orderNumber: string) {
    return http.get(`/data/order/${encodeURIComponent(orderNumber)}`)
  },

  // 获取财务流水
  getFinanceRecords(params?: QueryParams) {
    return http.get('/data/finance-records', params)
  },

  // 导出数据
  exportData(config: ExportConfig) {
    const { format, filename, ...params } = config
    return downloadFile(
      `/export/${format}`,
      filename,
      params
    )
  },

  // 导出客户汇总
  exportCustomerSummary(customerName: string, format: 'excel' | 'pdf') {
    return downloadFile(
      `/export/customer-summary/${format}`,
      `客户汇总-${customerName}.${format === 'excel' ? 'xlsx' : 'pdf'}`,
      { customerName }
    )
  },

  // 导出图表
  exportChart(type: string, format: 'png' | 'pdf') {
    return downloadFile(
      `/export/chart/${type}`,
      `图表-${type}.${format}`,
      { format }
    )
  },

  // 批量导出
  batchExport(config: {
    type: 'orders' | 'customers' | 'finance'
    format: 'excel' | 'csv'
    filters?: QueryParams
  }) {
    return http.post('/export/batch', config)
  },

  // 获取数据统计
  getDataStats() {
    return http.get('/data/stats')
  },

  // 获取最近更新时间
  getLastUpdateTime() {
    return http.get('/data/last-update')
  },

  // 刷新数据缓存
  refreshCache() {
    return http.post('/data/refresh-cache')
  }
}
