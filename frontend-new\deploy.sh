#!/bin/bash

# 太享查询系统前端部署脚本
# 使用方法: ./deploy.sh [环境] [版本]
# 示例: ./deploy.sh production v2.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PROJECT_NAME="hdsc-query-frontend"
DOCKER_REGISTRY="your-registry.com"
BACKEND_HOST="localhost:5000"

# 参数
ENVIRONMENT=${1:-development}
VERSION=${2:-latest}
BUILD_TIME=$(date '+%Y%m%d%H%M%S')
IMAGE_TAG="${DOCKER_REGISTRY}/${PROJECT_NAME}:${VERSION}"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}太享查询系统前端部署脚本${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "环境: ${GREEN}${ENVIRONMENT}${NC}"
echo -e "版本: ${GREEN}${VERSION}${NC}"
echo -e "构建时间: ${GREEN}${BUILD_TIME}${NC}"
echo ""

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查依赖...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}错误: Node.js 未安装${NC}"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误: npm 未安装${NC}"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}错误: Docker 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查通过${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${YELLOW}安装依赖...${NC}"
    npm ci
    echo -e "${GREEN}✓ 依赖安装完成${NC}"
}

# 代码检查
lint_code() {
    echo -e "${YELLOW}代码检查...${NC}"
    npm run lint
    echo -e "${GREEN}✓ 代码检查通过${NC}"
}

# 类型检查
type_check() {
    echo -e "${YELLOW}类型检查...${NC}"
    npm run type-check
    echo -e "${GREEN}✓ 类型检查通过${NC}"
}

# 构建应用
build_app() {
    echo -e "${YELLOW}构建应用...${NC}"
    
    # 设置环境变量
    export NODE_ENV=production
    export VITE_APP_VERSION=${VERSION}
    export VITE_BUILD_TIME=${BUILD_TIME}
    
    if [ "$ENVIRONMENT" = "production" ]; then
        export VITE_API_BASE_URL="/api"
    else
        export VITE_API_BASE_URL="http://${BACKEND_HOST}/api"
    fi
    
    npm run build
    echo -e "${GREEN}✓ 应用构建完成${NC}"
}

# 构建Docker镜像
build_docker_image() {
    echo -e "${YELLOW}构建Docker镜像...${NC}"
    
    docker build \
        --tag ${IMAGE_TAG} \
        --tag ${DOCKER_REGISTRY}/${PROJECT_NAME}:latest \
        --build-arg VERSION=${VERSION} \
        --build-arg BUILD_TIME=${BUILD_TIME} \
        .
    
    echo -e "${GREEN}✓ Docker镜像构建完成${NC}"
}

# 推送镜像
push_image() {
    if [ "$ENVIRONMENT" = "production" ]; then
        echo -e "${YELLOW}推送镜像到仓库...${NC}"
        docker push ${IMAGE_TAG}
        docker push ${DOCKER_REGISTRY}/${PROJECT_NAME}:latest
        echo -e "${GREEN}✓ 镜像推送完成${NC}"
    fi
}

# 部署到本地
deploy_local() {
    echo -e "${YELLOW}部署到本地...${NC}"
    
    # 停止现有容器
    docker stop ${PROJECT_NAME} 2>/dev/null || true
    docker rm ${PROJECT_NAME} 2>/dev/null || true
    
    # 启动新容器
    docker run -d \
        --name ${PROJECT_NAME} \
        --restart unless-stopped \
        -p 3000:80 \
        ${IMAGE_TAG}
    
    echo -e "${GREEN}✓ 本地部署完成${NC}"
    echo -e "访问地址: ${BLUE}http://localhost:3000${NC}"
}

# 健康检查
health_check() {
    echo -e "${YELLOW}健康检查...${NC}"
    
    for i in {1..30}; do
        if curl -f http://localhost:3000/health &>/dev/null; then
            echo -e "${GREEN}✓ 应用启动成功${NC}"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    
    echo -e "${RED}✗ 应用启动失败${NC}"
    exit 1
}

# 清理
cleanup() {
    echo -e "${YELLOW}清理临时文件...${NC}"
    # 清理构建缓存等
    echo -e "${GREEN}✓ 清理完成${NC}"
}

# 主流程
main() {
    check_dependencies
    install_dependencies
    lint_code
    type_check
    build_app
    build_docker_image
    
    if [ "$ENVIRONMENT" = "production" ]; then
        push_image
    else
        deploy_local
        health_check
    fi
    
    cleanup
    
    echo ""
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}部署完成！${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo -e "环境: ${ENVIRONMENT}"
    echo -e "版本: ${VERSION}"
    echo -e "镜像: ${IMAGE_TAG}"
    
    if [ "$ENVIRONMENT" != "production" ]; then
        echo -e "访问地址: http://localhost:3000"
    fi
}

# 错误处理
trap 'echo -e "${RED}部署失败！${NC}"; exit 1' ERR

# 执行主流程
main
