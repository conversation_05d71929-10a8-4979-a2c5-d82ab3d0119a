/**
 * Performance Optimizer - 性能优化工具
 * 企业级性能优化解决方案
 */

class PerformanceOptimizer {
    constructor(options = {}) {
        this.config = {
            enableLazyLoading: true,
            enableCaching: true,
            enableErrorBoundary: true,
            cacheTimeout: 5 * 60 * 1000, // 5分钟
            retryAttempts: 3,
            retryDelay: 1000,
            ...options
        };
        
        this.cache = new Map();
        this.observers = new Map();
        this.errorBoundaries = new Set();
        this.performanceMetrics = {
            apiCalls: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errors: 0,
            startTime: Date.now()
        };
        
        this.init();
    }

    init() {
        this.setupErrorBoundary();
        this.setupLazyLoading();
        this.preloadCriticalResources();
        this.optimizeDOM();
    }

    // 错误边界设置
    setupErrorBoundary() {
        if (!this.config.enableErrorBoundary) return;
        
        window.addEventListener('error', this.handleError.bind(this));
        window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
    }

    handleError(event) {
        this.performanceMetrics.errors++;
        console.error('Performance Optimizer caught error:', event.error);
        
        // 记录错误信息
        this.logError({
            type: 'javascript_error',
            message: event.error?.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack,
            timestamp: Date.now()
        });
        
        // 尝试恢复
        this.attemptRecovery(event);
    }

    handlePromiseRejection(event) {
        this.performanceMetrics.errors++;
        console.error('Unhandled promise rejection:', event.reason);
        
        this.logError({
            type: 'promise_rejection',
            reason: event.reason,
            timestamp: Date.now()
        });
    }

    attemptRecovery(errorEvent) {
        // 基本恢复策略
        try {
            // 清理可能损坏的DOM状态
            this.cleanupCorruptedState();
            
            // 重新初始化关键功能
            this.reinitializeCriticalFeatures();
        } catch (recoveryError) {
            console.error('Recovery failed:', recoveryError);
        }
    }

    // 懒加载设置
    setupLazyLoading() {
        if (!this.config.enableLazyLoading) return;
        
        // 创建Intersection Observer用于懒加载
        this.lazyLoadObserver = new IntersectionObserver(
            this.handleLazyLoad.bind(this),
            {
                root: null,
                rootMargin: '50px',
                threshold: 0.1
            }
        );
        
        // 观察所有懒加载元素
        this.observeLazyElements();
    }

    observeLazyElements() {
        // 图片懒加载
        document.querySelectorAll('img[data-src], img[loading="lazy"]').forEach(img => {
            this.lazyLoadObserver.observe(img);
        });
        
        // 组件懒加载
        document.querySelectorAll('[data-lazy-component]').forEach(element => {
            this.lazyLoadObserver.observe(element);
        });
        
        // 内容懒加载
        document.querySelectorAll('[data-lazy-content]').forEach(element => {
            this.lazyLoadObserver.observe(element);
        });
    }

    handleLazyLoad(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                
                if (element.tagName === 'IMG') {
                    this.loadLazyImage(element);
                } else if (element.dataset.lazyComponent) {
                    this.loadLazyComponent(element);
                } else if (element.dataset.lazyContent) {
                    this.loadLazyContent(element);
                }
                
                this.lazyLoadObserver.unobserve(element);
            }
        });
    }

    loadLazyImage(img) {
        const src = img.dataset.src || img.src;
        if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        }
    }

    async loadLazyComponent(element) {
        const componentName = element.dataset.lazyComponent;
        try {
            // 动态加载组件
            const component = await this.loadComponent(componentName);
            if (component) {
                component.init(element);
                element.classList.add('component-loaded');
            }
        } catch (error) {
            console.error(`Failed to load component ${componentName}:`, error);
            element.classList.add('component-error');
        }
    }

    async loadLazyContent(element) {
        const contentUrl = element.dataset.lazyContent;
        try {
            const content = await this.fetchWithCache(contentUrl);
            element.innerHTML = content;
            element.classList.add('content-loaded');
        } catch (error) {
            console.error(`Failed to load content from ${contentUrl}:`, error);
            element.innerHTML = '<div class="alert alert-warning">内容加载失败</div>';
        }
    }

    // 缓存管理
    async fetchWithCache(url, options = {}) {
        if (!this.config.enableCaching) {
            return this.fetchWithRetry(url, options);
        }
        
        const cacheKey = this.generateCacheKey(url, options);
        const cached = this.cache.get(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < this.config.cacheTimeout) {
            this.performanceMetrics.cacheHits++;
            return cached.data;
        }
        
        this.performanceMetrics.cacheMisses++;
        this.performanceMetrics.apiCalls++;
        
        try {
            const data = await this.fetchWithRetry(url, options);
            
            // 存储到缓存
            this.cache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });
            
            return data;
        } catch (error) {
            // 如果有过期的缓存，在错误时返回
            if (cached) {
                console.warn('Using stale cache due to fetch error:', error);
                return cached.data;
            }
            throw error;
        }
    }

    async fetchWithRetry(url, options = {}) {
        let lastError;
        
        for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, options);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                lastError = error;
                
                if (attempt < this.config.retryAttempts - 1) {
                    await this.delay(this.config.retryDelay * Math.pow(2, attempt));
                }
            }
        }
        
        throw lastError;
    }

    // 预加载关键资源
    preloadCriticalResources() {
        // 预加载关键CSS
        this.preloadCSS([
            '/static/css/pages/home.css',
            '/static/css/components/data-table.css'
        ]);
        
        // 预加载关键JS
        this.preloadJS([
            '/static/js/components/calculator.js',
            '/static/js/components/todo.js'
        ]);
        
        // 预取下一页面可能需要的资源
        this.prefetchNextPageResources();
    }

    preloadCSS(urls) {
        urls.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = url;
            link.onload = () => {
                link.rel = 'stylesheet';
            };
            document.head.appendChild(link);
        });
    }

    preloadJS(urls) {
        urls.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'script';
            link.href = url;
            document.head.appendChild(link);
        });
    }

    prefetchNextPageResources() {
        // 根据用户行为预测可能访问的页面
        const likelyNextPages = this.predictNextPages();
        
        likelyNextPages.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url;
            document.head.appendChild(link);
        });
    }

    predictNextPages() {
        // 基于当前页面和用户行为预测
        const currentPath = window.location.pathname;
        const predictions = [];
        
        if (currentPath === '/' || currentPath === '/home') {
            predictions.push('/customer_summary', '/overdue', '/summary');
        }
        
        return predictions;
    }

    // DOM优化
    optimizeDOM() {
        // 虚拟滚动优化
        this.setupVirtualScrolling();
        
        // 事件委托优化
        this.setupEventDelegation();
        
        // 防抖和节流
        this.setupDebounceAndThrottle();
    }

    setupVirtualScrolling() {
        const longLists = document.querySelectorAll('[data-virtual-scroll]');
        longLists.forEach(list => {
            new VirtualScrollOptimizer(list);
        });
    }

    setupEventDelegation() {
        // 全局事件委托
        document.addEventListener('click', this.handleDelegatedClick.bind(this));
        document.addEventListener('input', this.throttle(this.handleDelegatedInput.bind(this), 300));
    }

    handleDelegatedClick(event) {
        const target = event.target;
        
        // 处理按钮点击
        if (target.matches('[data-action]')) {
            this.handleActionClick(target, event);
        }
        
        // 处理链接点击
        if (target.matches('[data-navigate]')) {
            this.handleNavigationClick(target, event);
        }
    }

    handleDelegatedInput(event) {
        const target = event.target;
        
        // 处理搜索输入
        if (target.matches('[data-search]')) {
            this.handleSearchInput(target, event);
        }
    }

    // 工具方法
    generateCacheKey(url, options) {
        return `${url}_${JSON.stringify(options)}`;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 性能监控
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            uptime: Date.now() - this.performanceMetrics.startTime,
            cacheHitRate: this.performanceMetrics.cacheHits / 
                         (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) || 0,
            memoryUsage: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : null
        };
    }

    logError(errorInfo) {
        // 发送错误日志到服务器
        if (typeof window.errorLogger !== 'undefined') {
            window.errorLogger.log(errorInfo);
        } else {
            console.error('Error logged:', errorInfo);
        }
    }

    // 清理方法
    cleanupCorruptedState() {
        // 清理可能损坏的状态
        this.cache.clear();
        
        // 移除错误的DOM元素
        document.querySelectorAll('.error-state, .loading-failed').forEach(el => {
            el.remove();
        });
    }

    reinitializeCriticalFeatures() {
        // 重新初始化关键功能
        if (window.homePageController) {
            try {
                window.homePageController.init();
            } catch (error) {
                console.error('Failed to reinitialize home controller:', error);
            }
        }
    }

    setupDebounceAndThrottle() {
        // 为常用操作添加防抖和节流
        const searchInputs = document.querySelectorAll('input[type="search"], input[data-search]');
        searchInputs.forEach(input => {
            const debouncedHandler = this.debounce((e) => {
                // 处理搜索输入
                this.handleSearchInput(e.target, e);
            }, 300);
            
            input.addEventListener('input', debouncedHandler);
        });
        
        // 滚动事件节流
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, 100));
        
        // 窗口大小调整节流
        window.addEventListener('resize', this.throttle(() => {
            this.handleResize();
        }, 250));
    }

    handleScroll() {
        // 处理滚动优化
        this.updateViewportElements();
    }

    handleResize() {
        // 处理窗口大小调整
        this.recalculateLayout();
    }

    updateViewportElements() {
        // 更新视口内的元素
    }

    recalculateLayout() {
        // 重新计算布局
    }

    // 销毁方法
    destroy() {
        // 清理观察器
        if (this.lazyLoadObserver) {
            this.lazyLoadObserver.disconnect();
        }
        
        // 清理缓存
        this.cache.clear();
        
        // 清理事件监听器
        window.removeEventListener('error', this.handleError);
        window.removeEventListener('unhandledrejection', this.handlePromiseRejection);
    }
}

// 虚拟滚动优化器
class VirtualScrollOptimizer {
    constructor(container) {
        this.container = container;
        this.items = [];
        this.visibleStart = 0;
        this.visibleEnd = 0;
        this.itemHeight = 50; // 默认项目高度
        
        this.init();
    }

    init() {
        this.measureItemHeight();
        this.setupScrolling();
    }

    measureItemHeight() {
        const firstItem = this.container.querySelector('[data-item]');
        if (firstItem) {
            this.itemHeight = firstItem.offsetHeight;
        }
    }

    setupScrolling() {
        this.container.addEventListener('scroll', this.throttle(() => {
            this.updateVisibleItems();
        }, 16)); // 60fps
    }

    updateVisibleItems() {
        const scrollTop = this.container.scrollTop;
        const containerHeight = this.container.clientHeight;
        
        this.visibleStart = Math.floor(scrollTop / this.itemHeight);
        this.visibleEnd = Math.min(
            this.visibleStart + Math.ceil(containerHeight / this.itemHeight) + 1,
            this.items.length
        );
        
        this.renderVisibleItems();
    }

    renderVisibleItems() {
        // 只渲染可见的项目
        const visibleItems = this.items.slice(this.visibleStart, this.visibleEnd);
        
        // 更新容器内容
        this.container.innerHTML = visibleItems.map(item => this.renderItem(item)).join('');
        
        // 设置滚动偏移
        this.container.style.paddingTop = `${this.visibleStart * this.itemHeight}px`;
        this.container.style.paddingBottom = `${(this.items.length - this.visibleEnd) * this.itemHeight}px`;
    }

    renderItem(item) {
        return `<div class="virtual-item" data-item="${item.id}">${item.content}</div>`;
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// 导出模块
window.PerformanceOptimizer = PerformanceOptimizer;
window.VirtualScrollOptimizer = VirtualScrollOptimizer;