<template>
  <div class="search-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      :label-position="labelPosition"
      class="search-form-content"
    >
      <el-row :gutter="16">
        <!-- 动态表单字段 -->
        <el-col
          v-for="field in fields"
          :key="field.key"
          :xs="24"
          :sm="field.colSpan?.sm || 12"
          :md="field.colSpan?.md || 8"
          :lg="field.colSpan?.lg || 6"
          :xl="field.colSpan?.xl || 6"
        >
          <el-form-item
            :label="field.label"
            :prop="field.key"
            :required="field.required"
          >
            <!-- 文本输入 -->
            <el-input
              v-if="field.type === 'text'"
              v-model="formData[field.key]"
              :placeholder="field.placeholder"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              @input="handleFieldChange(field.key, $event)"
            />
            
            <!-- 数字输入 -->
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="formData[field.key]"
              :placeholder="field.placeholder"
              :min="field.min"
              :max="field.max"
              :step="field.step"
              :precision="field.precision"
              :disabled="field.disabled"
              class="w-full"
              @change="handleFieldChange(field.key, $event)"
            />
            
            <!-- 日期选择 -->
            <el-date-picker
              v-else-if="field.type === 'date'"
              v-model="formData[field.key]"
              type="date"
              :placeholder="field.placeholder"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
              @change="handleFieldChange(field.key, $event)"
            />
            
            <!-- 日期范围选择 -->
            <el-date-picker
              v-else-if="field.type === 'daterange'"
              v-model="formData[field.key]"
              type="daterange"
              :range-separator="field.rangeSeparator || '至'"
              :start-placeholder="field.startPlaceholder || '开始日期'"
              :end-placeholder="field.endPlaceholder || '结束日期'"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
              @change="handleFieldChange(field.key, $event)"
            />
            
            <!-- 下拉选择 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="formData[field.key]"
              :placeholder="field.placeholder"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              :multiple="field.multiple"
              :filterable="field.filterable"
              class="w-full"
              @change="handleFieldChange(field.key, $event)"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              />
            </el-select>
            
            <!-- 级联选择 -->
            <el-cascader
              v-else-if="field.type === 'cascader'"
              v-model="formData[field.key]"
              :options="field.options"
              :placeholder="field.placeholder"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              :filterable="field.filterable"
              class="w-full"
              @change="handleFieldChange(field.key, $event)"
            />
            
            <!-- 开关 -->
            <el-switch
              v-else-if="field.type === 'switch'"
              v-model="formData[field.key]"
              :disabled="field.disabled"
              :active-text="field.activeText"
              :inactive-text="field.inactiveText"
              @change="handleFieldChange(field.key, $event)"
            />
            
            <!-- 单选框组 -->
            <el-radio-group
              v-else-if="field.type === 'radio'"
              v-model="formData[field.key]"
              :disabled="field.disabled"
              @change="handleFieldChange(field.key, $event)"
            >
              <el-radio
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
            
            <!-- 复选框组 -->
            <el-checkbox-group
              v-else-if="field.type === 'checkbox'"
              v-model="formData[field.key]"
              :disabled="field.disabled"
              @change="handleFieldChange(field.key, $event)"
            >
              <el-checkbox
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        
        <!-- 操作按钮 -->
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item class="search-actions">
            <div class="flex flex-wrap gap-2 justify-center sm:justify-start">
              <el-button
                type="primary"
                :icon="Search"
                :loading="loading"
                @click="handleSearch"
              >
                搜索
              </el-button>
              
              <el-button
                :icon="Refresh"
                @click="handleReset"
              >
                重置
              </el-button>
              
              <el-button
                v-if="showAdvanced"
                type="info"
                text
                @click="toggleAdvanced"
              >
                {{ isAdvancedVisible ? '收起' : '展开' }}
                <el-icon class="ml-1">
                  <component :is="isAdvancedVisible ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </el-button>
              
              <!-- 自定义操作按钮 -->
              <slot name="actions" :form-data="formData"></slot>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <!-- 快速筛选标签 -->
    <div v-if="quickFilters.length > 0" class="quick-filters mt-4">
      <div class="quick-filters-title text-sm text-gray-600 mb-2">快速筛选：</div>
      <div class="flex flex-wrap gap-2">
        <el-tag
          v-for="filter in quickFilters"
          :key="filter.value"
          :type="isQuickFilterActive(filter) ? 'primary' : 'info'"
          :effect="isQuickFilterActive(filter) ? 'dark' : 'plain'"
          class="cursor-pointer"
          @click="toggleQuickFilter(filter)"
        >
          {{ filter.label }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Search, Refresh, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import type { FormRules } from 'element-plus'
import type { SearchField, QuickFilter } from '@/types/data'
import { useResponsive } from '@/composables/useResponsive'

interface Props {
  fields: SearchField[]
  quickFilters?: QuickFilter[]
  loading?: boolean
  showAdvanced?: boolean
  defaultValues?: Record<string, any>
  rules?: FormRules
  labelWidth?: string
}

const props = withDefaults(defineProps<Props>(), {
  quickFilters: () => [],
  loading: false,
  showAdvanced: false,
  defaultValues: () => ({}),
  rules: () => ({}),
  labelWidth: '80px'
})

const emit = defineEmits<{
  search: [formData: Record<string, any>]
  reset: []
  change: [key: string, value: any, formData: Record<string, any>]
}>()

const { isMobile } = useResponsive()

// 响应式数据
const formRef = ref()
const formData = ref<Record<string, any>>({})
const isAdvancedVisible = ref(false)
const activeQuickFilters = ref<any[]>([])

// 计算属性
const labelPosition = computed(() => {
  return isMobile.value ? 'top' : 'right'
})

const formRules = computed(() => {
  const rules: FormRules = { ...props.rules }
  
  // 根据字段配置生成验证规则
  props.fields.forEach(field => {
    if (field.required) {
      rules[field.key] = [
        {
          required: true,
          message: `请输入${field.label}`,
          trigger: ['blur', 'change']
        }
      ]
    }
    
    if (field.rules) {
      rules[field.key] = [...(rules[field.key] || []), ...field.rules]
    }
  })
  
  return rules
})

// 方法
const initFormData = () => {
  const data: Record<string, any> = {}
  
  // 设置默认值
  props.fields.forEach(field => {
    if (props.defaultValues[field.key] !== undefined) {
      data[field.key] = props.defaultValues[field.key]
    } else if (field.defaultValue !== undefined) {
      data[field.key] = field.defaultValue
    } else {
      // 根据字段类型设置默认值
      switch (field.type) {
        case 'checkbox':
          data[field.key] = []
          break
        case 'number':
          data[field.key] = undefined
          break
        case 'switch':
          data[field.key] = false
          break
        default:
          data[field.key] = ''
      }
    }
  })
  
  formData.value = data
}

const handleSearch = async () => {
  try {
    await formRef.value?.validate()
    emit('search', { ...formData.value })
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  activeQuickFilters.value = []
  initFormData()
  emit('reset')
}

const handleFieldChange = (key: string, value: any) => {
  formData.value[key] = value
  emit('change', key, value, { ...formData.value })
}

const toggleAdvanced = () => {
  isAdvancedVisible.value = !isAdvancedVisible.value
}

const toggleQuickFilter = (filter: QuickFilter) => {
  const index = activeQuickFilters.value.findIndex(f => f.value === filter.value)
  
  if (index > -1) {
    activeQuickFilters.value.splice(index, 1)
  } else {
    activeQuickFilters.value.push(filter)
  }
  
  // 应用快速筛选到表单
  applyQuickFilters()
}

const isQuickFilterActive = (filter: QuickFilter) => {
  return activeQuickFilters.value.some(f => f.value === filter.value)
}

const applyQuickFilters = () => {
  activeQuickFilters.value.forEach(filter => {
    if (formData.value.hasOwnProperty(filter.field)) {
      formData.value[filter.field] = filter.value
    }
  })
  
  // 自动触发搜索
  nextTick(() => {
    handleSearch()
  })
}

// 监听默认值变化
watch(() => props.defaultValues, () => {
  initFormData()
}, { deep: true, immediate: true })

// 暴露方法
defineExpose({
  getFormData: () => formData.value,
  setFormData: (data: Record<string, any>) => {
    Object.assign(formData.value, data)
  },
  resetForm: handleReset,
  validateForm: () => formRef.value?.validate(),
  clearValidation: () => formRef.value?.clearValidate()
})
</script>

<style scoped>
.search-form {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4;
}

.search-actions {
  @apply mb-0;
}

.search-actions :deep(.el-form-item__content) {
  @apply justify-center sm:justify-start;
}

.quick-filters {
  @apply border-t border-gray-100 pt-4;
}

@media (max-width: 768px) {
  .search-form {
    margin: 0 -1rem 1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .search-form :deep(.el-form-item) {
    margin-bottom: 16px;
  }
  
  .search-form :deep(.el-form-item__label) {
    padding-bottom: 4px;
  }
}
</style>
