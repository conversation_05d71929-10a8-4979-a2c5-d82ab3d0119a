/**
 * 企业级表格管理器
 * 版本: 2.0
 * 统一管理所有数据表格的初始化和行为
 */

class EnterpriseTableManager {
    constructor() {
        this.tables = new Map();
        this.defaultConfig = {
            responsive: {
                details: {
                    type: 'column',
                    target: 0,
                    renderer: this.createCompactRenderer.bind(this)
                }
            },
            autoWidth: false,
            scrollX: true,
            scrollCollapse: true,
            processing: true,
            deferRender: true,
            language: {
                search: "搜索:",
                info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                infoFiltered: "(由 _MAX_ 项结果过滤)",
                paginate: {
                    first: "首页",
                    previous: "上页",
                    next: "下页",
                    last: "末页"
                },
                zeroRecords: "没有匹配结果",
                emptyTable: "暂无数据",
                loadingRecords: "加载中...",
                processing: "处理中..."
            }
        };
    }

    /**
     * 初始化表格
     * @param {string} tableId - 表格ID
     * @param {Object} options - 配置选项
     */
    initTable(tableId, options = {}) {
        const tableElement = document.getElementById(tableId);
        if (!tableElement) {
            console.warn(`表格 ${tableId} 不存在`);
            return null;
        }

        // 如果表格已经初始化，先销毁
        if ($.fn.DataTable.isDataTable(tableElement)) {
            $(tableElement).DataTable().destroy();
        }

        // 合并配置
        const config = this.mergeConfig(options);
        
        // 初始化DataTable
        const dataTable = $(tableElement).DataTable(config);
        
        // 存储表格实例
        this.tables.set(tableId, {
            instance: dataTable,
            element: tableElement,
            config: config
        });

        // 应用企业级样式增强
        this.applyEnterpriseStyles(tableElement);
        
        // 设置响应式调整
        this.setupResponsiveHandlers(tableId);
        
        console.log(`✅ 表格 ${tableId} 初始化完成`);
        return dataTable;
    }

    /**
     * 创建紧凑型响应式渲染器
     */
    createCompactRenderer(api, rowIdx, columns) {
        const data = $.map(columns, (col, i) => {
            if (col.hidden) {
                let fieldType = this.determineFieldType(col.title);
                
                return `<li data-dt-row="${col.rowIndex}" data-dt-column="${col.columnIndex}" data-field-type="${fieldType}">
                    <span class="dtr-title">${col.title}</span>
                    <span class="dtr-data">${col.data}</span>
                </li>`;
            }
            return '';
        }).join('');

        return data ? `<ul class="dtr-details-list">${data}</ul>` : false;
    }

    /**
     * 确定字段类型
     */
    determineFieldType(title) {
        if (title.includes('金额') || title.includes('待收') || title.includes('成本')) {
            return 'amount';
        } else if (title.includes('状态') || title.includes('逾期')) {
            return 'status';
        } else if (title.includes('日期') || title.includes('时间')) {
            return 'date';
        } else if (title.includes('手机') || title.includes('电话') || title.includes('姓名')) {
            return 'contact';
        }
        return 'default';
    }

    /**
     * 合并配置
     */
    mergeConfig(userConfig) {
        const config = { ...this.defaultConfig };
        
        // 合并列定义
        config.columnDefs = [
            {
                className: 'dtr-control',
                orderable: false,
                targets: 0,
                width: "30px"
            },
            {
                targets: '_all',
                className: 'dt-head-center dt-body-center',
                render: (data, type, row) => {
                    if (type === 'display') {
                        const content = data === null || data === undefined ? '-' : data;
                        return `<div class="text-nowrap">${content}</div>`;
                    }
                    return data;
                }
            },
            ...(userConfig.columnDefs || [])
        ];

        // 合并其他配置
        Object.assign(config, userConfig);
        
        return config;
    }

    /**
     * 应用企业级样式增强
     */
    applyEnterpriseStyles(tableElement) {
        // 添加企业级样式类
        tableElement.classList.add('enterprise-table');
        
        // 增强产品类型徽章
        setTimeout(() => {
            const badges = tableElement.querySelectorAll('.badge[data-product-type]');
            badges.forEach(badge => {
                const productType = badge.getAttribute('data-product-type');
                if (productType === '电商') {
                    badge.style.backgroundColor = '#cfe2ff';
                    badge.style.color = '#084298';
                    badge.style.borderColor = '#b6d4fe';
                } else if (productType === '租赁') {
                    badge.style.backgroundColor = '#d1e7dd';
                    badge.style.color = '#0f5132';
                    badge.style.borderColor = '#badbcc';
                }
            });
        }, 100);
    }

    /**
     * 设置响应式处理器
     */
    setupResponsiveHandlers(tableId) {
        const tableInfo = this.tables.get(tableId);
        if (!tableInfo) return;

        let resizeTimer;
        $(window).on('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                if ($.fn.DataTable.isDataTable(tableInfo.element)) {
                    tableInfo.instance.columns.adjust().responsive.recalc();
                }
            }, 250);
        });

        // 初始调整
        setTimeout(() => {
            if ($.fn.DataTable.isDataTable(tableInfo.element)) {
                tableInfo.instance.columns.adjust().responsive.recalc();
            }
        }, 300);
    }

    /**
     * 获取表格实例
     */
    getTable(tableId) {
        const tableInfo = this.tables.get(tableId);
        return tableInfo ? tableInfo.instance : null;
    }

    /**
     * 销毁表格
     */
    destroyTable(tableId) {
        const tableInfo = this.tables.get(tableId);
        if (tableInfo && $.fn.DataTable.isDataTable(tableInfo.element)) {
            tableInfo.instance.destroy();
            this.tables.delete(tableId);
            console.log(`🗑️ 表格 ${tableId} 已销毁`);
        }
    }

    /**
     * 刷新表格
     */
    refreshTable(tableId) {
        const tableInfo = this.tables.get(tableId);
        if (tableInfo) {
            tableInfo.instance.ajax.reload();
        }
    }

    /**
     * 切换紧凑模式
     */
    toggleCompactMode(enable = true) {
        document.body.classList.toggle('compact-mode-active', enable);
        
        // 重新计算所有表格
        this.tables.forEach((tableInfo, tableId) => {
            if ($.fn.DataTable.isDataTable(tableInfo.element)) {
                tableInfo.instance.columns.adjust().responsive.recalc();
            }
        });
        
        console.log(`📊 紧凑模式已${enable ? '启用' : '禁用'}`);
    }

    /**
     * 性能优化模式
     */
    enablePerformanceMode(enable = true) {
        document.body.classList.toggle('performance-mode', enable);
        console.log(`⚡ 性能模式已${enable ? '启用' : '禁用'}`);
    }

    /**
     * 导出表格数据
     */
    exportTableData(tableId, format = 'excel') {
        const tableInfo = this.tables.get(tableId);
        if (!tableInfo) {
            console.error(`表格 ${tableId} 不存在`);
            return;
        }

        // 这里可以集成导出功能
        console.log(`📤 导出表格 ${tableId} 为 ${format} 格式`);
    }

    /**
     * 获取表格统计信息
     */
    getTableStats(tableId) {
        const tableInfo = this.tables.get(tableId);
        if (!tableInfo) return null;

        const instance = tableInfo.instance;
        return {
            totalRecords: instance.data().length,
            visibleRecords: instance.rows({ filter: 'applied' }).data().length,
            columns: instance.columns().count(),
            responsive: instance.responsive.hasHidden()
        };
    }
}

// 创建全局实例
window.EnterpriseTableManager = new EnterpriseTableManager();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 企业级表格管理器已就绪');
    
    // 启用紧凑模式（默认）
    window.EnterpriseTableManager.toggleCompactMode(true);
});

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnterpriseTableManager;
}