/**
 * Dashboard风格的响应式折叠详情样式
 * 参考日期筛选页面的成功设计，简洁而舒适
 */

/* ==================== Dashboard风格的响应式详情样式 ==================== */

/* 响应式详情容器 - 简洁舒适的设计 */
.dtr-details {
    width: 100% !important;
    background: #ffffff !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    margin: 10px 0 !important;
    padding: 15px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    font-size: 0.9rem !important;
}

/* 详情内容区域 */
.dtr-details ul {
    display: block !important; /* 使用简单的块级布局，不是网格 */
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

/* 每个详情项 - 舒适的行间距 */
.dtr-details li {
    display: flex !important;
    align-items: flex-start !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid #f8f9fa !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

/* 最后一项不要下边框 */
.dtr-details li:last-child {
    border-bottom: none !important;
}

/* 字段标签 - 简洁明了 */
.dtr-title {
    font-weight: 600 !important;
    color: #495057 !important;
    min-width: 100px !important;
    max-width: 120px !important;
    margin-right: 15px !important;
    flex-shrink: 0 !important;
    font-size: 0.9rem !important;
}

/* 字段数据 - 清晰易读 */
.dtr-data {
    flex: 1 !important;
    color: #212529 !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
    word-break: break-word !important;
}

/* 徽章样式优化 */
.dtr-data .badge {
    font-size: 0.8rem !important;
    padding: 0.25em 0.5em !important;
    border-radius: 0.375rem !important;
}

/* 金额字段样式 */
.dtr-data .amount-field {
    font-weight: 600 !important;
    color: #0d6efd !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', monospace !important;
}

/* ==================== 移动设备优化 ==================== */
@media (max-width: 768px) {
    .dtr-details {
        margin: 8px 0 !important;
        padding: 12px !important;
        font-size: 0.85rem !important;
    }
    
    .dtr-details li {
        padding: 6px 0 !important;
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    
    .dtr-title {
        min-width: auto !important;
        max-width: none !important;
        margin-right: 0 !important;
        margin-bottom: 4px !important;
        font-size: 0.8rem !important;
        color: #6c757d !important;
    }
    
    .dtr-data {
        font-size: 0.85rem !important;
        margin-left: 0 !important;
    }
}

@media (max-width: 576px) {
    .dtr-details {
        margin: 6px 0 !important;
        padding: 10px !important;
        font-size: 0.8rem !important;
    }
    
    .dtr-details li {
        padding: 5px 0 !important;
    }
    
    .dtr-title {
        font-size: 0.75rem !important;
        margin-bottom: 3px !important;
    }
    
    .dtr-data {
        font-size: 0.8rem !important;
    }
    
    .dtr-data .badge {
        font-size: 0.7rem !important;
        padding: 0.2em 0.4em !important;
    }
}

/* ==================== 表格整体优化 ==================== */

/* 表格行高优化 - 与dashboard保持一致 */
.data-table tbody tr {
    min-height: 48px; /* 稍微增加行高，提升舒适度 */
}

.data-table tbody td {
    padding: 10px 8px !important; /* 增加垂直内边距 */
    vertical-align: middle !important;
    line-height: 1.4 !important;
}

/* 表头优化 */
.data-table thead th {
    padding: 12px 8px !important;
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-weight: 600 !important;
    color: #495057 !important;
}

/* 响应式控制按钮优化 */
.data-table td.dtr-control,
.data-table th.dtr-control {
    width: 35px !important;
    min-width: 35px !important;
    max-width: 35px !important;
    text-align: center !important;
    padding: 8px 4px !important;
}

/* 展开/折叠按钮样式 - 更舒适的大小 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    content: '+';
    background-color: #0d6efd;
    color: white;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.25);
    transition: all 0.2s ease;
}

/* 展开状态按钮 */
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
    content: '−';
    background-color: #dc3545;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.25);
}

/* 悬停效果 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:hover:before {
    transform: scale(1.1);
    box-shadow: 0 3px 6px rgba(13, 110, 253, 0.35);
}

/* 表格整体容器 */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    background-color: #fff;
    border: 1px solid #e9ecef;
}

/* 奇偶行样式 */
.data-table tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.data-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transition: background-color 0.2s ease;
}

/* ==================== 舒适的间距系统 ==================== */

/* 页面内容间距 */
.main-content {
    padding: 20px;
}

/* 表格上下间距 */
.table-responsive {
    margin-bottom: 2rem;
}

/* 控制区间距 */
.table-controls-row {
    margin-bottom: 1.5rem;
}

/* 分页容器间距 */
.pagination-container {
    margin-top: 1.5rem;
    margin-bottom: 2rem;
}