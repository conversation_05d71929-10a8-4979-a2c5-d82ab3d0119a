/**
 * 应用主入口文件
 * 整合所有控制器的初始化和配置
 */

// 应用主对象
const DataQueryApp = {
    // 配置
    config: {
        // 全局配置
        debug: true,
        version: '1.0.0',
        
        // 控制器配置
        controllers: {
            style: true,       // 是否启用样式控制器
            loading: true,     // 是否启用加载状态控制器
            dataQuery: true,   // 是否启用数据查询控制器
            responsive: true   // 是否启用响应式控制器
        }
    },
    
    // 控制器实例
    controllers: {},
    
    // 初始化
    init: function() {
        console.log(`初始化数据查询应用 v${this.config.version}`);
        
        // 注册并初始化控制器
        this.registerControllers();
        
        // 注册全局事件
        this.registerEvents();
        
        // 附加全局方法
        this.attachGlobalMethods();
        
        // 初始化完成后通知
        document.dispatchEvent(new CustomEvent('app:ready'));
        console.log('应用初始化完成');
    },
    
    // 注册控制器
    registerControllers: function() {
        // 样式控制器
        if (this.config.controllers.style && window.StyleController) {
            this.controllers.style = window.StyleController;
            this.log('注册样式控制器');
        }
        
        // 加载状态控制器
        if (this.config.controllers.loading && window.LoadingController) {
            this.controllers.loading = window.LoadingController;
            this.log('注册加载状态控制器');
        }
        
        // 数据查询控制器
        if (this.config.controllers.dataQuery && window.DataQueryController) {
            this.controllers.dataQuery = window.DataQueryController;
            this.log('注册数据查询控制器');
        }
        
        // 初始化已注册的所有控制器
        Object.values(this.controllers).forEach(controller => {
            if (typeof controller.init === 'function') {
                controller.init();
            }
        });
    },
    
    // 注册全局事件
    registerEvents: function() {
        // 错误处理
        window.addEventListener('error', this.handleGlobalError.bind(this));
        window.addEventListener('unhandledrejection', this.handlePromiseError.bind(this));
        
        // 阻止表单默认提交
        document.addEventListener('submit', function(e) {
            // 如果没有指定action属性，阻止默认提交
            if (!e.target.action) {
                e.preventDefault();
            }
        });
    },
    
    // 附加全局方法
    attachGlobalMethods: function() {
        // 导出数据功能 - 保持与旧代码兼容
        window.exportData = this.exportData.bind(this);
        
        // 查看客户汇总数据功能 - 保持与旧代码兼容
        window.viewCustomerSummary = this.viewCustomerSummary.bind(this);
    },
    
    // 全局错误处理
    handleGlobalError: function(event) {
        this.error('全局错误', event.error || event.message);
        
        // 使用加载控制器显示错误消息
        if (this.controllers.loading) {
            this.controllers.loading.showError(`发生错误: ${event.message}`);
        }
    },
    
    // Promise错误处理
    handlePromiseError: function(event) {
        this.error('Promise错误', event.reason);
        
        // 使用加载控制器显示错误消息
        if (this.controllers.loading) {
            this.controllers.loading.showError(`异步操作失败: ${event.reason.message || '未知错误'}`);
        }
    },
    
    // 导出数据功能
    exportData: function(format) {
        this.log(`导出数据: ${format}`);
        
        // 获取当前活动选项卡
        const activeTab = document.querySelector('.tab-pane.active');
        if (!activeTab) {
            alert('无法识别当前数据视图');
            return;
        }
        
        const tabId = activeTab.id;
        let table = activeTab.querySelector('.data-table');
        
        if (!table) {
            alert('找不到数据表格');
            return;
        }
        
        // 获取搜索查询参数
        let searchQuery = '';
        if ($.fn.DataTable.isDataTable(table)) {
            const dataTable = $(table).DataTable();
            searchQuery = dataTable.search();
        }
        
        // 显示加载状态
        if (this.controllers.loading) {
            this.controllers.loading.show(`正在导出${format}文件...`);
        }
        
        try {
            // 调用统一的导出管理模块
            ExportManager.exportData(format, {
                tabId: tabId,
                table: table,
                searchQuery: searchQuery
            });
            
            // 导出成功后隐藏加载状态
            setTimeout(() => {
                if (this.controllers.loading) {
                    this.controllers.loading.hide();
                }
            }, 1000);
        } catch (error) {
            this.error('导出错误:', error);
            
            // 隐藏加载状态并显示错误消息
            if (this.controllers.loading) {
                this.controllers.loading.showError(`导出失败: ${error.message}`);
            } else {
                alert(`导出失败: ${error.message}`);
            }
        }
    },
    
    // 从HTML内容中提取纯文本
    stripHtml: function(html) {
        // 如果不是字符串，直接返回
        if (typeof html !== 'string') {
            return html;
        }
        
        // 创建临时DOM元素
        const tempElement = document.createElement('div');
        tempElement.innerHTML = html;
        
        // 获取元素中的纯文本
        return tempElement.textContent || tempElement.innerText || html;
    },
    
    // 将数据发送到服务器进行导出
    exportToServer: function(format, data, searchQuery = '') {
        // 显示加载状态
        if (this.controllers.loading) {
            this.controllers.loading.show(`正在导出${format}文件...`);
        }
        
        // 使用ExportManager中的方法发送导出请求
        ExportManager.sendExportRequest(format, data, searchQuery)
            .then(() => {
                // 隐藏加载状态
                if (this.controllers.loading) {
                    this.controllers.loading.hide();
                }
            })
            .catch(error => {
                this.error('导出错误:', error);
                
                // 隐藏加载状态并显示错误消息
                if (this.controllers.loading) {
                    this.controllers.loading.showError(`导出失败: ${error.message}`);
                } else {
                    alert(`导出失败: ${error.message}`);
                }
            });
    },
    
    // 查看客户汇总数据
    viewCustomerSummary: function(customerName) {
        if (!customerName) {
            alert('客户姓名不能为空');
            return;
        }
        
        // 显示加载指示器
        if (this.controllers.loading) {
            this.controllers.loading.show(`加载${customerName}的汇总数据...`);
        }
        
        // 检查缓存中是否有数据
        const params = { customer_name: customerName };
        const cachedData = ApiDataManager.getData('customer_summary', params);
        
        if (cachedData) {
            // 使用缓存的数据
            this.log('使用缓存的客户汇总数据');
            
            // 存储数据到localStorage以便在页面跳转后使用
            localStorage.setItem('customerSummary', JSON.stringify(cachedData));
            
            // 跳转到客户汇总页面
            window.location.href = `/customer_summary/${encodeURIComponent(customerName)}`;
            return;
        }
        
        // 发送API请求获取客户汇总数据
        fetch(`/api/customer_summary?customer_name=${encodeURIComponent(customerName)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('获取汇总数据失败，请重试');
                }
                return response.json();
            })
            .then(data => {
                // 缓存API响应数据
                ApiDataManager.storeData('customer_summary', params, data);
                
                // 存储数据到localStorage以便在页面跳转后使用
                localStorage.setItem('customerSummary', JSON.stringify(data));
                
                // 跳转到客户汇总页面
                window.location.href = `/customer_summary/${encodeURIComponent(customerName)}`;
            })
            .catch(error => {
                this.error('客户汇总数据错误:', error);
                
                // 显示错误消息
                if (this.controllers.loading) {
                    this.controllers.loading.showError(`获取汇总数据失败: ${error.message}`);
                } else {
                    alert(`获取汇总数据失败: ${error.message}`);
                }
            });
    },
    
    // 日志输出
    log: function(...args) {
        if (this.config.debug) {
            console.log('[DataQueryApp]', ...args);
        }
    },
    
    // 错误日志输出
    error: function(...args) {
        console.error('[DataQueryApp]', ...args);
    }
};

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    DataQueryApp.init();
}); 